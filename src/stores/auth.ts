import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { 
  signInWithCustomToken, 
  signOut, 
  onAuthStateChanged,
  type User 
} from 'firebase/auth'
import { doc, getDoc, setDoc } from 'firebase/firestore'
import { auth, db } from '@/firebase/config'

export interface UserProfile {
  uid: string
  email: string
  displayName: string
  department?: string
  role: 'user' | 'admin'
  createdAt: Date
  lastLogin: Date
}

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const userProfile = ref<UserProfile | null>(null)
  const loading = ref(false)
  const initialized = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isAuthenticated = computed(() => !!user.value)
  const isAdmin = computed(() => userProfile.value?.role === 'admin')
  const currentUser = computed(() => user.value)
  const currentUserProfile = computed(() => userProfile.value)

  // Actions
  const initializeAuth = async () => {
    return new Promise<void>((resolve) => {
      const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
        if (firebaseUser) {
          user.value = firebaseUser
          await loadUserProfile(firebaseUser.uid)
        } else {
          user.value = null
          userProfile.value = null
        }
        initialized.value = true
        unsubscribe()
        resolve()
      })
    })
  }

  const loginWithExternalAPI = async (credentials: { username: string; password: string }) => {
    loading.value = true
    error.value = null

    try {
      // Call external API for authentication
      const response = await fetch(import.meta.env.VITE_EXTERNAL_AUTH_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      })

      if (!response.ok) {
        throw new Error('Invalid credentials')
      }

      const data = await response.json()
      
      // Assuming the external API returns a custom token for Firebase
      if (data.customToken) {
        const userCredential = await signInWithCustomToken(auth, data.customToken)
        user.value = userCredential.user
        
        // Create or update user profile
        await createOrUpdateUserProfile({
          uid: userCredential.user.uid,
          email: data.email || userCredential.user.email || '',
          displayName: data.displayName || userCredential.user.displayName || '',
          department: data.department,
          role: data.role || 'user',
          createdAt: new Date(),
          lastLogin: new Date()
        })
        
        await loadUserProfile(userCredential.user.uid)
      } else {
        throw new Error('Authentication failed')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Login failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    loading.value = true
    try {
      await signOut(auth)
      user.value = null
      userProfile.value = null
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Logout failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  const loadUserProfile = async (uid: string) => {
    try {
      const userDoc = await getDoc(doc(db, 'users', uid))
      if (userDoc.exists()) {
        const data = userDoc.data()
        userProfile.value = {
          uid,
          email: data.email,
          displayName: data.displayName,
          department: data.department,
          role: data.role || 'user',
          createdAt: data.createdAt?.toDate() || new Date(),
          lastLogin: data.lastLogin?.toDate() || new Date()
        }
      }
    } catch (err) {
      console.error('Error loading user profile:', err)
    }
  }

  const createOrUpdateUserProfile = async (profile: UserProfile) => {
    try {
      await setDoc(doc(db, 'users', profile.uid), {
        email: profile.email,
        displayName: profile.displayName,
        department: profile.department,
        role: profile.role,
        createdAt: profile.createdAt,
        lastLogin: profile.lastLogin
      }, { merge: true })
    } catch (err) {
      console.error('Error creating/updating user profile:', err)
      throw err
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    user: readonly(user),
    userProfile: readonly(userProfile),
    loading: readonly(loading),
    initialized: readonly(initialized),
    error: readonly(error),
    
    // Getters
    isAuthenticated,
    isAdmin,
    currentUser,
    currentUserProfile,
    
    // Actions
    initializeAuth,
    loginWithExternalAPI,
    logout,
    loadUserProfile,
    createOrUpdateUserProfile,
    clearError
  }
})
