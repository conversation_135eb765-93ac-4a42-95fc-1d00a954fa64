<template>
  <div class="login-container">
    <div class="login-card">
      <h1>Login</h1>
      <p>Employee authentication placeholder</p>
      <Button label="Login" @click="handleLogin" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const handleLogin = async () => {
  // Placeholder login logic
  router.push('/')
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: var(--surface-ground);
}

.login-card {
  padding: 2rem;
  background: var(--surface-card);
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
}
</style>
