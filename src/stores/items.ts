import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy,
  onSnapshot,
  type Unsubscribe
} from 'firebase/firestore'
import { db } from '@/firebase/config'

export interface RentalItem {
  id: string
  name: string
  description: string
  category: string
  totalStock: number
  availableStock: number
  imageUrl?: string
  specifications?: Record<string, any>
  pricePerDay?: number
  location: string
  condition: 'excellent' | 'good' | 'fair' | 'poor'
  createdBy: string
  createdAt: Date
  updatedAt: Date
  isActive: boolean
}

export interface ItemCategory {
  id: string
  name: string
  description: string
  icon?: string
  color?: string
}

export const useItemsStore = defineStore('items', () => {
  // State
  const items = ref<RentalItem[]>([])
  const categories = ref<ItemCategory[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const selectedCategory = ref<string>('')
  const searchQuery = ref<string>('')
  
  // Real-time listeners
  let itemsUnsubscribe: Unsubscribe | null = null
  let categoriesUnsubscribe: Unsubscribe | null = null

  // Getters
  const filteredItems = computed(() => {
    let filtered = items.value.filter(item => item.isActive)
    
    if (selectedCategory.value) {
      filtered = filtered.filter(item => item.category === selectedCategory.value)
    }
    
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(item => 
        item.name.toLowerCase().includes(query) ||
        item.description.toLowerCase().includes(query)
      )
    }
    
    return filtered
  })

  const availableItems = computed(() => 
    filteredItems.value.filter(item => item.availableStock > 0)
  )

  const itemsByCategory = computed(() => {
    const grouped: Record<string, RentalItem[]> = {}
    filteredItems.value.forEach(item => {
      if (!grouped[item.category]) {
        grouped[item.category] = []
      }
      grouped[item.category].push(item)
    })
    return grouped
  })

  // Actions
  const initializeRealTimeListeners = () => {
    // Listen to items collection
    const itemsQuery = query(
      collection(db, 'items'),
      orderBy('name')
    )
    
    itemsUnsubscribe = onSnapshot(itemsQuery, (snapshot) => {
      items.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      })) as RentalItem[]
    }, (err) => {
      console.error('Error listening to items:', err)
      error.value = 'Failed to load items'
    })

    // Listen to categories collection
    const categoriesQuery = query(
      collection(db, 'categories'),
      orderBy('name')
    )
    
    categoriesUnsubscribe = onSnapshot(categoriesQuery, (snapshot) => {
      categories.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ItemCategory[]
    }, (err) => {
      console.error('Error listening to categories:', err)
    })
  }

  const stopRealTimeListeners = () => {
    if (itemsUnsubscribe) {
      itemsUnsubscribe()
      itemsUnsubscribe = null
    }
    if (categoriesUnsubscribe) {
      categoriesUnsubscribe()
      categoriesUnsubscribe = null
    }
  }

  const fetchItems = async () => {
    loading.value = true
    error.value = null
    
    try {
      const q = query(
        collection(db, 'items'),
        where('isActive', '==', true),
        orderBy('name')
      )
      
      const snapshot = await getDocs(q)
      items.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      })) as RentalItem[]
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch items'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchItemById = async (id: string): Promise<RentalItem | null> => {
    try {
      const docRef = doc(db, 'items', id)
      const docSnap = await getDoc(docRef)
      
      if (docSnap.exists()) {
        const data = docSnap.data()
        return {
          id: docSnap.id,
          ...data,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date()
        } as RentalItem
      }
      return null
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch item'
      throw err
    }
  }

  const addItem = async (itemData: Omit<RentalItem, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    error.value = null
    
    try {
      const now = new Date()
      const docRef = await addDoc(collection(db, 'items'), {
        ...itemData,
        createdAt: now,
        updatedAt: now
      })
      
      return docRef.id
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to add item'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateItem = async (id: string, updates: Partial<RentalItem>) => {
    loading.value = true
    error.value = null
    
    try {
      const docRef = doc(db, 'items', id)
      await updateDoc(docRef, {
        ...updates,
        updatedAt: new Date()
      })
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update item'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateItemStock = async (id: string, newAvailableStock: number) => {
    await updateItem(id, { availableStock: newAvailableStock })
  }

  const deleteItem = async (id: string) => {
    loading.value = true
    error.value = null
    
    try {
      // Soft delete by setting isActive to false
      await updateItem(id, { isActive: false })
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to delete item'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchCategories = async () => {
    try {
      const snapshot = await getDocs(collection(db, 'categories'))
      categories.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ItemCategory[]
    } catch (err) {
      console.error('Error fetching categories:', err)
    }
  }

  const setSelectedCategory = (categoryId: string) => {
    selectedCategory.value = categoryId
  }

  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }

  const clearFilters = () => {
    selectedCategory.value = ''
    searchQuery.value = ''
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    items: readonly(items),
    categories: readonly(categories),
    loading: readonly(loading),
    error: readonly(error),
    selectedCategory: readonly(selectedCategory),
    searchQuery: readonly(searchQuery),
    
    // Getters
    filteredItems,
    availableItems,
    itemsByCategory,
    
    // Actions
    initializeRealTimeListeners,
    stopRealTimeListeners,
    fetchItems,
    fetchItemById,
    addItem,
    updateItem,
    updateItemStock,
    deleteItem,
    fetchCategories,
    setSelectedCategory,
    setSearchQuery,
    clearFilters,
    clearError
  }
})
