<template>
  <div class="admin-container">
    <div class="page-header">
      <h1>Admin Dashboard</h1>
      <p class="page-description">
        Manage items, view all rentals, and handle administrative tasks.
      </p>
    </div>

    <!-- Admin Tabs -->
    <div class="admin-tabs">
      <Button
        v-for="tab in adminTabs"
        :key="tab.value"
        :label="tab.label"
        :icon="tab.icon"
        :class="{ 'p-button-outlined': selectedTab !== tab.value }"
        @click="setSelectedTab(tab.value)"
      />
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Items Management -->
      <div v-if="selectedTab === 'items'" class="tab-panel">
        <div class="panel-header">
          <h2>Items Management</h2>
          <Button
            label="Add New Item Group"
            icon="pi pi-plus"
            @click="showAddItemDialog = true"
          />
        </div>
        
        <DataTable 
          :value="allItems" 
          :loading="loadingItems"
          paginator 
          :rows="20"
          class="admin-table"
          responsive-layout="scroll"
        >
          <Column field="name" header="Item Name" sortable />
          <Column field="category" header="Category" sortable>
            <template #body="{ data }">
              <Badge :value="formatCategory(data.category)" />
            </template>
          </Column>
          <Column field="type" header="Type" sortable />
          <Column header="Total Items">
            <template #body="{ data }">
              {{ data.items?.length || 0 }}
            </template>
          </Column>
          <Column header="Available">
            <template #body="{ data }">
              {{ getAvailableCount(data) }}
            </template>
          </Column>
          <Column header="Actions">
            <template #body="{ data }">
              <Button
                icon="pi pi-pencil"
                class="p-button-text p-button-sm"
                @click="editItem(data)"
                v-tooltip="'Edit item'"
              />
              <Button
                icon="pi pi-eye"
                class="p-button-text p-button-sm"
                @click="viewItemDetails(data)"
                v-tooltip="'View details'"
              />
            </template>
          </Column>
        </DataTable>
      </div>

      <!-- All Rentals -->
      <div v-if="selectedTab === 'rentals'" class="tab-panel">
        <div class="panel-header">
          <h2>All Rentals</h2>
          <div class="rental-filters">
            <Dropdown
              v-model="selectedRentalStatus"
              :options="rentalStatusOptions"
              option-label="label"
              option-value="value"
              placeholder="Filter by status"
              class="filter-dropdown"
            />
            <Calendar
              v-model="selectedDateRange"
              selection-mode="range"
              :manual-input="false"
              placeholder="Filter by date range"
              class="filter-calendar"
            />
          </div>
        </div>
        
        <DataTable 
          :value="filteredRentals" 
          :loading="loadingRentals"
          paginator 
          :rows="15"
          class="admin-table"
          responsive-layout="scroll"
        >
          <Column field="id" header="Episode ID" sortable>
            <template #body="{ data }">
              <code class="episode-id">{{ data.id }}</code>
            </template>
          </Column>
          <Column field="user_employee_number" header="Employee" sortable />
          <Column field="status" header="Status" sortable>
            <template #body="{ data }">
              <Badge 
                :value="data.status" 
                :severity="getStatusSeverity(data.status)"
              />
            </template>
          </Column>
          <Column header="Items">
            <template #body="{ data }">
              {{ data.items?.length || 0 }} item(s)
            </template>
          </Column>
          <Column header="Dates">
            <template #body="{ data }">
              {{ formatDateRange(data.dates) }}
            </template>
          </Column>
          <Column field="created_at" header="Created" sortable>
            <template #body="{ data }">
              {{ formatDate(data.created_at) }}
            </template>
          </Column>
          <Column header="Actions">
            <template #body="{ data }">
              <Button
                icon="pi pi-eye"
                class="p-button-text p-button-sm"
                @click="viewRentalDetails(data)"
                v-tooltip="'View details'"
              />
            </template>
          </Column>
        </DataTable>
      </div>

      <!-- System Stats -->
      <div v-if="selectedTab === 'stats'" class="tab-panel">
        <div class="panel-header">
          <h2>System Statistics</h2>
        </div>
        
        <div class="stats-grid">
          <Card class="stat-card">
            <template #title>Total Items</template>
            <template #content>
              <div class="stat-value">{{ totalItemsCount }}</div>
              <div class="stat-label">Item groups in system</div>
            </template>
          </Card>
          
          <Card class="stat-card">
            <template #title>Active Rentals</template>
            <template #content>
              <div class="stat-value">{{ activeRentalsCount }}</div>
              <div class="stat-label">Currently rented episodes</div>
            </template>
          </Card>
          
          <Card class="stat-card">
            <template #title>Total Users</template>
            <template #content>
              <div class="stat-value">{{ totalUsersCount }}</div>
              <div class="stat-label">Registered users</div>
            </template>
          </Card>
          
          <Card class="stat-card">
            <template #title>This Month</template>
            <template #content>
              <div class="stat-value">{{ thisMonthRentalsCount }}</div>
              <div class="stat-label">Rentals this month</div>
            </template>
          </Card>
        </div>
      </div>

      <!-- User Management -->
      <div v-if="selectedTab === 'users'" class="tab-panel">
        <div class="panel-header">
          <h2>User Management</h2>
        </div>
        
        <DataTable 
          :value="allUsers" 
          :loading="loadingUsers"
          paginator 
          :rows="20"
          class="admin-table"
          responsive-layout="scroll"
        >
          <Column field="employee_number" header="Employee Number" sortable />
          <Column field="name" header="Name" sortable />
          <Column field="email" header="Email" sortable />
          <Column field="department" header="Department" sortable />
          <Column header="Total Rentals">
            <template #body="{ data }">
              {{ getUserRentalCount(data.employee_number) }}
            </template>
          </Column>
          <Column field="created_at" header="Joined" sortable>
            <template #body="{ data }">
              {{ formatDate(data.created_at) }}
            </template>
          </Column>
          <Column header="Actions">
            <template #body="{ data }">
              <Button
                icon="pi pi-eye"
                class="p-button-text p-button-sm"
                @click="viewUserDetails(data)"
                v-tooltip="'View user details'"
              />
            </template>
          </Column>
        </DataTable>
      </div>
    </div>

    <!-- Dialogs -->
    <Dialog
      v-model:visible="showAddItemDialog"
      header="Add New Item Group"
      modal
      style="width: 50vw; min-width: 400px;"
    >
      <p>Item creation functionality would be implemented here.</p>
      <div class="dialog-actions">
        <Button label="Cancel" class="p-button-outlined" @click="showAddItemDialog = false" />
        <Button label="Create" @click="showAddItemDialog = false" />
      </div>
    </Dialog>

    <Toast />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import { useItemsStore } from '@/stores/items'
import { useEpisodesStore } from '@/stores/episodes'
import { useAuthStore } from '@/stores/auth'
import { format } from 'date-fns'

// Stores
const itemsStore = useItemsStore()
const episodesStore = useEpisodesStore()
const authStore = useAuthStore()
const toast = useToast()

// Reactive state
const selectedTab = ref<'items' | 'rentals' | 'stats' | 'users'>('items')
const loadingItems = ref(false)
const loadingRentals = ref(false)
const loadingUsers = ref(false)
const showAddItemDialog = ref(false)
const selectedRentalStatus = ref<string | null>(null)
const selectedDateRange = ref<Date[] | null>(null)

// Mock data (in real implementation, these would come from stores)
const allItems = computed(() => itemsStore.itemGroups)
const allUsers = ref<any[]>([]) // Would be populated from a users store
const allRentals = ref<any[]>([]) // Would be populated from episodes store

// Tab configuration
const adminTabs = [
  { label: 'Items', value: 'items' as const, icon: 'pi pi-box' },
  { label: 'Rentals', value: 'rentals' as const, icon: 'pi pi-calendar' },
  { label: 'Statistics', value: 'stats' as const, icon: 'pi pi-chart-bar' },
  { label: 'Users', value: 'users' as const, icon: 'pi pi-users' }
]

const rentalStatusOptions = [
  { label: 'All Statuses', value: null },
  { label: 'Scheduled', value: 'scheduled' },
  { label: 'Rented', value: 'rented' },
  { label: 'Returned', value: 'returned' },
  { label: 'Cancelled', value: 'cancelled' }
]

// Computed properties
const filteredRentals = computed(() => {
  let rentals = allRentals.value
  
  if (selectedRentalStatus.value) {
    rentals = rentals.filter(rental => rental.status === selectedRentalStatus.value)
  }
  
  // Date range filtering would be implemented here
  
  return rentals
})

const totalItemsCount = computed(() => allItems.value.length)
const activeRentalsCount = computed(() => 
  allRentals.value.filter(rental => rental.status === 'rented').length
)
const totalUsersCount = computed(() => allUsers.value.length)
const thisMonthRentalsCount = computed(() => {
  const now = new Date()
  const thisMonth = now.getMonth()
  const thisYear = now.getFullYear()
  
  return allRentals.value.filter(rental => {
    const createdDate = new Date(rental.created_at)
    return createdDate.getMonth() === thisMonth && createdDate.getFullYear() === thisYear
  }).length
})

// Methods
const setSelectedTab = (tab: 'items' | 'rentals' | 'stats' | 'users') => {
  selectedTab.value = tab
}

const formatCategory = (category: string) => {
  switch (category) {
    case 'resus_trainings':
      return 'Resuscitating Training'
    case 'venues':
      return 'Venue'
    case 'audio_visuals':
      return 'Audio Visual'
    default:
      return category
  }
}

const getAvailableCount = (itemGroup: any) => {
  // This would use the actual availability checking logic
  return itemGroup.items?.filter((item: any) => !item.suspended).length || 0
}

const getStatusSeverity = (status: string) => {
  switch (status) {
    case 'scheduled':
      return 'info'
    case 'rented':
      return 'warning'
    case 'returned':
      return 'success'
    case 'cancelled':
      return 'danger'
    default:
      return 'info'
  }
}

const formatDate = (date: Date | string) => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return format(dateObj, 'MMM dd, yyyy')
}

const formatDateRange = (dates: string[]) => {
  if (!dates || dates.length === 0) return 'No dates'
  if (dates.length === 1) return formatDate(dates[0])
  
  const sortedDates = [...dates].sort()
  return `${formatDate(sortedDates[0])} - ${formatDate(sortedDates[sortedDates.length - 1])}`
}

const getUserRentalCount = (employeeNumber: string) => {
  return allRentals.value.filter(rental => rental.user_employee_number === employeeNumber).length
}

const editItem = (item: any) => {
  toast.add({
    severity: 'info',
    summary: 'Edit Item',
    detail: `Edit functionality for ${item.name} would be implemented here`,
    life: 3000
  })
}

const viewItemDetails = (item: any) => {
  toast.add({
    severity: 'info',
    summary: 'Item Details',
    detail: `Detailed view for ${item.name} would be implemented here`,
    life: 3000
  })
}

const viewRentalDetails = (rental: any) => {
  toast.add({
    severity: 'info',
    summary: 'Rental Details',
    detail: `Detailed view for episode ${rental.id} would be implemented here`,
    life: 3000
  })
}

const viewUserDetails = (user: any) => {
  toast.add({
    severity: 'info',
    summary: 'User Details',
    detail: `Detailed view for ${user.name} would be implemented here`,
    life: 3000
  })
}

// Lifecycle
onMounted(async () => {
  // Load initial data
  loadingItems.value = true
  try {
    await itemsStore.initializeRealTimeListeners()
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Loading Error',
      detail: 'Failed to load items data',
      life: 5000
    })
  } finally {
    loadingItems.value = false
  }
})
</script>

<style scoped>
.admin-container {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 2rem;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 2.5rem;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
}

.admin-tabs {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--surface-ground);
  border-radius: 8px;
}

.tab-content {
  background: var(--surface-card);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tab-panel {
  padding: 1.5rem;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.panel-header h2 {
  margin: 0;
  color: var(--text-color);
  font-size: 1.5rem;
  font-weight: 600;
}

.rental-filters {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.filter-dropdown,
.filter-calendar {
  min-width: 200px;
}

.admin-table {
  margin-top: 1rem;
}

.episode-id {
  font-family: 'Courier New', monospace;
  background: var(--surface-100);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  text-align: center;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.dialog-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

@media (max-width: 768px) {
  .admin-container {
    padding: 1rem;
  }
  
  .admin-tabs {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .panel-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .rental-filters {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .filter-dropdown,
  .filter-calendar {
    min-width: unset;
    width: 100%;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
