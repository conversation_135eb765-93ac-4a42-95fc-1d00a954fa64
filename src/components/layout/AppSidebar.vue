<template>
  <aside class="app-sidebar" :class="{ 'sidebar-collapsed': collapsed }">
    <div class="sidebar-content">
      <!-- Toggle button -->
      <Button 
        icon="pi pi-bars"
        severity="secondary"
        text
        rounded
        class="sidebar-toggle"
        @click="toggleSidebar"
      />
      
      <!-- Navigation menu -->
      <nav class="sidebar-nav">
        <ul class="nav-list">
          <li v-for="item in navigationItems" :key="item.name" class="nav-item">
            <router-link 
              :to="item.to" 
              class="nav-link"
              :class="{ 'nav-link-active': isActiveRoute(item.to) }"
            >
              <i :class="item.icon" class="nav-icon"></i>
              <span v-if="!collapsed" class="nav-label">{{ item.label }}</span>
              <Badge 
                v-if="item.badge && !collapsed" 
                :value="item.badge" 
                severity="danger"
                class="nav-badge"
              />
            </router-link>
          </li>
        </ul>
      </nav>
      
      <!-- Quick stats (when expanded) -->
      <div v-if="!collapsed" class="sidebar-stats">
        <Card class="stats-card">
          <template #content>
            <div class="stats-content">
              <h4 class="stats-title">Quick Stats</h4>
              <div class="stat-item">
                <span class="stat-label">Available Items</span>
                <span class="stat-value">{{ availableItemsCount }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">My Active Rentals</span>
                <span class="stat-value">{{ userActiveRentalsCount }}</span>
              </div>
              <div v-if="authStore.isAdmin" class="stat-item">
                <span class="stat-label">Pending Approvals</span>
                <span class="stat-value">{{ pendingApprovalsCount }}</span>
              </div>
            </div>
          </template>
        </Card>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useItemsStore } from '@/stores/items'
import { useRentalsStore } from '@/stores/rentals'

const route = useRoute()
const authStore = useAuthStore()
const itemsStore = useItemsStore()
const rentalsStore = useRentalsStore()

const collapsed = ref(false)

const navigationItems = computed(() => {
  const baseItems = [
    {
      name: 'home',
      label: 'Dashboard',
      icon: 'pi pi-home',
      to: '/'
    },
    {
      name: 'items',
      label: 'Browse Items',
      icon: 'pi pi-box',
      to: '/items'
    },
    {
      name: 'booking',
      label: 'New Booking',
      icon: 'pi pi-plus-circle',
      to: '/booking'
    },
    {
      name: 'my-rentals',
      label: 'My Rentals',
      icon: 'pi pi-calendar',
      to: '/my-rentals',
      badge: rentalsStore.userActiveRentals.length || undefined
    },
    {
      name: 'profile',
      label: 'Profile',
      icon: 'pi pi-user',
      to: '/profile'
    }
  ]

  // Add admin items if user is admin
  if (authStore.isAdmin) {
    baseItems.push({
      name: 'admin',
      label: 'Admin Panel',
      icon: 'pi pi-cog',
      to: '/admin',
      badge: rentalsStore.pendingRentals.length || undefined
    })
  }

  return baseItems
})

const availableItemsCount = computed(() => 
  itemsStore.availableItems.length
)

const userActiveRentalsCount = computed(() => 
  rentalsStore.userActiveRentals.length
)

const pendingApprovalsCount = computed(() => 
  rentalsStore.pendingRentals.length
)

const toggleSidebar = () => {
  collapsed.value = !collapsed.value
}

const isActiveRoute = (to: string) => {
  if (to === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(to)
}
</script>

<style scoped>
.app-sidebar {
  width: 280px;
  background: var(--p-surface-card);
  border-right: 1px solid var(--p-surface-border);
  transition: width 0.3s ease;
  display: flex;
  flex-direction: column;
}

.sidebar-collapsed {
  width: 80px;
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1rem;
}

.sidebar-toggle {
  align-self: flex-end;
  margin-bottom: 1rem;
}

.sidebar-nav {
  flex: 1;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 0.5rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: var(--p-text-color);
  border-radius: var(--p-border-radius);
  transition: all 0.2s ease;
  position: relative;
}

.nav-link:hover {
  background: var(--p-surface-hover);
  color: var(--p-primary-color);
}

.nav-link-active {
  background: var(--p-primary-50);
  color: var(--p-primary-color);
  font-weight: 600;
}

.nav-icon {
  font-size: 1.25rem;
  margin-right: 0.75rem;
  min-width: 1.25rem;
}

.nav-label {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
}

.nav-badge {
  margin-left: auto;
}

.sidebar-stats {
  margin-top: auto;
  padding-top: 1rem;
}

.stats-card {
  background: var(--p-surface-50);
  border: 1px solid var(--p-surface-200);
}

.stats-content {
  padding: 0;
}

.stats-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--p-text-color-secondary);
  margin: 0 0 1rem 0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--p-text-color-secondary);
}

.stat-value {
  font-weight: 600;
  color: var(--p-primary-color);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .app-sidebar {
    position: fixed;
    left: -280px;
    top: 0;
    height: 100vh;
    z-index: 1001;
    transition: left 0.3s ease;
  }
  
  .app-sidebar.sidebar-open {
    left: 0;
  }
  
  .sidebar-collapsed {
    left: -80px;
  }
  
  .sidebar-collapsed.sidebar-open {
    left: 0;
  }
}

/* Tablet responsive */
@media (max-width: 1024px) and (min-width: 769px) {
  .app-sidebar {
    width: 240px;
  }
  
  .sidebar-collapsed {
    width: 70px;
  }
}
</style>
