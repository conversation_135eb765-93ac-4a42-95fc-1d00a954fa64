import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  query, 
  where, 
  orderBy,
  onSnapshot,
  runTransaction,
  type Unsubscribe
} from 'firebase/firestore'
import { db, isDemoMode } from '@/firebase/config'

// User episode structure
export interface UserEpisode {
  id: string
  employee_number: string
  dates: string[] // Array of dates in YYYY-MM-DD format
  items: Array<{
    item_group_id: string
    item_group_name: string
    item_group_type: 'resus_trainings' | 'venues' | 'audio_visuals'
    individual_item_id: string
    quantity: number
  }>
  status: 'cancelled' | 'scheduled' | 'rented' | 'returned'
  created_at: Date
  updated_at: Date
}

// Cart item structure
export interface CartItem {
  item_group_id: string
  item_group_name: string
  item_group_type: 'resus_trainings' | 'venues' | 'audio_visuals'
  quantity: number
  max_quantity: number
  contraindications: Array<{
    item_group_name: string
    reason: string
  }>
}

export const useEpisodesStore = defineStore('episodes', () => {
  // State
  const userEpisodes = ref<UserEpisode[]>([])
  const allEpisodes = ref<UserEpisode[]>([]) // For admin view
  const cart = ref<CartItem[]>([])
  const selectedDates = ref<string[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Real-time listeners
  let userEpisodesUnsubscribe: Unsubscribe | null = null
  let allEpisodesUnsubscribe: Unsubscribe | null = null

  // Getters
  const episodesByStatus = computed(() => {
    const grouped: Record<string, UserEpisode[]> = {
      scheduled: [],
      rented: [],
      returned: [],
      cancelled: []
    }
    
    userEpisodes.value.forEach(episode => {
      if (grouped[episode.status]) {
        grouped[episode.status].push(episode)
      }
    })
    
    return grouped
  })

  const cartTotal = computed(() => {
    return cart.value.reduce((total, item) => total + item.quantity, 0)
  })

  const cartItemGroups = computed(() => {
    return cart.value.map(item => item.item_group_name)
  })

  // Actions
  const initializeUserEpisodesListener = (employeeNumber: string) => {
    if (isDemoMode) {
      // Demo mode - create some sample episodes for demonstration
      const now = new Date()
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)

      userEpisodes.value = [
        {
          id: 'demo_episode_1',
          employee_number: employeeNumber,
          dates: [tomorrow.toISOString().split('T')[0]],
          items: [
            {
              item_group_id: 'demo_group_1',
              item_group_name: 'Demo Training Equipment',
              item_group_type: 'resus_trainings',
              individual_item_id: 'demo_item_1',
              quantity: 1
            }
          ],
          status: 'scheduled',
          created_at: yesterday,
          updated_at: yesterday
        },
        {
          id: 'demo_episode_2',
          employee_number: employeeNumber,
          dates: [yesterday.toISOString().split('T')[0]],
          items: [
            {
              item_group_id: 'demo_group_2',
              item_group_name: 'Demo Venue',
              item_group_type: 'venues',
              individual_item_id: 'demo_item_2',
              quantity: 1
            }
          ],
          status: 'returned',
          created_at: new Date(yesterday.getTime() - 24 * 60 * 60 * 1000),
          updated_at: yesterday
        }
      ]
      return
    }

    const userEpisodesQuery = query(
      collection(db!, `users/${employeeNumber}/episodes`),
      orderBy('created_at', 'desc')
    )
    
    userEpisodesUnsubscribe = onSnapshot(userEpisodesQuery, (snapshot) => {
      userEpisodes.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        created_at: doc.data().created_at?.toDate() || new Date(),
        updated_at: doc.data().updated_at?.toDate() || new Date()
      })) as UserEpisode[]
    }, (err) => {
      console.error('Error listening to user episodes:', err)
      error.value = 'Failed to load user episodes'
    })
  }

  const stopUserEpisodesListener = () => {
    if (userEpisodesUnsubscribe) {
      userEpisodesUnsubscribe()
      userEpisodesUnsubscribe = null
    }
  }

  // Admin method to fetch all episodes from all users
  const initializeAllEpisodesListener = () => {
    if (isDemoMode) {
      allEpisodes.value = []
      return
    }

    loading.value = true
    error.value = null

    try {
      // This is a complex query - we need to get all episodes from all users
      // Since Firestore doesn't support collection group queries easily with our structure,
      // we'll use a different approach by listening to all users and their episodes
      const usersQuery = query(collection(db!, 'users'))

      allEpisodesUnsubscribe = onSnapshot(usersQuery, async (usersSnapshot) => {
        const allEpisodesData: UserEpisode[] = []

        // For each user, get their episodes
        for (const userDoc of usersSnapshot.docs) {
          const userData = userDoc.data()
          const employeeNumber = userData.employee_number

          if (employeeNumber) {
            try {
              const userEpisodesQuery = query(
                collection(db!, `users/${employeeNumber}/episodes`),
                orderBy('created_at', 'desc')
              )

              const episodesSnapshot = await getDocs(userEpisodesQuery)
              const userEpisodesData = episodesSnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data(),
                user_employee_number: employeeNumber, // Add employee number for admin view
                created_at: doc.data().created_at?.toDate() || new Date(),
                updated_at: doc.data().updated_at?.toDate() || new Date()
              })) as UserEpisode[]

              allEpisodesData.push(...userEpisodesData)
            } catch (err) {
              console.warn(`Failed to load episodes for user ${employeeNumber}:`, err)
            }
          }
        }

        // Sort all episodes by creation date (newest first)
        allEpisodesData.sort((a, b) => b.created_at.getTime() - a.created_at.getTime())
        allEpisodes.value = allEpisodesData
        loading.value = false
      }, (err) => {
        console.error('Error listening to all episodes:', err)
        error.value = 'Failed to load all episodes'
        loading.value = false
      })
    } catch (err) {
      console.error('Error initializing all episodes listener:', err)
      error.value = 'Failed to initialize episodes listener'
      loading.value = false
    }
  }

  const stopAllEpisodesListener = () => {
    if (allEpisodesUnsubscribe) {
      allEpisodesUnsubscribe()
      allEpisodesUnsubscribe = null
    }
  }

  const addToCart = (item: CartItem): { success: boolean; reason?: string } => {
    // Check contraindications
    const conflictingItems = checkContraindications(item)
    if (conflictingItems.length > 0) {
      // Return false to indicate contraindication check needed
      return { success: false, reason: 'contraindication' }
    }

    const existingIndex = cart.value.findIndex(
      cartItem => cartItem.item_group_id === item.item_group_id
    )

    if (existingIndex >= 0) {
      // Update quantity if item already in cart
      const newQuantity = cart.value[existingIndex].quantity + item.quantity
      if (newQuantity <= item.max_quantity) {
        cart.value[existingIndex].quantity = newQuantity
        return { success: true }
      } else {
        return { success: false, reason: 'insufficient_quantity' }
      }
    } else {
      // Check if requested quantity is available
      if (item.quantity > item.max_quantity) {
        return { success: false, reason: 'insufficient_quantity' }
      }

      // Add new item to cart
      cart.value.push(item)
      return { success: true }
    }
  }

  const removeFromCart = (itemGroupId: string) => {
    const index = cart.value.findIndex(item => item.item_group_id === itemGroupId)
    if (index >= 0) {
      cart.value.splice(index, 1)
    }
  }

  const updateCartItemQuantity = (itemGroupId: string, quantity: number) => {
    const item = cart.value.find(item => item.item_group_id === itemGroupId)
    if (item) {
      if (quantity <= 0) {
        removeFromCart(itemGroupId)
      } else if (quantity <= item.max_quantity) {
        item.quantity = quantity
      }
    }
  }

  const clearCart = () => {
    cart.value = []
  }

  // Validate cart availability before submission
  const validateCartAvailability = async (): Promise<{ valid: boolean; errors: string[] }> => {
    const errors: string[] = []

    if (cart.value.length === 0) {
      errors.push('Cart is empty')
      return { valid: false, errors }
    }

    if (selectedDates.value.length === 0) {
      errors.push('No dates selected')
      return { valid: false, errors }
    }

    // Import items store to check current availability
    const { useItemsStore } = await import('@/stores/items')
    const itemsStore = useItemsStore()

    for (const cartItem of cart.value) {
      // Find the item group
      const itemGroup = itemsStore.itemGroups.find(group => group.id === cartItem.item_group_id)

      if (!itemGroup) {
        errors.push(`Item "${cartItem.item_group_name}" no longer exists`)
        continue
      }

      // Check if item group is still active
      if (itemGroup.isActive === false) {
        errors.push(`Item "${cartItem.item_group_name}" is no longer available`)
        continue
      }

      // Check availability for selected dates
      const availableQuantity = itemsStore.getAvailableQuantityWithCart(itemGroup, cart.value)

      if (cartItem.quantity > availableQuantity) {
        errors.push(`Only ${availableQuantity} of "${cartItem.item_group_name}" available for selected dates`)
      }
    }

    return { valid: errors.length === 0, errors }
  }

  const checkContraindications = (newItem: CartItem): Array<{item_group_name: string, reason: string}> => {
    const conflicts: Array<{item_group_name: string, reason: string}> = []
    
    cart.value.forEach(cartItem => {
      // Check if new item has contraindications with existing cart items
      newItem.contraindications.forEach(contraindication => {
        if (contraindication.item_group_name === cartItem.item_group_name) {
          conflicts.push(contraindication)
        }
      })
      
      // Check if existing cart items have contraindications with new item
      cartItem.contraindications.forEach(contraindication => {
        if (contraindication.item_group_name === newItem.item_group_name) {
          conflicts.push(contraindication)
        }
      })
    })
    
    return conflicts
  }

  const setSelectedDates = (dates: string[]) => {
    selectedDates.value = dates
  }

  const submitCart = async (employeeNumber: string): Promise<string> => {
    // Validate cart availability before submission
    const validation = await validateCartAvailability()
    if (!validation.valid) {
      throw new Error(`Booking validation failed:\n${validation.errors.join('\n')}`)
    }

    loading.value = true
    error.value = null

    try {
      if (isDemoMode) {
        // Demo mode - simulate successful submission and add to episodes
        const episodeId = `demo_episode_${Date.now()}`
        console.log('Demo mode: Simulating cart submission for employee:', employeeNumber)

        // Create a new episode from cart data
        const newEpisode: UserEpisode = {
          id: episodeId,
          employee_number: employeeNumber,
          dates: [...selectedDates.value],
          items: cart.value.map(cartItem => ({
            item_group_id: cartItem.item_group_id,
            item_group_name: cartItem.item_group_name,
            item_group_type: cartItem.item_group_type,
            individual_item_id: `demo_item_${Date.now()}`,
            quantity: cartItem.quantity
          })),
          status: 'scheduled',
          created_at: new Date(),
          updated_at: new Date()
        }

        // Add to the beginning of the episodes array
        userEpisodes.value.unshift(newEpisode)

        // Clear cart and dates
        clearCart()
        selectedDates.value = []

        return episodeId
      }

      const episodeId = await runTransaction(db!, async (transaction) => {
        const now = new Date()
        const episodeId = `episode_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

        // STEP 1: DO ALL READS FIRST
        const itemGroupDocs: any[] = []
        const userRef = doc(db, 'users', employeeNumber)

        // Read all item groups first
        for (const cartItem of cart.value) {
          const itemGroupRef = doc(db, 'item_groups', cartItem.item_group_id)
          const itemGroupDoc = await transaction.get(itemGroupRef)

          if (!itemGroupDoc.exists()) {
            throw new Error(`Item group ${cartItem.item_group_name} not found`)
          }

          itemGroupDocs.push({
            ref: itemGroupRef,
            doc: itemGroupDoc,
            cartItem: cartItem
          })
        }

        // Read user document
        const userDoc = await transaction.get(userRef)

        // STEP 2: PROCESS DATA AND PREPARE WRITES
        const episodeItems: Array<{
          item_group_id: string
          item_group_name: string
          item_group_type: 'resus_trainings' | 'venues' | 'audio_visuals'
          individual_item_id: string
          quantity: number
        }> = []

        // Process each item group
        for (const { ref: itemGroupRef, doc: itemGroupDoc, cartItem } of itemGroupDocs) {
          const groupData = itemGroupDoc.data()
          const items = groupData.items || []

          // Find available items with lowest use_count
          const availableItems = items
            .filter((item: any) => {
              if (item.suspended) return false

              // Check if item is available on selected dates
              for (const episodeId in item.episodes || {}) {
                const episode = item.episodes[episodeId]
                if (episode.status === 'scheduled' || episode.status === 'rented') {
                  const hasConflict = selectedDates.value.some(date =>
                    episode.dates.includes(date)
                  )
                  if (hasConflict) return false
                }
              }
              return true
            })
            .sort((a: any, b: any) => (a.use_count || 0) - (b.use_count || 0))

          if (availableItems.length < cartItem.quantity) {
            throw new Error(`Insufficient stock for ${cartItem.item_group_name}`)
          }

          // Select items with lowest use_count
          const selectedItems = availableItems.slice(0, cartItem.quantity)

          selectedItems.forEach((item: any) => {
            episodeItems.push({
              item_group_id: cartItem.item_group_id,
              item_group_name: cartItem.item_group_name,
              item_group_type: cartItem.item_group_type,
              individual_item_id: item.id,
              quantity: 1
            })

            // Update item's episodes and use_count
            if (!item.episodes) item.episodes = {}
            item.episodes[episodeId] = {
              dates: [...selectedDates.value],
              status: 'scheduled'
            }
            item.use_count = (item.use_count || 0) + 1
          })

          // STEP 3: DO ALL WRITES
          transaction.update(itemGroupRef, { items, updated_at: now })
        }

        // Create user episode
        const userEpisodeRef = doc(db, `users/${employeeNumber}/episodes`, episodeId)
        transaction.set(userEpisodeRef, {
          id: episodeId,
          employee_number: employeeNumber,
          dates: [...selectedDates.value],
          items: episodeItems,
          status: 'scheduled',
          created_at: now,
          updated_at: now
        })

        // Create centralized rental record for admin panel
        const rentalRef = doc(db, 'rentals', episodeId)
        transaction.set(rentalRef, {
          id: episodeId,
          employee_number: employeeNumber,
          user_employee_number: employeeNumber, // For compatibility
          dates: [...selectedDates.value],
          items: episodeItems,
          status: 'scheduled',
          created_at: now,
          updated_at: now,
          // Additional admin-friendly fields
          total_items: episodeItems.length,
          item_types: [...new Set(episodeItems.map(item => item.item_group_type))],
          item_names: episodeItems.map(item => item.item_group_name).join(', ')
        })

        // Update user's episodes in main user document
        if (userDoc.exists()) {
          const userData = userDoc.data()
          const episodes = userData.episodes || {}
          episodes[episodeId] = {
            id: episodeId,
            dates: [...selectedDates.value],
            items: episodeItems,
            status: 'scheduled'
          }
          transaction.update(userRef, { episodes, updated_at: now })
        }

        return episodeId
      })

      // Clear cart after successful submission
      clearCart()
      selectedDates.value = []

      return episodeId
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to submit booking'
      throw err
    } finally {
      loading.value = false
    }
  }

  const cancelEpisode = async (employeeNumber: string, episodeId: string): Promise<void> => {
    loading.value = true
    error.value = null

    try {
      if (isDemoMode) {
        // Demo mode - simulate successful cancellation
        console.log('Demo mode: Simulating episode cancellation:', episodeId)
        return
      }

      await runTransaction(db!, async (transaction) => {
        const now = new Date()

        // STEP 1: DO ALL READS FIRST
        const userEpisodeRef = doc(db, `users/${employeeNumber}/episodes`, episodeId)
        const rentalRef = doc(db, 'rentals', episodeId)

        const userEpisodeDoc = await transaction.get(userEpisodeRef)
        const rentalDoc = await transaction.get(rentalRef)

        if (!userEpisodeDoc.exists()) {
          throw new Error('Episode not found')
        }

        const episodeData = userEpisodeDoc.data()

        if (episodeData.status !== 'scheduled') {
          throw new Error('Can only cancel scheduled episodes')
        }

        // Read all item groups first
        const itemGroupUpdates: Array<{
          ref: any,
          items: any[]
        }> = []

        for (const item of episodeData.items) {
          const itemGroupRef = doc(db, 'item_groups', item.item_group_id)
          const itemGroupDoc = await transaction.get(itemGroupRef)

          if (itemGroupDoc.exists()) {
            const groupData = itemGroupDoc.data()
            const items = groupData.items || []

            const itemIndex = items.findIndex((i: any) => i.id === item.individual_item_id)
            if (itemIndex >= 0 && items[itemIndex].episodes && items[itemIndex].episodes[episodeId]) {
              items[itemIndex].episodes[episodeId].status = 'cancelled'
              itemGroupUpdates.push({
                ref: itemGroupRef,
                items: items
              })
            }
          }
        }

        // Read user document
        const userRef = doc(db, 'users', employeeNumber)
        const userDoc = await transaction.get(userRef)

        let userEpisodesUpdate = null
        if (userDoc.exists()) {
          const userData = userDoc.data()
          const episodes = userData.episodes || {}
          if (episodes[episodeId]) {
            episodes[episodeId].status = 'cancelled'
            userEpisodesUpdate = { episodes }
          }
        }

        // STEP 2: DO ALL WRITES
        // Update episode status
        transaction.update(userEpisodeRef, {
          status: 'cancelled',
          updated_at: now
        })

        // Update centralized rental record (only if it exists)
        if (rentalDoc.exists()) {
          transaction.update(rentalRef, {
            status: 'cancelled',
            updated_at: now
          })
        }

        // Update all item groups
        for (const update of itemGroupUpdates) {
          transaction.update(update.ref, {
            items: update.items,
            updated_at: now
          })
        }

        // Update user's main episodes record
        if (userEpisodesUpdate) {
          transaction.update(userRef, userEpisodesUpdate)
        }
      })
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to cancel episode'
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    userEpisodes: readonly(userEpisodes),
    allEpisodes: readonly(allEpisodes),
    cart: readonly(cart),
    selectedDates: readonly(selectedDates),
    loading: readonly(loading),
    error: readonly(error),

    // Getters
    episodesByStatus,
    cartTotal,
    cartItemGroups,

    // Actions
    initializeUserEpisodesListener,
    stopUserEpisodesListener,
    initializeAllEpisodesListener,
    stopAllEpisodesListener,
    addToCart,
    removeFromCart,
    updateCartItemQuantity,
    clearCart,
    validateCartAvailability,
    checkContraindications,
    setSelectedDates,
    submitCart,
    cancelEpisode,
    clearError
  }
})
