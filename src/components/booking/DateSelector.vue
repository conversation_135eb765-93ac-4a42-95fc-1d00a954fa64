<template>
  <div class="date-selector">
    <div class="date-mode-selector">
      <div class="mode-buttons">
        <Button
          label="Single Date"
          :class="{ 'p-button-outlined': dateMode !== 'single' }"
          @click="setDateMode('single')"
          size="small"
          :disabled="disabled"
        />
        <Button
          label="Multiple Dates"
          :class="{ 'p-button-outlined': dateMode !== 'multiple' }"
          @click="setDateMode('multiple')"
          size="small"
          :disabled="disabled"
        />
        <Button
          label="Date Range"
          :class="{ 'p-button-outlined': dateMode !== 'range' }"
          @click="setDateMode('range')"
          size="small"
          :disabled="disabled"
        />
      </div>

      <div class="selected-dates-info" v-if="selectedDates.length > 0">
        <span class="dates-count">{{ selectedDates.length }} date(s) selected</span>
        <Button
          icon="pi pi-times"
          class="p-button-text p-button-sm"
          @click="clearDates"
          :disabled="disabled"
          v-tooltip="'Clear all dates'"
        />
      </div>
    </div>

    <!-- Instructions for date selection -->
    <div class="date-instructions" v-if="dateMode">
      <div class="instruction-text">
        <i class="pi pi-info-circle"></i>
        <span v-if="dateMode === 'single'">Click on a date button to select it</span>
        <span v-else-if="dateMode === 'multiple'">Click on multiple date buttons to select them individually</span>
        <span v-else-if="dateMode === 'range'">Click on start date, then click on end date to select a range</span>
      </div>
    </div>

    <!-- Date Buttons Grid -->
    <div class="date-buttons-container">
      <!-- Month Navigation -->
      <div class="month-navigation">
        <Button
          icon="pi pi-chevron-left"
          class="p-button-text"
          @click="previousMonth"
          :disabled="disabled || !canGoPreviousMonth"
          v-tooltip="'Previous month'"
        />
        <h3 class="current-month-label">{{ currentMonthLabel }}</h3>
        <Button
          icon="pi pi-chevron-right"
          class="p-button-text"
          @click="nextMonth"
          :disabled="disabled || !canGoNextMonth"
          v-tooltip="'Next month'"
        />
      </div>

      <!-- Current Month Display -->
      <div class="month-section">
        <div class="date-buttons-grid">
          <Button
            v-for="date in currentMonthDates"
            :key="date.value"
            :label="date.day"
            :class="getDateButtonClass(date)"
            @click="onDateButtonClick(date)"
            :disabled="disabled || date.disabled"
            size="small"
            class="date-button"
          >
            <template #default>
              <div class="date-button-content">
                <span class="day-number">{{ date.day }}</span>
                <span class="day-name">{{ date.dayName }}</span>
              </div>
            </template>
          </Button>
        </div>
      </div>
    </div>

    <!-- Selected Dates Display -->
    <div class="selected-dates-display" v-if="selectedDates.length > 0">
      <h4>Selected Dates:</h4>
      <div class="dates-list">
        <Tag
          v-for="date in formattedSelectedDates"
          :key="date.value"
          :value="date.label"
          severity="info"
          class="date-tag"
        >
          <template #default>
            {{ date.label }}
            <Button
              icon="pi pi-times"
              class="p-button-text p-button-sm remove-date-btn"
              @click="removeDate(date.value)"
              :disabled="disabled"
            />
          </template>
        </Tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { format, parseISO, addDays, eachDayOfInterval, startOfMonth, endOfMonth, differenceInDays } from 'date-fns'
import { useToast } from 'primevue/usetoast'

interface Props {
  modelValue: string[]
  disabled?: boolean
  minDaysAhead?: number
  maxDaysAhead?: number
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void
}

interface DateButton {
  value: string
  day: string
  dayName: string
  date: Date
  disabled: boolean
  isToday: boolean
}

interface MonthSection {
  key: string
  label: string
  dates: DateButton[]
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  minDaysAhead: 0,
  maxDaysAhead: 90
})

const emit = defineEmits<Emits>()

// Toast for user feedback
const toast = useToast()

// Constants
const MAX_BOOKING_DAYS = 365 // 1 year maximum

// Reactive state
const dateMode = ref<'single' | 'multiple' | 'range'>('single')
const selectedDates = ref<string[]>([...props.modelValue])
const rangeStartDate = ref<string | null>(null)
const currentViewMonth = ref(new Date())

// Computed properties
const minDate = computed(() => {
  const today = new Date()
  return addDays(today, props.minDaysAhead)
})

const maxDate = computed(() => {
  const today = new Date()
  return addDays(today, props.maxDaysAhead)
})

// Current month navigation
const currentMonthLabel = computed(() => {
  return format(currentViewMonth.value, 'MMMM yyyy')
})

const currentMonthDates = computed((): DateButton[] => {
  const today = new Date()
  const monthStart = startOfMonth(currentViewMonth.value)
  const monthEnd = endOfMonth(currentViewMonth.value)

  // Get all days in the current view month
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd })
    .map(date => {
      const dateStr = format(date, 'yyyy-MM-dd')
      const isDisabled = date < minDate.value || date > maxDate.value

      return {
        value: dateStr,
        day: format(date, 'd'),
        dayName: format(date, 'EEE'),
        date,
        disabled: isDisabled,
        isToday: format(date, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd')
      } as DateButton
    })

  return monthDays
})

const canGoPreviousMonth = computed(() => {
  const prevMonth = new Date(currentViewMonth.value.getFullYear(), currentViewMonth.value.getMonth() - 1, 1)
  const minMonth = startOfMonth(minDate.value)
  return prevMonth >= minMonth
})

const canGoNextMonth = computed(() => {
  const nextMonth = new Date(currentViewMonth.value.getFullYear(), currentViewMonth.value.getMonth() + 1, 1)
  const maxMonth = startOfMonth(maxDate.value)
  return nextMonth <= maxMonth
})

// Month navigation methods
const previousMonth = () => {
  if (canGoPreviousMonth.value) {
    currentViewMonth.value = new Date(
      currentViewMonth.value.getFullYear(),
      currentViewMonth.value.getMonth() - 1,
      1
    )
  }
}

const nextMonth = () => {
  if (canGoNextMonth.value) {
    currentViewMonth.value = new Date(
      currentViewMonth.value.getFullYear(),
      currentViewMonth.value.getMonth() + 1,
      1
    )
  }
}

const formattedSelectedDates = computed(() => {
  return selectedDates.value
    .sort()
    .map(dateStr => ({
      value: dateStr,
      label: format(parseISO(dateStr), 'MMM dd, yyyy')
    }))
})

// Validation methods
const validateBookingPeriod = (dates: string[]): boolean => {
  if (dates.length === 0) return true

  // Sort dates to get the range
  const sortedDates = [...dates].sort()
  const firstDate = parseISO(sortedDates[0])
  const lastDate = parseISO(sortedDates[sortedDates.length - 1])

  // Calculate the difference in days
  const daysDifference = differenceInDays(lastDate, firstDate) + 1 // +1 to include both start and end dates

  if (daysDifference > MAX_BOOKING_DAYS) {
    toast.add({
      severity: 'error',
      summary: 'Booking Period Too Long',
      detail: `Maximum booking period is ${MAX_BOOKING_DAYS} days (1 year). Selected period is ${daysDifference} days.`,
      life: 5000
    })
    return false
  }

  return true
}

// Methods
const setDateMode = (mode: 'single' | 'multiple' | 'range') => {
  dateMode.value = mode
  clearDates()
}

const clearDates = () => {
  selectedDates.value = []
  rangeStartDate.value = null
  emit('update:modelValue', [])
}

const removeDate = (dateStr: string) => {
  const index = selectedDates.value.indexOf(dateStr)
  if (index > -1) {
    selectedDates.value.splice(index, 1)
    emit('update:modelValue', [...selectedDates.value])
  }
}

const getDateButtonClass = (date: DateButton) => {
  const classes = []

  if (selectedDates.value.includes(date.value)) {
    classes.push('selected')
  }

  if (date.isToday) {
    classes.push('today')
  }

  if (rangeStartDate.value === date.value && dateMode.value === 'range') {
    classes.push('range-start')
  }

  return classes.join(' ')
}

const onDateButtonClick = (date: DateButton) => {
  if (date.disabled) return

  const dateStr = date.value

  if (dateMode.value === 'single') {
    selectedDates.value = [dateStr]
    rangeStartDate.value = null
  } else if (dateMode.value === 'multiple') {
    const index = selectedDates.value.indexOf(dateStr)
    if (index > -1) {
      // Date already selected, remove it
      selectedDates.value.splice(index, 1)
    } else {
      // Add new date - check 1-year limit
      const newDates = [...selectedDates.value, dateStr]
      if (!validateBookingPeriod(newDates)) {
        return // Don't add if it exceeds 1-year limit
      }
      selectedDates.value.push(dateStr)
    }
    rangeStartDate.value = null
  } else if (dateMode.value === 'range') {
    if (!rangeStartDate.value) {
      // First click - set start date
      rangeStartDate.value = dateStr
      selectedDates.value = [dateStr]
    } else {
      // Second click - set end date and generate range
      const startDate = parseISO(rangeStartDate.value)
      const endDate = parseISO(dateStr)

      let datesInRange: Date[]
      if (endDate < startDate) {
        // If end date is before start date, swap them
        datesInRange = eachDayOfInterval({ start: endDate, end: startDate })
      } else {
        datesInRange = eachDayOfInterval({ start: startDate, end: endDate })
      }

      // Check 1-year limit for range
      const rangeDates = datesInRange.map(d => format(d, 'yyyy-MM-dd'))
      if (!validateBookingPeriod(rangeDates)) {
        rangeStartDate.value = null
        return // Don't create range if it exceeds 1-year limit
      }

      selectedDates.value = rangeDates
      rangeStartDate.value = null
    }
  }

  emit('update:modelValue', [...selectedDates.value])
}

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  selectedDates.value = [...newValue]
}, { immediate: true })
</script>

<style scoped>
.date-selector {
  width: 100%;
}

.date-mode-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.mode-buttons {
  display: flex;
  gap: 0.5rem;
}

.selected-dates-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dates-count {
  font-size: 0.9rem;
  color: var(--text-color-secondary);
  font-weight: 500;
}

.date-buttons-container {
  margin-bottom: 1.5rem;
}

/* Month Navigation */
.month-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--surface-card);
  border: 2px solid var(--primary-color);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.current-month-label {
  margin: 0;
  color: var(--text-color);
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  flex: 1;
  text-shadow: none;
}

.month-navigation .p-button {
  background: var(--primary-color) !important;
  border: 1px solid var(--primary-color) !important;
  color: white !important;
  transition: all 0.3s ease;
  min-width: 3rem !important;
  height: 3rem !important;
  border-radius: 8px !important;
}

.month-navigation .p-button:hover:not(:disabled) {
  background: var(--primary-600) !important;
  border-color: var(--primary-600) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.month-navigation .p-button:disabled {
  background: var(--surface-300) !important;
  border-color: var(--surface-300) !important;
  color: var(--text-color-secondary) !important;
  cursor: not-allowed;
  transform: none;
}

.month-section {
  margin-bottom: 2rem;
}

.month-header {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  text-align: center;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--surface-border);
}

.date-buttons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 0.5rem;
  max-width: 100%;
}

.date-button {
  min-height: 60px;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  color: #333 !important; /* Ensure default text is visible */
}

.date-button:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.date-button.selected {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
}

/* Ensure selected date buttons have proper text visibility */
.date-selector .p-button.date-button.selected {
  background: #6366f1 !important;
  border-color: #6366f1 !important;
  color: white !important;
}

.date-selector .p-button.date-button.selected:hover {
  background: #5855eb !important;
  border-color: #5855eb !important;
  color: white !important;
}

.date-button.today {
  border: 2px solid var(--primary-color);
  font-weight: 600;
}

.date-button.range-start {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
  position: relative;
}

/* Ensure range buttons have proper text visibility */
.date-selector .p-button.date-button.range-start,
.date-selector .p-button.date-button.range-end {
  background: #6366f1 !important;
  border-color: #6366f1 !important;
  color: white !important;
}

.date-selector .p-button.date-button.range-start:hover,
.date-selector .p-button.date-button.range-end:hover {
  background: #5855eb !important;
  border-color: #5855eb !important;
  color: white !important;
}

.date-button.range-start::after {
  content: 'START';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.6rem;
  font-weight: bold;
  opacity: 0.8;
}

.date-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.day-number {
  font-size: 1.1rem;
  font-weight: 600;
}

.day-name {
  font-size: 0.75rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.selected-dates-display h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  color: var(--text-color);
  font-weight: 600;
}

.dates-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.date-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.remove-date-btn {
  padding: 0.125rem;
  width: 1.25rem;
  height: 1.25rem;
  min-width: unset;
}

.date-instructions {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
}

.instruction-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.9rem;
}

.instruction-text i {
  color: #3b82f6;
}

/* Additional specificity to override global button styles */
.date-selector .p-button.date-button {
  color: #333 !important;
  background: white !important;
  border-color: #ddd !important;
}

.date-selector .p-button.date-button:hover {
  color: #333 !important;
  background: #f8f9fa !important;
  border-color: #6366f1 !important;
}

.date-selector .p-button.date-button:disabled {
  color: #999 !important;
  background: #f5f5f5 !important;
  border-color: #e5e5e5 !important;
}

@media (max-width: 768px) {
  .date-mode-selector {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .mode-buttons {
    justify-content: center;
  }

  .selected-dates-info {
    justify-content: center;
  }

  .date-buttons-grid {
    grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
    gap: 0.4rem;
  }

  .date-button {
    min-height: 50px;
    padding: 0.4rem;
  }

  .day-number {
    font-size: 1rem;
  }

  .day-name {
    font-size: 0.7rem;
  }

  /* Month navigation responsive */
  .month-navigation {
    padding: 0.75rem;
  }

  .current-month-label {
    font-size: 1.25rem;
    color: var(--text-color);
  }

  .month-navigation .p-button {
    padding: 0.5rem !important;
    min-width: 2.5rem !important;
    height: 2.5rem !important;
  }
}
</style>
