/* UNIFIED BUTTON SYSTEM - Consistent styling across the entire application */

/* Base Button Variables */
:root {
  --btn-border-radius: 8px;
  --btn-font-weight: 600;
  --btn-transition: all 0.2s ease;
  --btn-shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.15);
  --btn-transform-hover: translateY(-2px);
  
  /* Primary Colors */
  --btn-primary-bg: #6366f1;
  --btn-primary-border: #6366f1;
  --btn-primary-color: white;
  --btn-primary-hover-bg: #5855eb;
  --btn-primary-hover-border: #5855eb;
  --btn-primary-shadow: rgba(99, 102, 241, 0.3);
  
  /* Secondary Colors */
  --btn-secondary-bg: #6b7280;
  --btn-secondary-border: #6b7280;
  --btn-secondary-color: white;
  --btn-secondary-hover-bg: #5b6470;
  --btn-secondary-hover-border: #5b6470;
  --btn-secondary-shadow: rgba(107, 114, 128, 0.3);
  
  /* Success Colors */
  --btn-success-bg: #10b981;
  --btn-success-border: #10b981;
  --btn-success-color: white;
  --btn-success-hover-bg: #059669;
  --btn-success-hover-border: #059669;
  --btn-success-shadow: rgba(16, 185, 129, 0.3);
  
  /* Warning Colors */
  --btn-warning-bg: #f59e0b;
  --btn-warning-border: #f59e0b;
  --btn-warning-color: white;
  --btn-warning-hover-bg: #d97706;
  --btn-warning-hover-border: #d97706;
  --btn-warning-shadow: rgba(245, 158, 11, 0.3);
  
  /* Danger Colors */
  --btn-danger-bg: #ef4444;
  --btn-danger-border: #ef4444;
  --btn-danger-color: white;
  --btn-danger-hover-bg: #dc2626;
  --btn-danger-hover-border: #dc2626;
  --btn-danger-shadow: rgba(239, 68, 68, 0.3);
  
  /* Info Colors */
  --btn-info-bg: #3b82f6;
  --btn-info-border: #3b82f6;
  --btn-info-color: white;
  --btn-info-hover-bg: #2563eb;
  --btn-info-hover-border: #2563eb;
  --btn-info-shadow: rgba(59, 130, 246, 0.3);
}

/* Base Button Styles - Apply to ALL buttons */
.p-button,
button,
.btn {
  font-weight: var(--btn-font-weight) !important;
  border-radius: var(--btn-border-radius) !important;
  transition: var(--btn-transition) !important;
  cursor: pointer !important;
  border: 2px solid transparent !important;
  position: relative !important;
  overflow: hidden !important;
}

/* Hover Effects for ALL buttons */
.p-button:not(:disabled):hover,
button:not(:disabled):hover,
.btn:not(:disabled):hover {
  transform: var(--btn-transform-hover) !important;
  box-shadow: var(--btn-shadow-hover) !important;
}

/* Primary Button Styles */
.p-button:not(.p-button-outlined):not([severity]),
.p-button.p-button-primary,
.btn-primary {
  background: var(--btn-primary-bg) !important;
  border-color: var(--btn-primary-border) !important;
  color: var(--btn-primary-color) !important;
}

.p-button:not(.p-button-outlined):not([severity]):hover,
.p-button.p-button-primary:hover,
.btn-primary:hover {
  background: var(--btn-primary-hover-bg) !important;
  border-color: var(--btn-primary-hover-border) !important;
  box-shadow: 0 4px 12px var(--btn-primary-shadow) !important;
}

/* Secondary Button Styles */
.p-button[severity="secondary"],
.p-button.p-button-secondary,
.btn-secondary {
  background: var(--btn-secondary-bg) !important;
  border-color: var(--btn-secondary-border) !important;
  color: var(--btn-secondary-color) !important;
}

.p-button[severity="secondary"]:hover,
.p-button.p-button-secondary:hover,
.btn-secondary:hover {
  background: var(--btn-secondary-hover-bg) !important;
  border-color: var(--btn-secondary-hover-border) !important;
  box-shadow: 0 4px 12px var(--btn-secondary-shadow) !important;
}

/* Success Button Styles */
.p-button[severity="success"],
.p-button.p-button-success,
.btn-success {
  background: var(--btn-success-bg) !important;
  border-color: var(--btn-success-border) !important;
  color: var(--btn-success-color) !important;
}

.p-button[severity="success"]:hover,
.p-button.p-button-success:hover,
.btn-success:hover {
  background: var(--btn-success-hover-bg) !important;
  border-color: var(--btn-success-hover-border) !important;
  box-shadow: 0 4px 12px var(--btn-success-shadow) !important;
}

/* Warning Button Styles */
.p-button[severity="warning"],
.p-button.p-button-warning,
.btn-warning {
  background: var(--btn-warning-bg) !important;
  border-color: var(--btn-warning-border) !important;
  color: var(--btn-warning-color) !important;
}

.p-button[severity="warning"]:hover,
.p-button.p-button-warning:hover,
.btn-warning:hover {
  background: var(--btn-warning-hover-bg) !important;
  border-color: var(--btn-warning-hover-border) !important;
  box-shadow: 0 4px 12px var(--btn-warning-shadow) !important;
}

/* Danger Button Styles */
.p-button[severity="danger"],
.p-button.p-button-danger,
.btn-danger {
  background: var(--btn-danger-bg) !important;
  border-color: var(--btn-danger-border) !important;
  color: var(--btn-danger-color) !important;
}

.p-button[severity="danger"]:hover,
.p-button.p-button-danger:hover,
.btn-danger:hover {
  background: var(--btn-danger-hover-bg) !important;
  border-color: var(--btn-danger-hover-border) !important;
  box-shadow: 0 4px 12px var(--btn-danger-shadow) !important;
}

/* Info Button Styles */
.p-button[severity="info"],
.p-button.p-button-info,
.btn-info {
  background: var(--btn-info-bg) !important;
  border-color: var(--btn-info-border) !important;
  color: var(--btn-info-color) !important;
}

.p-button[severity="info"]:hover,
.p-button.p-button-info:hover,
.btn-info:hover {
  background: var(--btn-info-hover-bg) !important;
  border-color: var(--btn-info-hover-border) !important;
  box-shadow: 0 4px 12px var(--btn-info-shadow) !important;
}

/* Outlined Button Styles */
.p-button.p-button-outlined {
  background: white !important;
  color: var(--btn-primary-bg) !important;
  border-color: var(--btn-primary-border) !important;
}

.p-button.p-button-outlined:hover {
  background: var(--btn-primary-bg) !important;
  color: white !important;
  border-color: var(--btn-primary-border) !important;
  box-shadow: 0 4px 12px var(--btn-primary-shadow) !important;
}

.p-button.p-button-outlined[severity="secondary"] {
  color: var(--btn-secondary-bg) !important;
  border-color: var(--btn-secondary-border) !important;
}

.p-button.p-button-outlined[severity="secondary"]:hover {
  background: var(--btn-secondary-bg) !important;
  color: white !important;
  box-shadow: 0 4px 12px var(--btn-secondary-shadow) !important;
}

.p-button.p-button-outlined[severity="danger"] {
  color: var(--btn-danger-bg) !important;
  border-color: var(--btn-danger-border) !important;
}

.p-button.p-button-outlined[severity="danger"]:hover {
  background: var(--btn-danger-bg) !important;
  color: white !important;
  box-shadow: 0 4px 12px var(--btn-danger-shadow) !important;
}

/* Text Button Styles */
.p-button.p-button-text {
  background: transparent !important;
  border-color: transparent !important;
  color: var(--btn-primary-bg) !important;
}

.p-button.p-button-text:hover {
  background: rgba(99, 102, 241, 0.1) !important;
  color: var(--btn-primary-hover-bg) !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Size Variations */
.p-button.p-button-sm,
.btn-sm {
  padding: 0.5rem 1rem !important;
  font-size: 0.875rem !important;
}

.p-button.p-button-lg,
.btn-lg {
  padding: 1rem 2rem !important;
  font-size: 1.125rem !important;
}

/* Special Button Types */
.add-to-cart-btn {
  background: var(--btn-success-bg) !important;
  border-color: var(--btn-success-border) !important;
  color: var(--btn-success-color) !important;
  font-weight: 600 !important;
  padding: 0.75rem 1.5rem !important;
}

.add-to-cart-btn:hover {
  background: var(--btn-success-hover-bg) !important;
  border-color: var(--btn-success-hover-border) !important;
  box-shadow: 0 4px 12px var(--btn-success-shadow) !important;
}

.submit-btn {
  background: var(--btn-primary-bg) !important;
  border-color: var(--btn-primary-border) !important;
  color: var(--btn-primary-color) !important;
  font-weight: 700 !important;
  padding: 1rem 2rem !important;
  font-size: 1.1rem !important;
}

.submit-btn:hover {
  background: var(--btn-primary-hover-bg) !important;
  border-color: var(--btn-primary-hover-border) !important;
  box-shadow: 0 6px 16px var(--btn-primary-shadow) !important;
}

.login-btn {
  background: var(--btn-primary-bg) !important;
  border-color: var(--btn-primary-border) !important;
  color: var(--btn-primary-color) !important;
  font-weight: 700 !important;
  padding: 1rem 2rem !important;
  width: 100% !important;
  font-size: 1.1rem !important;
}

.login-btn:hover {
  background: var(--btn-primary-hover-bg) !important;
  border-color: var(--btn-primary-hover-border) !important;
  box-shadow: 0 6px 16px var(--btn-primary-shadow) !important;
}

/* Disabled Button Styles */
.p-button:disabled,
button:disabled,
.btn:disabled {
  background: #e5e7eb !important;
  border-color: #e5e7eb !important;
  color: #9ca3af !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Loading Button Styles */
.p-button.p-button-loading {
  pointer-events: none !important;
}

.p-button.p-button-loading .p-button-loading-icon {
  margin-right: 0.5rem !important;
}

/* Responsive Button Adjustments */
@media (max-width: 768px) {
  .p-button,
  button,
  .btn {
    padding: 0.75rem 1.25rem !important;
    font-size: 0.95rem !important;
  }
  
  .p-button.p-button-sm,
  .btn-sm {
    padding: 0.5rem 0.875rem !important;
    font-size: 0.8rem !important;
  }
  
  .p-button.p-button-lg,
  .btn-lg {
    padding: 0.875rem 1.5rem !important;
    font-size: 1rem !important;
  }
}
