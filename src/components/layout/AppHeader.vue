<template>
  <header class="app-header">
    <Toolbar class="header-toolbar">
      <template #start>
        <div class="header-brand">
          <i class="pi pi-box text-2xl text-primary mr-2"></i>
          <span class="font-bold text-xl text-primary">TKOH NSD Renting Platform</span>
        </div>
      </template>
      
      <template #center>
        <div class="header-search" v-if="showSearch">
          <span class="p-input-icon-left">
            <i class="pi pi-search"></i>
            <InputText 
              v-model="searchQuery"
              placeholder="Search items..."
              class="w-full"
              @input="onSearch"
            />
          </span>
        </div>
      </template>
      
      <template #end>
        <div class="header-actions">
          <!-- Notifications -->
          <Button 
            icon="pi pi-bell" 
            severity="secondary" 
            text 
            rounded
            @click="toggleNotifications"
            class="mr-2"
          >
            <Badge 
              v-if="notificationCount > 0" 
              :value="notificationCount" 
              severity="danger"
              class="notification-badge"
            />
          </Button>
          
          <!-- User menu -->
          <Button 
            :label="userStore.currentUserProfile?.displayName || 'User'"
            icon="pi pi-user"
            severity="secondary"
            text
            @click="toggleUserMenu"
            aria-haspopup="true"
            aria-controls="user-menu"
          />
          
          <!-- User menu overlay -->
          <Menu 
            ref="userMenu" 
            id="user-menu" 
            :model="userMenuItems" 
            :popup="true"
          />
        </div>
      </template>
    </Toolbar>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useItemsStore } from '@/stores/items'
import { useRentalsStore } from '@/stores/rentals'
import Menu from 'primevue/menu'

const router = useRouter()
const userStore = useAuthStore()
const itemsStore = useItemsStore()
const rentalsStore = useRentalsStore()

const userMenu = ref()
const searchQuery = ref('')
const showNotifications = ref(false)

// Show search only on certain pages
const showSearch = computed(() => {
  const searchPages = ['home', 'items', 'booking']
  return searchPages.includes(router.currentRoute.value.name as string)
})

// Notification count (pending rentals for admin, user's rental updates for users)
const notificationCount = computed(() => {
  if (userStore.isAdmin) {
    return rentalsStore.pendingRentals.length
  } else {
    // Count user's rentals that have status updates
    return rentalsStore.userRentals.filter(rental => 
      rental.status === 'approved' || rental.status === 'overdue'
    ).length
  }
})

const userMenuItems = computed(() => [
  {
    label: 'Profile',
    icon: 'pi pi-user',
    command: () => router.push('/profile')
  },
  {
    label: 'My Rentals',
    icon: 'pi pi-calendar',
    command: () => router.push('/my-rentals')
  },
  ...(userStore.isAdmin ? [{
    label: 'Admin Panel',
    icon: 'pi pi-cog',
    command: () => router.push('/admin')
  }] : []),
  {
    separator: true
  },
  {
    label: 'Logout',
    icon: 'pi pi-sign-out',
    command: logout
  }
])

const toggleUserMenu = (event: Event) => {
  userMenu.value.toggle(event)
}

const toggleNotifications = () => {
  showNotifications.value = !showNotifications.value
  // Navigate to appropriate notifications page
  if (userStore.isAdmin) {
    router.push('/admin')
  } else {
    router.push('/my-rentals')
  }
}

const onSearch = () => {
  itemsStore.setSearchQuery(searchQuery.value)
}

const logout = async () => {
  try {
    await userStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('Logout failed:', error)
  }
}

onMounted(() => {
  // Sync search query with store
  searchQuery.value = itemsStore.searchQuery
})
</script>

<style scoped>
.app-header {
  background: var(--p-surface-card);
  border-bottom: 1px solid var(--p-surface-border);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-toolbar {
  padding: 0.75rem 1.5rem;
  border: none;
  background: transparent;
}

.header-brand {
  display: flex;
  align-items: center;
}

.header-search {
  width: 100%;
  max-width: 400px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  min-width: 18px;
  height: 18px;
  font-size: 0.75rem;
}

@media (max-width: 768px) {
  .header-brand span {
    display: none;
  }
  
  .header-search {
    max-width: 200px;
  }
  
  .header-toolbar {
    padding: 0.5rem 1rem;
  }
}
</style>
