/* SAFE PrimeVue Overlay Fixes - Only target overlay components that appear on top */

/* DIALOGS - Only target actual dialog overlays */
.p-dialog.p-component {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
  border: 1px solid #ddd !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.p-dialog .p-dialog-content {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

.p-dialog .p-dialog-header {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
  border-bottom: 1px solid #eee !important;
}

.p-dialog .p-dialog-footer {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
  border-top: 1px solid #eee !important;
}

/* DROPDOWN PANELS - Only target the floating panels */
.p-dropdown-panel.p-component {
  background: white !important;
  background-color: white !important;
  border: 1px solid #ddd !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.p-dropdown-items {
  background: white !important;
  background-color: white !important;
}

.p-dropdown-item {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

.p-dropdown-item:hover {
  background: #f5f5f5 !important;
  background-color: #f5f5f5 !important;
  color: #333 !important;
}

/* CALENDAR PANELS - Only target the floating calendar */
.p-calendar-panel.p-component {
  background: white !important;
  background-color: white !important;
  border: 1px solid #ddd !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.p-datepicker {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

.p-datepicker-header {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

.p-datepicker-calendar td {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

.p-datepicker-calendar td:hover {
  background: #f5f5f5 !important;
  background-color: #f5f5f5 !important;
}

/* MULTISELECT PANELS */
.p-multiselect-panel.p-component {
  background: white !important;
  background-color: white !important;
  border: 1px solid #ddd !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.p-multiselect-items {
  background: white !important;
  background-color: white !important;
}

.p-multiselect-item {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

/* TOAST MESSAGES */
.p-toast .p-toast-message {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
  border: 1px solid #ddd !important;
}

/* Z-INDEX MANAGEMENT */
.p-dialog.p-component {
  z-index: 10001 !important;
}

.p-dropdown-panel.p-component,
.p-multiselect-panel.p-component,
.p-calendar-panel.p-component {
  z-index: 10002 !important;
}
