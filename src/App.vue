<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { RouterView } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useItemsStore } from '@/stores/items'
import { useRentalsStore } from '@/stores/rentals'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppSidebar from '@/components/layout/AppSidebar.vue'

const authStore = useAuthStore()
const itemsStore = useItemsStore()
const rentalsStore = useRentalsStore()

onMounted(async () => {
  // Initialize authentication
  await authStore.initializeAuth()

  // Initialize real-time listeners if authenticated
  if (authStore.isAuthenticated) {
    itemsStore.initializeRealTimeListeners()
    rentalsStore.initializeRealTimeListeners(authStore.currentUser?.uid)
  }
})

onUnmounted(() => {
  // Clean up listeners
  itemsStore.stopRealTimeListeners()
  rentalsStore.stopRealTimeListeners()
})
</script>

<template>
  <div id="app">
    <!-- Toast for notifications -->
    <Toast />

    <!-- Confirmation dialog -->
    <ConfirmDialog />

    <!-- Loading overlay -->
    <div v-if="authStore.loading" class="loading-overlay">
      <ProgressSpinner />
    </div>

    <!-- Main layout for authenticated users -->
    <div v-if="authStore.isAuthenticated" class="app-layout">
      <AppHeader />
      <div class="app-content">
        <AppSidebar />
        <main class="main-content">
          <RouterView />
        </main>
      </div>
    </div>

    <!-- Login layout for unauthenticated users -->
    <div v-else class="login-layout">
      <RouterView />
    </div>
  </div>
</template>

<style>
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  color: #2c3e50;
  min-height: 100vh;
}

#app {
  min-height: 100vh;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  color: white;
}

.loading-overlay .p-progress-spinner {
  margin-bottom: 1rem;
}

.app-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.app-content {
  display: flex;
  flex: 1;
  max-width: 1600px;
  margin: 0 auto;
  width: 100%;
  background: white;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
  border-radius: 0 0 16px 16px;
  overflow: hidden;
}

.main-content {
  flex: 1;
  padding: 2rem;
  background: #f8f9fa;
  overflow-y: auto;
  min-height: calc(100vh - 140px);
}

.login-layout {
  min-height: 100vh;
}

/* Professional scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 6px;
  border: 2px solid #f1f3f4;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Professional focus styles */
*:focus-visible {
  outline: 2px solid #667eea;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Professional animations */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Desktop optimizations */
@media (min-width: 1200px) {
  .main-content {
    padding: 2.5rem;
  }

  .app-content {
    margin: 1rem auto;
    border-radius: 16px;
    max-width: 1800px;
  }
}

@media (max-width: 1024px) {
  .app-content {
    margin: 0;
    border-radius: 0;
  }

  .main-content {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }
}
</style>
