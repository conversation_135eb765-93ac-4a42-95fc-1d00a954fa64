<template>
  <div class="date-selector">
    <div class="date-mode-selector">
      <div class="mode-buttons">
        <Button
          label="Single Date"
          :class="{ 'p-button-outlined': dateMode !== 'single' }"
          @click="setDateMode('single')"
          size="small"
          :disabled="disabled"
        />
        <Button
          label="Multiple Dates"
          :class="{ 'p-button-outlined': dateMode !== 'multiple' }"
          @click="setDateMode('multiple')"
          size="small"
          :disabled="disabled"
        />
        <Button
          label="Date Range"
          :class="{ 'p-button-outlined': dateMode !== 'range' }"
          @click="setDateMode('range')"
          size="small"
          :disabled="disabled"
        />
      </div>
      
      <div class="selected-dates-info" v-if="selectedDates.length > 0">
        <span class="dates-count">{{ selectedDates.length }} date(s) selected</span>
        <Button
          icon="pi pi-times"
          class="p-button-text p-button-sm"
          @click="clearDates"
          :disabled="disabled"
          v-tooltip="'Clear all dates'"
        />
      </div>
    </div>

    <!-- Calendar Component -->
    <div class="calendar-container">
      <Calendar
        v-model="calendarValue"
        :selection-mode="calendarSelectionMode"
        :inline="true"
        :disabled-dates="disabledDates"
        :min-date="minDate"
        :max-date="maxDate"
        @date-select="onDateSelect"
        @update:modelValue="onCalendarUpdate"
        :disabled="disabled"
        class="custom-calendar"
      />
    </div>

    <!-- Selected Dates Display -->
    <div class="selected-dates-display" v-if="selectedDates.length > 0">
      <h4>Selected Dates:</h4>
      <div class="dates-list">
        <Tag
          v-for="date in formattedSelectedDates"
          :key="date.value"
          :value="date.label"
          severity="info"
          class="date-tag"
        >
          <template #default>
            {{ date.label }}
            <Button
              icon="pi pi-times"
              class="p-button-text p-button-sm remove-date-btn"
              @click="removeDate(date.value)"
              :disabled="disabled"
            />
          </template>
        </Tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { format, parseISO, addDays, eachDayOfInterval } from 'date-fns'

interface Props {
  modelValue: string[]
  disabled?: boolean
  minDaysAhead?: number
  maxDaysAhead?: number
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  minDaysAhead: 0,
  maxDaysAhead: 365
})

const emit = defineEmits<Emits>()

// Reactive state
const dateMode = ref<'single' | 'multiple' | 'range'>('single')
const calendarValue = ref<Date | Date[] | null>(null)
const selectedDates = ref<string[]>([...props.modelValue])

// Computed properties
const calendarSelectionMode = computed(() => {
  switch (dateMode.value) {
    case 'single':
      return 'single'
    case 'multiple':
      return 'multiple'
    case 'range':
      return 'range'
    default:
      return 'single'
  }
})

const minDate = computed(() => {
  const today = new Date()
  return addDays(today, props.minDaysAhead)
})

const maxDate = computed(() => {
  const today = new Date()
  return addDays(today, props.maxDaysAhead)
})

const disabledDates = computed(() => {
  const today = new Date()
  const pastDates: Date[] = []
  
  // Disable past dates (before minDate)
  for (let i = -30; i < props.minDaysAhead; i++) {
    pastDates.push(addDays(today, i))
  }
  
  return pastDates
})

const formattedSelectedDates = computed(() => {
  return selectedDates.value
    .sort()
    .map(dateStr => ({
      value: dateStr,
      label: format(parseISO(dateStr), 'MMM dd, yyyy')
    }))
})

// Methods
const setDateMode = (mode: 'single' | 'multiple' | 'range') => {
  dateMode.value = mode
  clearDates()
}

const clearDates = () => {
  selectedDates.value = []
  calendarValue.value = null
  emit('update:modelValue', [])
}

const removeDate = (dateStr: string) => {
  const index = selectedDates.value.indexOf(dateStr)
  if (index > -1) {
    selectedDates.value.splice(index, 1)
    updateCalendarValue()
    emit('update:modelValue', [...selectedDates.value])
  }
}

const onDateSelect = (event: any) => {
  // Handle date selection based on mode
  try {
    if (!event) return

    if (dateMode.value === 'single') {
      const dateStr = format(event, 'yyyy-MM-dd')
      selectedDates.value = [dateStr]
      calendarValue.value = event
    } else if (dateMode.value === 'multiple') {
      const dateStr = format(event, 'yyyy-MM-dd')
      const index = selectedDates.value.indexOf(dateStr)

      if (index > -1) {
        // Date already selected, remove it
        selectedDates.value.splice(index, 1)
      } else {
        // Add new date
        selectedDates.value.push(dateStr)
      }

      // Update calendar value for multiple selection
      calendarValue.value = selectedDates.value.map(dateStr => parseISO(dateStr))
    }

    emit('update:modelValue', [...selectedDates.value])
  } catch (error) {
    console.error('Error in onDateSelect:', error)
  }
}

const onCalendarUpdate = (value: Date | Date[] | (Date | null)[] | null | undefined) => {
  try {
    if (!value || value === undefined) {
      return // Don't clear dates on undefined, let onDateSelect handle it
    }

    if (dateMode.value === 'single' && value instanceof Date) {
      const dateStr = format(value, 'yyyy-MM-dd')
      selectedDates.value = [dateStr]
    } else if (dateMode.value === 'multiple' && Array.isArray(value)) {
      // Filter out null/undefined values and convert to date strings
      const validDates = value.filter(date => date !== null && date !== undefined) as Date[]
      selectedDates.value = validDates.map(date => format(date, 'yyyy-MM-dd'))
    } else if (dateMode.value === 'range' && Array.isArray(value) && value.length === 2) {
      // Generate all dates in the range
      const [startDate, endDate] = value
      if (startDate && endDate) {
        const datesInRange = eachDayOfInterval({ start: startDate, end: endDate })
        selectedDates.value = datesInRange.map(date => format(date, 'yyyy-MM-dd'))
      }
    }

    emit('update:modelValue', [...selectedDates.value])
  } catch (error) {
    console.error('Error in onCalendarUpdate:', error)
    // Don't clear dates on error, just log it
  }
}

const updateCalendarValue = () => {
  if (selectedDates.value.length === 0) {
    calendarValue.value = null
    return
  }

  if (dateMode.value === 'single') {
    calendarValue.value = parseISO(selectedDates.value[0])
  } else if (dateMode.value === 'multiple') {
    calendarValue.value = selectedDates.value.map(dateStr => parseISO(dateStr))
  } else if (dateMode.value === 'range' && selectedDates.value.length >= 2) {
    const sortedDates = [...selectedDates.value].sort()
    calendarValue.value = [
      parseISO(sortedDates[0]),
      parseISO(sortedDates[sortedDates.length - 1])
    ]
  }
}

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  selectedDates.value = [...newValue]
  updateCalendarValue()
}, { immediate: true })
</script>

<style scoped>
.date-selector {
  width: 100%;
}

.date-mode-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.mode-buttons {
  display: flex;
  gap: 0.5rem;
}

.selected-dates-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dates-count {
  font-size: 0.9rem;
  color: var(--text-color-secondary);
  font-weight: 500;
}

.calendar-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.custom-calendar {
  width: 100%;
}

.selected-dates-display h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  color: var(--text-color);
  font-weight: 600;
}

.dates-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.date-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.remove-date-btn {
  padding: 0.125rem;
  width: 1.25rem;
  height: 1.25rem;
  min-width: unset;
}

@media (max-width: 768px) {
  .date-mode-selector {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .mode-buttons {
    justify-content: center;
  }
  
  .selected-dates-info {
    justify-content: center;
  }
}
</style>
