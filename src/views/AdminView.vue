<template>
  <div class="admin-container">
    <div class="page-header">
      <h1>Admin Dashboard</h1>
      <p class="page-description">
        Manage items, view all rentals, and handle administrative tasks.
      </p>
    </div>

    <!-- Admin Tabs -->
    <div class="admin-tabs">
      <Button
        v-for="tab in adminTabs"
        :key="tab.value"
        :label="tab.label"
        :icon="tab.icon"
        :class="{ 'p-button-outlined': selectedTab !== tab.value }"
        @click="setSelectedTab(tab.value)"
      />
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Items Management -->
      <div v-if="selectedTab === 'items'" class="tab-panel">
        <div class="panel-header">
          <h2>Items Management</h2>
          <div class="header-actions">
            <Button
              label="Refresh Data"
              icon="pi pi-refresh"
              severity="secondary"
              outlined
              @click="refreshItemsData"
              :loading="loadingItems"
            />
            <Button
              label="Add New Item Group"
              icon="pi pi-plus"
              @click="showAddItemDialog = true"
            />
          </div>
        </div>

        <!-- Items Grid Layout -->
        <div class="items-grid">
          <Card
            v-for="itemGroup in allItems"
            :key="itemGroup.id"
            class="item-card"
          >
            <template #header>
              <div class="item-card-header">
                <div class="item-info">
                  <h3 class="item-name">{{ itemGroup.name }}</h3>
                  <Badge
                    :value="formatCategory(itemGroup.type)"
                    :severity="getCategorySeverity(itemGroup.type)"
                  />
                </div>
                <div class="item-actions">
                  <Button
                    icon="pi pi-pencil"
                    class="p-button-text p-button-sm"
                    @click="editItemGroup(itemGroup)"
                    v-tooltip="'Edit item group'"
                  />
                  <Button
                    icon="pi pi-eye"
                    class="p-button-text p-button-sm"
                    @click="viewItemDetails(itemGroup)"
                    v-tooltip="'View details'"
                  />
                </div>
              </div>
            </template>

            <template #content>
              <div class="item-content">
                <!-- Item Statistics -->
                <div class="item-stats">
                  <div class="stat-item">
                    <span class="stat-label">Total Items:</span>
                    <span class="stat-value">{{ itemGroup.items?.length || 0 }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">Available:</span>
                    <span class="stat-value available">{{ getAvailableCount(itemGroup) }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">Suspended:</span>
                    <span class="stat-value suspended">{{ getSuspendedCount(itemGroup) }}</span>
                  </div>
                </div>

                <!-- Individual Items List -->
                <div class="individual-items">
                  <h4>Individual Items</h4>
                  <div class="items-list">
                    <div
                      v-for="(item, index) in (itemGroup.items || [])"
                      :key="item.id || index"
                      class="individual-item"
                      :class="{ 'suspended': item.suspended }"
                    >
                      <div class="item-details">
                        <span class="item-serial">{{ item.serial_number || `Item ${index + 1}` }}</span>
                        <Badge
                          v-if="item.suspended"
                          value="Suspended"
                          severity="danger"
                          class="item-status"
                        />
                      </div>
                      <div class="item-controls">
                        <Button
                          :icon="item.suspended ? 'pi pi-check' : 'pi pi-ban'"
                          :class="item.suspended ? 'p-button-success' : 'p-button-warning'"
                          class="p-button-text p-button-sm"
                          @click="toggleItemSuspension(itemGroup, item)"
                          :v-tooltip="item.suspended ? 'Reactivate item' : 'Suspend item'"
                        />
                        <Button
                          icon="pi pi-pencil"
                          class="p-button-text p-button-sm"
                          @click="editIndividualItem(itemGroup, item)"
                          v-tooltip="'Edit item details'"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Baseline Quantity Management -->
                <div class="quantity-management">
                  <h4>Baseline Quantity Management</h4>
                  <div class="quantity-controls">
                    <div class="quantity-display">
                      <label>Current Baseline:</label>
                      <span class="current-quantity">{{ itemGroup.items?.length || 0 }} items</span>
                    </div>
                    <div class="quantity-actions">
                      <Button
                        label="Add Items"
                        icon="pi pi-plus"
                        size="small"
                        @click="showAddItemsDialog(itemGroup)"
                      />
                      <Button
                        label="Remove Items"
                        icon="pi pi-minus"
                        severity="secondary"
                        size="small"
                        @click="showRemoveItemsDialog(itemGroup)"
                        :disabled="!itemGroup.items?.length"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </Card>
        </div>

        <!-- Empty State -->
        <div v-if="!allItems.length && !loadingItems" class="empty-state">
          <i class="pi pi-box empty-icon"></i>
          <h3>No Items Found</h3>
          <p>Start by adding your first item group to manage inventory.</p>
          <Button
            label="Add First Item Group"
            icon="pi pi-plus"
            @click="showAddItemDialog = true"
          />
        </div>
      </div>

      <!-- All Rentals -->
      <div v-if="selectedTab === 'rentals'" class="tab-panel">
        <div class="panel-header">
          <h2>All Rentals</h2>
          <div class="rental-filters">
            <Dropdown
              v-model="selectedRentalStatus"
              :options="rentalStatusOptions"
              option-label="label"
              option-value="value"
              placeholder="Filter by status"
              class="filter-dropdown"
            />
            <Calendar
              v-model="selectedDateRange"
              selection-mode="range"
              :manual-input="false"
              placeholder="Filter by date range"
              class="filter-calendar"
            />
          </div>
        </div>
        
        <DataTable 
          :value="filteredRentals" 
          :loading="loadingRentals"
          paginator 
          :rows="15"
          class="admin-table"
          responsive-layout="scroll"
        >
          <Column field="id" header="Episode ID" sortable>
            <template #body="{ data }">
              <code class="episode-id">{{ data.id }}</code>
            </template>
          </Column>
          <Column field="user_employee_number" header="Employee" sortable />
          <Column field="status" header="Status" sortable>
            <template #body="{ data }">
              <Badge 
                :value="data.status" 
                :severity="getStatusSeverity(data.status)"
              />
            </template>
          </Column>
          <Column header="Items">
            <template #body="{ data }">
              {{ data.items?.length || 0 }} item(s)
            </template>
          </Column>
          <Column header="Dates">
            <template #body="{ data }">
              {{ formatDateRange(data.dates) }}
            </template>
          </Column>
          <Column field="created_at" header="Created" sortable>
            <template #body="{ data }">
              {{ formatDate(data.created_at) }}
            </template>
          </Column>
          <Column header="Actions">
            <template #body="{ data }">
              <Button
                icon="pi pi-eye"
                class="p-button-text p-button-sm"
                @click="viewRentalDetails(data)"
                v-tooltip="'View details'"
              />
            </template>
          </Column>
        </DataTable>
      </div>

      <!-- System Stats -->
      <div v-if="selectedTab === 'stats'" class="tab-panel">
        <div class="panel-header">
          <h2>System Statistics</h2>
        </div>
        
        <div class="stats-grid">
          <Card class="stat-card">
            <template #title>Total Items</template>
            <template #content>
              <div class="stat-value">{{ totalItemsCount }}</div>
              <div class="stat-label">Item groups in system</div>
            </template>
          </Card>
          
          <Card class="stat-card">
            <template #title>Active Rentals</template>
            <template #content>
              <div class="stat-value">{{ activeRentalsCount }}</div>
              <div class="stat-label">Currently rented episodes</div>
            </template>
          </Card>
          
          <Card class="stat-card">
            <template #title>Total Users</template>
            <template #content>
              <div class="stat-value">{{ totalUsersCount }}</div>
              <div class="stat-label">Registered users</div>
            </template>
          </Card>
          
          <Card class="stat-card">
            <template #title>This Month</template>
            <template #content>
              <div class="stat-value">{{ thisMonthRentalsCount }}</div>
              <div class="stat-label">Rentals this month</div>
            </template>
          </Card>
        </div>
      </div>

      <!-- User Management -->
      <div v-if="selectedTab === 'users'" class="tab-panel">
        <div class="panel-header">
          <h2>User Management</h2>
        </div>
        
        <DataTable 
          :value="allUsers" 
          :loading="loadingUsers"
          paginator 
          :rows="20"
          class="admin-table"
          responsive-layout="scroll"
        >
          <Column field="employee_number" header="Employee Number" sortable />
          <Column field="name" header="Name" sortable />
          <Column field="email" header="Email" sortable />
          <Column field="department" header="Department" sortable />
          <Column header="Total Rentals">
            <template #body="{ data }">
              {{ getUserRentalCount(data.employee_number) }}
            </template>
          </Column>
          <Column field="created_at" header="Joined" sortable>
            <template #body="{ data }">
              {{ formatDate(data.created_at) }}
            </template>
          </Column>
          <Column header="Actions">
            <template #body="{ data }">
              <Button
                icon="pi pi-eye"
                class="p-button-text p-button-sm"
                @click="viewUserDetails(data)"
                v-tooltip="'View user details'"
              />
            </template>
          </Column>
        </DataTable>
      </div>
    </div>

    <!-- Dialogs -->
    <Dialog
      v-model:visible="showAddItemDialog"
      header="Add New Item Group"
      modal
      style="width: 50vw; min-width: 400px;"
    >
      <p>Item creation functionality would be implemented here.</p>
      <div class="dialog-actions">
        <Button label="Cancel" class="p-button-outlined" @click="showAddItemDialog = false" />
        <Button label="Create" @click="showAddItemDialog = false" />
      </div>
    </Dialog>

    <!-- Add Items Dialog -->
    <Dialog
      v-model:visible="showAddItemsDialogVisible"
      header="Add Items to Group"
      modal
      style="width: 40vw; min-width: 350px;"
    >
      <div class="dialog-content">
        <div class="form-group">
          <label for="itemsToAdd">Number of items to add:</label>
          <InputNumber
            id="itemsToAdd"
            v-model="itemsToAdd"
            :min="1"
            :max="50"
            show-buttons
            class="quantity-input"
          />
        </div>
        <div class="form-group">
          <label for="startingSerial">Starting serial number (optional):</label>
          <InputText
            id="startingSerial"
            v-model="startingSerialNumber"
            placeholder="e.g., SN001"
            class="serial-input"
          />
        </div>
        <div class="info-message">
          <i class="pi pi-info-circle"></i>
          <span>This will add {{ itemsToAdd }} new items to "{{ selectedItemGroup?.name }}"</span>
        </div>
      </div>
      <div class="dialog-actions">
        <Button
          label="Cancel"
          class="p-button-outlined"
          @click="cancelAddItems"
        />
        <Button
          label="Add Items"
          icon="pi pi-plus"
          @click="confirmAddItems"
          :disabled="!itemsToAdd || itemsToAdd < 1"
        />
      </div>
    </Dialog>

    <!-- Remove Items Dialog -->
    <Dialog
      v-model:visible="showRemoveItemsDialogVisible"
      header="Remove Items from Group"
      modal
      style="width: 40vw; min-width: 350px;"
    >
      <div class="dialog-content">
        <div class="form-group">
          <label for="itemsToRemove">Number of items to remove:</label>
          <InputNumber
            id="itemsToRemove"
            v-model="itemsToRemove"
            :min="1"
            :max="selectedItemGroup?.items?.length || 1"
            show-buttons
            class="quantity-input"
          />
        </div>
        <div class="warning-message">
          <i class="pi pi-exclamation-triangle"></i>
          <span>This will permanently remove {{ itemsToRemove }} items from "{{ selectedItemGroup?.name }}". Only non-suspended items will be removed first.</span>
        </div>
      </div>
      <div class="dialog-actions">
        <Button
          label="Cancel"
          class="p-button-outlined"
          @click="cancelRemoveItems"
        />
        <Button
          label="Remove Items"
          icon="pi pi-minus"
          severity="danger"
          @click="confirmRemoveItems"
          :disabled="!itemsToRemove || itemsToRemove < 1"
        />
      </div>
    </Dialog>

    <!-- Edit Individual Item Dialog -->
    <Dialog
      v-model:visible="showEditItemDialogVisible"
      header="Edit Item Details"
      modal
      style="width: 40vw; min-width: 350px;"
    >
      <div class="dialog-content" v-if="selectedIndividualItem">
        <div class="form-group">
          <label for="serialNumber">Serial Number:</label>
          <InputText
            id="serialNumber"
            v-model="editingSerialNumber"
            placeholder="Enter serial number"
            class="serial-input"
          />
        </div>
        <div class="form-group">
          <label for="itemNotes">Notes:</label>
          <Textarea
            id="itemNotes"
            v-model="editingItemNotes"
            placeholder="Add any notes about this item"
            rows="3"
            class="notes-input"
          />
        </div>
        <div class="form-group">
          <div class="checkbox-group">
            <Checkbox
              id="suspendItem"
              v-model="editingItemSuspended"
              binary
            />
            <label for="suspendItem">Suspend this item</label>
          </div>
        </div>
      </div>
      <div class="dialog-actions">
        <Button
          label="Cancel"
          class="p-button-outlined"
          @click="cancelEditItem"
        />
        <Button
          label="Save Changes"
          icon="pi pi-check"
          @click="confirmEditItem"
        />
      </div>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import { useItemsStore } from '@/stores/items'
import { useEpisodesStore } from '@/stores/episodes'
import { useAuthStore } from '@/stores/auth'
import { format } from 'date-fns'

// PrimeVue Components
import Button from 'primevue/button'
import Card from 'primevue/card'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Badge from 'primevue/badge'
import Dialog from 'primevue/dialog'
import InputNumber from 'primevue/inputnumber'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import Checkbox from 'primevue/checkbox'
import Dropdown from 'primevue/dropdown'
import Calendar from 'primevue/calendar'

// Stores
const itemsStore = useItemsStore()
const episodesStore = useEpisodesStore()
const authStore = useAuthStore()
const toast = useToast()

// Reactive state
const selectedTab = ref<'items' | 'rentals' | 'stats' | 'users'>('items')
const loadingItems = ref(false)
const loadingRentals = ref(false)
const loadingUsers = ref(false)
const showAddItemDialog = ref(false)
const selectedRentalStatus = ref<string | null>(null)
const selectedDateRange = ref<Date[] | null>(null)

// Items management dialogs
const showAddItemsDialogVisible = ref(false)
const showRemoveItemsDialogVisible = ref(false)
const showEditItemDialogVisible = ref(false)
const selectedItemGroup = ref<any>(null)
const selectedIndividualItem = ref<any>(null)

// Form data for dialogs
const itemsToAdd = ref(1)
const itemsToRemove = ref(1)
const startingSerialNumber = ref('')
const editingSerialNumber = ref('')
const editingItemNotes = ref('')
const editingItemSuspended = ref(false)

// Mock data (in real implementation, these would come from stores)
const allItems = computed(() => itemsStore.itemGroups)
const allUsers = ref<any[]>([]) // Would be populated from a users store
const allRentals = ref<any[]>([]) // Would be populated from episodes store

// Tab configuration
const adminTabs = [
  { label: 'Items', value: 'items' as const, icon: 'pi pi-box' },
  { label: 'Rentals', value: 'rentals' as const, icon: 'pi pi-calendar' },
  { label: 'Statistics', value: 'stats' as const, icon: 'pi pi-chart-bar' },
  { label: 'Users', value: 'users' as const, icon: 'pi pi-users' }
]

const rentalStatusOptions = [
  { label: 'All Statuses', value: null },
  { label: 'Scheduled', value: 'scheduled' },
  { label: 'Rented', value: 'rented' },
  { label: 'Returned', value: 'returned' },
  { label: 'Cancelled', value: 'cancelled' }
]

// Computed properties
const filteredRentals = computed(() => {
  let rentals = allRentals.value
  
  if (selectedRentalStatus.value) {
    rentals = rentals.filter(rental => rental.status === selectedRentalStatus.value)
  }
  
  // Date range filtering would be implemented here
  
  return rentals
})

const totalItemsCount = computed(() => allItems.value.length)
const activeRentalsCount = computed(() => 
  allRentals.value.filter(rental => rental.status === 'rented').length
)
const totalUsersCount = computed(() => allUsers.value.length)
const thisMonthRentalsCount = computed(() => {
  const now = new Date()
  const thisMonth = now.getMonth()
  const thisYear = now.getFullYear()
  
  return allRentals.value.filter(rental => {
    const createdDate = new Date(rental.created_at)
    return createdDate.getMonth() === thisMonth && createdDate.getFullYear() === thisYear
  }).length
})

// Methods
const setSelectedTab = (tab: 'items' | 'rentals' | 'stats' | 'users') => {
  selectedTab.value = tab
}

const formatCategory = (category: string) => {
  switch (category) {
    case 'resus_trainings':
      return 'Resuscitating Training'
    case 'venues':
      return 'Venue'
    case 'audio_visuals':
      return 'Audio Visual'
    default:
      return category
  }
}

const getAvailableCount = (itemGroup: any) => {
  // This would use the actual availability checking logic
  return itemGroup.items?.filter((item: any) => !item.suspended).length || 0
}

const getSuspendedCount = (itemGroup: any) => {
  return itemGroup.items?.filter((item: any) => item.suspended).length || 0
}

const getCategorySeverity = (type: string) => {
  switch (type) {
    case 'resus_trainings':
      return 'info'
    case 'venues':
      return 'success'
    case 'audio_visuals':
      return 'warning'
    default:
      return 'info'
  }
}

const refreshItemsData = async () => {
  loadingItems.value = true
  try {
    await itemsStore.fetchItemGroups()
    toast.add({
      severity: 'success',
      summary: 'Data Refreshed',
      detail: 'Items data has been refreshed successfully',
      life: 3000
    })
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Refresh Failed',
      detail: 'Failed to refresh items data',
      life: 5000
    })
  } finally {
    loadingItems.value = false
  }
}

const getStatusSeverity = (status: string) => {
  switch (status) {
    case 'scheduled':
      return 'info'
    case 'rented':
      return 'warning'
    case 'returned':
      return 'success'
    case 'cancelled':
      return 'danger'
    default:
      return 'info'
  }
}

const formatDate = (date: Date | string) => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return format(dateObj, 'MMM dd, yyyy')
}

const formatDateRange = (dates: string[]) => {
  if (!dates || dates.length === 0) return 'No dates'
  if (dates.length === 1) return formatDate(dates[0])
  
  const sortedDates = [...dates].sort()
  return `${formatDate(sortedDates[0])} - ${formatDate(sortedDates[sortedDates.length - 1])}`
}

const getUserRentalCount = (employeeNumber: string) => {
  return allRentals.value.filter(rental => rental.user_employee_number === employeeNumber).length
}

const editItemGroup = (itemGroup: any) => {
  toast.add({
    severity: 'info',
    summary: 'Edit Item Group',
    detail: `Edit functionality for ${itemGroup.name} would be implemented here`,
    life: 3000
  })
}

// Quantity management methods
const showAddItemsDialog = (itemGroup: any) => {
  selectedItemGroup.value = itemGroup
  itemsToAdd.value = 1
  startingSerialNumber.value = ''
  showAddItemsDialogVisible.value = true
}

const showRemoveItemsDialog = (itemGroup: any) => {
  selectedItemGroup.value = itemGroup
  itemsToRemove.value = 1
  showRemoveItemsDialogVisible.value = true
}

const cancelAddItems = () => {
  showAddItemsDialogVisible.value = false
  selectedItemGroup.value = null
  itemsToAdd.value = 1
  startingSerialNumber.value = ''
}

const cancelRemoveItems = () => {
  showRemoveItemsDialogVisible.value = false
  selectedItemGroup.value = null
  itemsToRemove.value = 1
}

const confirmAddItems = () => {
  if (!selectedItemGroup.value || !itemsToAdd.value) return

  // In a real implementation, this would call the store to add items
  toast.add({
    severity: 'success',
    summary: 'Items Added',
    detail: `Added ${itemsToAdd.value} items to ${selectedItemGroup.value.name}`,
    life: 3000
  })

  cancelAddItems()
}

const confirmRemoveItems = () => {
  if (!selectedItemGroup.value || !itemsToRemove.value) return

  // In a real implementation, this would call the store to remove items
  toast.add({
    severity: 'success',
    summary: 'Items Removed',
    detail: `Removed ${itemsToRemove.value} items from ${selectedItemGroup.value.name}`,
    life: 3000
  })

  cancelRemoveItems()
}

const viewItemDetails = (itemGroup: any) => {
  toast.add({
    severity: 'info',
    summary: 'Item Details',
    detail: `Detailed view for ${itemGroup.name} would be implemented here`,
    life: 3000
  })
}

// Individual item management
const toggleItemSuspension = (itemGroup: any, item: any) => {
  // In a real implementation, this would update the item in the store
  toast.add({
    severity: 'info',
    summary: item.suspended ? 'Item Reactivated' : 'Item Suspended',
    detail: `${item.serial_number || 'Item'} has been ${item.suspended ? 'reactivated' : 'suspended'}`,
    life: 3000
  })
}

const editIndividualItem = (itemGroup: any, item: any) => {
  selectedItemGroup.value = itemGroup
  selectedIndividualItem.value = item
  editingSerialNumber.value = item.serial_number || ''
  editingItemNotes.value = item.notes || ''
  editingItemSuspended.value = item.suspended || false
  showEditItemDialogVisible.value = true
}

const cancelEditItem = () => {
  showEditItemDialogVisible.value = false
  selectedItemGroup.value = null
  selectedIndividualItem.value = null
  editingSerialNumber.value = ''
  editingItemNotes.value = ''
  editingItemSuspended.value = false
}

const confirmEditItem = () => {
  if (!selectedIndividualItem.value) return

  // In a real implementation, this would update the item in the store
  toast.add({
    severity: 'success',
    summary: 'Item Updated',
    detail: `Item details have been updated successfully`,
    life: 3000
  })

  cancelEditItem()
}

const viewRentalDetails = (rental: any) => {
  toast.add({
    severity: 'info',
    summary: 'Rental Details',
    detail: `Detailed view for episode ${rental.id} would be implemented here`,
    life: 3000
  })
}

const viewUserDetails = (user: any) => {
  toast.add({
    severity: 'info',
    summary: 'User Details',
    detail: `Detailed view for ${user.name} would be implemented here`,
    life: 3000
  })
}

// Lifecycle
onMounted(async () => {
  // Load initial data
  loadingItems.value = true
  try {
    await itemsStore.initializeRealTimeListeners()
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Loading Error',
      detail: 'Failed to load items data',
      life: 5000
    })
  } finally {
    loadingItems.value = false
  }
})
</script>

<style scoped>
.admin-container {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 2rem;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 2.5rem;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
}

.admin-tabs {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--surface-ground);
  border-radius: 8px;
}

.tab-content {
  background: var(--surface-card);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tab-panel {
  padding: 1.5rem;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.panel-header h2 {
  margin: 0;
  color: var(--text-color);
  font-size: 1.5rem;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

/* Items Grid Layout */
.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.item-card {
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.item-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.item-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.25rem;
  background: linear-gradient(135deg, var(--surface-50) 0%, var(--surface-100) 100%);
  border-bottom: 1px solid var(--surface-border);
}

.item-info h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 1.25rem;
  font-weight: 600;
}

.item-actions {
  display: flex;
  gap: 0.25rem;
}

.item-content {
  padding: 1.25rem;
}

.item-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--surface-50);
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.85rem;
  color: var(--text-color-secondary);
  margin-bottom: 0.25rem;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
}

.stat-value.available {
  color: var(--green-500);
}

.stat-value.suspended {
  color: var(--red-500);
}

/* Individual Items Section */
.individual-items {
  margin-bottom: 1.5rem;
}

.individual-items h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-size: 1rem;
  font-weight: 600;
}

.items-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--surface-border);
  border-radius: 6px;
}

.individual-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--surface-border);
  transition: background-color 0.2s ease;
}

.individual-item:last-child {
  border-bottom: none;
}

.individual-item:hover {
  background: var(--surface-50);
}

.individual-item.suspended {
  background: var(--red-50);
  opacity: 0.7;
}

.item-details {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.item-serial {
  font-weight: 500;
  color: var(--text-color);
}

.item-controls {
  display: flex;
  gap: 0.25rem;
}

/* Quantity Management Section */
.quantity-management {
  border-top: 1px solid var(--surface-border);
  padding-top: 1.5rem;
}

.quantity-management h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-size: 1rem;
  font-weight: 600;
}

.quantity-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--surface-50);
  border-radius: 8px;
}

.quantity-display {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.quantity-display label {
  font-size: 0.85rem;
  color: var(--text-color-secondary);
  font-weight: 500;
}

.current-quantity {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
}

.quantity-actions {
  display: flex;
  gap: 0.5rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--text-color-secondary);
}

.empty-icon {
  font-size: 4rem;
  color: var(--surface-400);
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 1.5rem;
}

.empty-state p {
  margin: 0 0 2rem 0;
  font-size: 1rem;
}

.rental-filters {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.filter-dropdown,
.filter-calendar {
  min-width: 200px;
}

.admin-table {
  margin-top: 1rem;
}

.episode-id {
  font-family: 'Courier New', monospace;
  background: var(--surface-100);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  text-align: center;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

/* Dialog Styles */
.dialog-content {
  padding: 0.5rem 0;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.quantity-input,
.serial-input,
.notes-input {
  width: 100%;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-message,
.warning-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.9rem;
}

.info-message {
  background: var(--blue-50);
  color: var(--blue-700);
  border: 1px solid var(--blue-200);
}

.warning-message {
  background: var(--orange-50);
  color: var(--orange-700);
  border: 1px solid var(--orange-200);
}

.dialog-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

@media (max-width: 768px) {
  .admin-container {
    padding: 1rem;
  }

  .admin-tabs {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .panel-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .items-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .item-card-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .item-stats {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .quantity-controls {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .quantity-actions {
    justify-content: center;
  }

  .rental-filters {
    flex-direction: column;
    gap: 0.75rem;
  }

  .filter-dropdown,
  .filter-calendar {
    min-width: unset;
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .individual-item {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .item-controls {
    justify-content: center;
  }
}
</style>
