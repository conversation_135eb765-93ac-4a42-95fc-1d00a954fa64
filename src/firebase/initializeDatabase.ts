import { collection, doc, setDoc, getDocs } from 'firebase/firestore'
import { db, isDemoMode } from './config'

// Database structure for the rental platform
export const databaseStructure = {
  categories: [
    {
      id: 'resuscitation-training',
      name: 'Resuscitation Training Equipment',
      type: 'resus_trainings',
      description: 'Equipment for resuscitation and emergency response training',
      icon: 'pi pi-heart',
      order: 1
    },
    {
      id: 'training-venue',
      name: 'Training Venue',
      type: 'venues',
      description: 'Training venues and meeting rooms',
      icon: 'pi pi-building',
      order: 2
    },
    {
      id: 'audio-visual',
      name: 'Audio Visual Equipment',
      type: 'audio_visuals',
      description: 'Audio visual equipment for presentations and training',
      icon: 'pi pi-video',
      order: 3
    }
  ],
  itemGroups: [
    // Resuscitation Training Equipment
    {
      id: 'manikin-group',
      name: 'Manikin',
      category_id: 'resuscitation-training',
      type: 'resus_trainings',
      description: 'Training manikins for CPR and resuscitation practice',
      image_url: '',
      items: [
        {
          id: 'manikin-001',
          serial_number: 'MAN-001',
          condition: 'excellent',
          notes: 'Adult CPR training manikin',
          status: 'available'
        },
        {
          id: 'manikin-002',
          serial_number: 'MAN-002',
          condition: 'good',
          notes: 'Adult CPR training manikin',
          status: 'available'
        }
      ],
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 'sim-train-group',
      name: 'Sim Train Equipment',
      category_id: 'resuscitation-training',
      type: 'resus_trainings',
      description: 'Simulation training equipment for advanced scenarios',
      image_url: '',
      items: [
        {
          id: 'sim-001',
          serial_number: 'SIM-001',
          condition: 'excellent',
          notes: 'Advanced simulation training system',
          status: 'available'
        }
      ],
      created_at: new Date(),
      updated_at: new Date()
    },
    // Training Venue
    {
      id: 'rtrc-group',
      name: 'RTRC',
      category_id: 'training-venue',
      type: 'venues',
      description: 'Resuscitation Training and Research Centre',
      image_url: '',
      items: [
        {
          id: 'rtrc-001',
          serial_number: 'RTRC-MAIN',
          condition: 'excellent',
          notes: 'Main training room with full AV setup',
          status: 'available'
        }
      ],
      created_at: new Date(),
      updated_at: new Date()
    },
    // Audio Visual Equipment
    {
      id: 'smv-tv-group',
      name: 'SMV TV',
      category_id: 'audio-visual',
      type: 'audio_visuals',
      description: 'Smart TV for presentations and training videos',
      image_url: '',
      items: [
        {
          id: 'tv-001',
          serial_number: 'SMV-TV-001',
          condition: 'excellent',
          notes: '55" Smart TV with wireless casting',
          status: 'available'
        }
      ],
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 'phone-stabilizer-group',
      name: 'Mobile Phone Stabilizer',
      category_id: 'audio-visual',
      type: 'audio_visuals',
      description: 'Gimbal stabilizer for mobile phone video recording',
      image_url: '',
      items: [
        {
          id: 'stabilizer-001',
          serial_number: 'STAB-001',
          condition: 'good',
          notes: '3-axis gimbal stabilizer',
          status: 'available'
        },
        {
          id: 'stabilizer-002',
          serial_number: 'STAB-002',
          condition: 'excellent',
          notes: '3-axis gimbal stabilizer',
          status: 'available'
        }
      ],
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 'digital-camera-group',
      name: 'Digital Camera',
      category_id: 'audio-visual',
      type: 'audio_visuals',
      description: 'Professional digital camera for training documentation',
      image_url: '',
      items: [
        {
          id: 'camera-001',
          serial_number: 'CAM-001',
          condition: 'excellent',
          notes: 'DSLR camera with lens kit',
          status: 'available'
        }
      ],
      created_at: new Date(),
      updated_at: new Date()
    }
  ]
}

export async function initializeDatabase() {
  if (isDemoMode || !db) {
    console.log('Demo mode - skipping Firebase database initialization')
    return
  }

  try {
    console.log('Initializing Firebase database...')

    // Check if categories already exist
    const categoriesSnapshot = await getDocs(collection(db, 'categories'))
    if (categoriesSnapshot.empty) {
      // Initialize categories
      for (const category of databaseStructure.categories) {
        await setDoc(doc(db, 'categories', category.id), category)
        console.log(`Created category: ${category.name}`)
      }
    } else {
      console.log('Categories already exist, skipping...')
    }

    // Check if item groups already exist
    const itemGroupsSnapshot = await getDocs(collection(db, 'item_groups'))
    if (itemGroupsSnapshot.empty) {
      // Initialize item groups
      for (const itemGroup of databaseStructure.itemGroups) {
        await setDoc(doc(db, 'item_groups', itemGroup.id), itemGroup)
        console.log(`Created item group: ${itemGroup.name}`)
      }
    } else {
      console.log('Item groups already exist, skipping...')
    }

    console.log('Database initialization completed successfully!')
  } catch (error) {
    console.error('Error initializing database:', error)
    throw error
  }
}
