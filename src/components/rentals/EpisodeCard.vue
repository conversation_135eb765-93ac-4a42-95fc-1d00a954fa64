<template>
  <Card class="episode-card" :class="`episode-${episode.status}`">
    <template #header>
      <div class="episode-header">
        <div class="episode-id">
          <strong>{{ episode.id }}</strong>
        </div>
        <Badge 
          :value="statusLabel" 
          :severity="statusSeverity"
          class="status-badge"
        />
      </div>
    </template>

    <template #title>
      <div class="episode-title">
        <i class="pi pi-calendar"></i>
        {{ formatDateRange(episode.dates) }}
      </div>
    </template>

    <template #subtitle>
      <div class="episode-subtitle">
        {{ episode.items.length }} item(s) • Created {{ formatDate(episode.created_at) }}
      </div>
    </template>

    <template #content>
      <div class="episode-content">
        <!-- Items Summary -->
        <div class="items-summary">
          <h4>Items:</h4>
          <div class="items-list">
            <div
              v-for="item in episode.items.slice(0, 3)"
              :key="`${item.item_group_id}-${item.individual_item_id}`"
              class="item-summary"
            >
              <span class="item-name">{{ item.item_group_name }}</span>
              <span class="item-quantity">x{{ item.quantity }}</span>
            </div>
            <div v-if="episode.items.length > 3" class="more-items">
              +{{ episode.items.length - 3 }} more item(s)
            </div>
          </div>
        </div>

        <!-- Dates Display -->
        <div class="dates-display">
          <h4>Rental Dates:</h4>
          <div class="dates-list">
            <Tag
              v-for="date in episode.dates.slice(0, 4)"
              :key="date"
              :value="formatShortDate(date)"
              severity="info"
              class="date-tag"
            />
            <span v-if="episode.dates.length > 4" class="more-dates">
              +{{ episode.dates.length - 4 }} more
            </span>
          </div>
        </div>

        <!-- Status-specific Information -->
        <div v-if="episode.status === 'scheduled'" class="status-info scheduled-info">
          <i class="pi pi-clock"></i>
          <span>Rental starts {{ getStartDateText(episode.dates) }}</span>
        </div>

        <div v-else-if="episode.status === 'rented'" class="status-info rented-info">
          <i class="pi pi-check-circle"></i>
          <span>Currently rented • Returns {{ getEndDateText(episode.dates) }}</span>
        </div>

        <div v-else-if="episode.status === 'returned'" class="status-info returned-info">
          <i class="pi pi-verified"></i>
          <span>Returned on {{ getEndDateText(episode.dates) }}</span>
        </div>

        <div v-else-if="episode.status === 'cancelled'" class="status-info cancelled-info">
          <i class="pi pi-times-circle"></i>
          <span>Cancelled on {{ formatDate(episode.updated_at) }}</span>
        </div>
      </div>
    </template>

    <template #footer>
      <div class="episode-actions">
        <Button
          label="View Details"
          icon="pi pi-eye"
          class="p-button-outlined"
          @click="$emit('view-details', episode)"
        />
        
        <Button
          v-if="episode.status === 'scheduled'"
          label="Cancel"
          icon="pi pi-times"
          severity="danger"
          class="p-button-outlined"
          @click="$emit('cancel-episode', episode)"
        />
      </div>
    </template>
  </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { format, parseISO, isToday, isTomorrow, isYesterday, differenceInDays } from 'date-fns'
import type { UserEpisode } from '@/stores/episodes'

interface Props {
  episode: UserEpisode
}

interface Emits {
  (e: 'cancel-episode', episode: UserEpisode): void
  (e: 'view-details', episode: UserEpisode): void
}

const props = defineProps<Props>()
defineEmits<Emits>()

// Computed properties
const statusLabel = computed(() => {
  switch (props.episode.status) {
    case 'scheduled':
      return 'Scheduled'
    case 'rented':
      return 'Rented'
    case 'returned':
      return 'Returned'
    case 'cancelled':
      return 'Cancelled'
    default:
      return props.episode.status
  }
})

const statusSeverity = computed(() => {
  switch (props.episode.status) {
    case 'scheduled':
      return 'info'
    case 'rented':
      return 'warning'
    case 'returned':
      return 'success'
    case 'cancelled':
      return 'danger'
    default:
      return 'info'
  }
})

// Methods
const formatDate = (date: Date) => {
  return format(date, 'MMM dd, yyyy')
}

const formatShortDate = (dateStr: string) => {
  return format(parseISO(dateStr), 'MMM dd')
}

const formatDateRange = (dates: string[]) => {
  if (dates.length === 0) return 'No dates'
  if (dates.length === 1) return formatShortDate(dates[0])
  
  const sortedDates = [...dates].sort()
  const startDate = sortedDates[0]
  const endDate = sortedDates[sortedDates.length - 1]
  
  if (dates.length === 2 && startDate === endDate) {
    return formatShortDate(startDate)
  }
  
  return `${formatShortDate(startDate)} - ${formatShortDate(endDate)}`
}

const getStartDateText = (dates: string[]) => {
  if (dates.length === 0) return ''
  
  const startDate = parseISO([...dates].sort()[0])
  
  if (isToday(startDate)) return 'today'
  if (isTomorrow(startDate)) return 'tomorrow'
  
  const daysFromNow = differenceInDays(startDate, new Date())
  if (daysFromNow > 0) {
    return `in ${daysFromNow} day${daysFromNow === 1 ? '' : 's'}`
  }
  
  return format(startDate, 'MMM dd')
}

const getEndDateText = (dates: string[]) => {
  if (dates.length === 0) return ''
  
  const endDate = parseISO([...dates].sort().pop()!)
  
  if (isToday(endDate)) return 'today'
  if (isYesterday(endDate)) return 'yesterday'
  if (isTomorrow(endDate)) return 'tomorrow'
  
  return format(endDate, 'MMM dd')
}
</script>

<style scoped>
.episode-card {
  height: 100%;
  transition: transform 0.2s, box-shadow 0.2s;
}

.episode-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.episode-scheduled {
  border-left: 4px solid var(--blue-500);
}

.episode-rented {
  border-left: 4px solid var(--orange-500);
}

.episode-returned {
  border-left: 4px solid var(--green-500);
}

.episode-cancelled {
  border-left: 4px solid var(--red-500);
}

.episode-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1rem 0 1rem;
}

.episode-id {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: var(--text-color-secondary);
}

.status-badge {
  font-size: 0.8rem;
}

.episode-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
}

.episode-subtitle {
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.episode-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.items-summary h4,
.dates-display h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-color);
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.item-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
}

.item-name {
  color: var(--text-color);
  flex: 1;
}

.item-quantity {
  color: var(--text-color-secondary);
  font-weight: 500;
}

.more-items {
  font-size: 0.85rem;
  color: var(--text-color-secondary);
  font-style: italic;
  margin-top: 0.25rem;
}

.dates-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  align-items: center;
}

.date-tag {
  font-size: 0.8rem;
}

.more-dates {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
  font-style: italic;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
}

.scheduled-info {
  background: var(--blue-50);
  color: var(--blue-700);
  border: 1px solid var(--blue-200);
}

.rented-info {
  background: var(--orange-50);
  color: var(--orange-700);
  border: 1px solid var(--orange-200);
}

.returned-info {
  background: var(--green-50);
  color: var(--green-700);
  border: 1px solid var(--green-200);
}

.cancelled-info {
  background: var(--red-50);
  color: var(--red-700);
  border: 1px solid var(--red-200);
}

.episode-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .episode-actions {
    flex-direction: column;
  }
  
  .episode-actions .p-button {
    width: 100%;
  }
}
</style>
