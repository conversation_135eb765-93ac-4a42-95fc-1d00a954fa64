<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { RouterView } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useItemsStore } from '@/stores/items'
import { useRentalsStore } from '@/stores/rentals'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppSidebar from '@/components/layout/AppSidebar.vue'

const authStore = useAuthStore()
const itemsStore = useItemsStore()
const rentalsStore = useRentalsStore()

onMounted(async () => {
  // Initialize authentication
  await authStore.initializeAuth()

  // Initialize real-time listeners if authenticated
  if (authStore.isAuthenticated) {
    itemsStore.initializeRealTimeListeners()
    rentalsStore.initializeRealTimeListeners(authStore.currentUser?.uid)
  }
})

onUnmounted(() => {
  // Clean up listeners
  itemsStore.stopRealTimeListeners()
  rentalsStore.stopRealTimeListeners()
})
</script>

<template>
  <div id="app">
    <!-- Toast for notifications -->
    <Toast />

    <!-- Confirmation dialog -->
    <ConfirmDialog />

    <!-- Loading overlay -->
    <div v-if="authStore.loading" class="loading-overlay">
      <ProgressSpinner />
    </div>

    <!-- Main layout for authenticated users -->
    <div v-if="authStore.isAuthenticated" class="app-layout">
      <AppHeader />
      <div class="app-content">
        <AppSidebar />
        <main class="main-content">
          <RouterView />
        </main>
      </div>
    </div>

    <!-- Login layout for unauthenticated users -->
    <div v-else class="login-layout">
      <RouterView />
    </div>
  </div>
</template>

<style>
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--p-surface-ground);
  color: var(--p-text-color);
}

#app {
  min-height: 100vh;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.app-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.app-content {
  display: flex;
  flex: 1;
}

.main-content {
  flex: 1;
  padding: 1.5rem;
  background-color: var(--p-surface-ground);
  overflow-y: auto;
}

.login-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--p-primary-500) 0%, var(--p-primary-700) 100%);
}

/* Responsive design */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--p-surface-100);
}

::-webkit-scrollbar-thumb {
  background: var(--p-surface-300);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-400);
}
</style>
