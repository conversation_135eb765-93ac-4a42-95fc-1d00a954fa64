<template>
  <div class="confirmation-view">
    <div class="confirmation-container">
      <!-- Header -->
      <div class="confirmation-header">
        <div class="header-content">
          <div class="header-left">
            <Button
              icon="pi pi-arrow-left"
              label="Back to Booking"
              class="p-button-text"
              @click="goBackToBooking"
            />
          </div>
          <div class="header-center">
            <h1 class="page-title">
              <i class="pi pi-check-circle"></i>
              Confirm Your Booking
            </h1>
            <p class="page-subtitle">Review your selection and submit your booking request</p>
          </div>
          <div class="header-right">
            <Button
              label="Clear All"
              icon="pi pi-trash"
              severity="danger"
              outlined
              @click="clearCart"
              :disabled="cart.length === 0"
            />
          </div>
        </div>
      </div>

      <!-- Booking Summary -->
      <div class="booking-summary">
        <div class="summary-header">
          <h2>
            <i class="pi pi-calendar"></i>
            Booking Summary
          </h2>
        </div>
        <div class="summary-content">
          <div class="summary-item">
            <span class="label">Equipment Type:</span>
            <span class="value">{{ formatEquipmentType(selectedType) }}</span>
          </div>
          <div class="summary-item">
            <span class="label">Selected Dates:</span>
            <div class="dates-list">
              <Tag
                v-for="date in formattedSelectedDates"
                :key="date"
                :value="date"
                severity="info"
                class="date-tag"
              />
            </div>
          </div>
          <div class="summary-item">
            <span class="label">Total Items:</span>
            <span class="value">{{ cart.length }} item group(s)</span>
          </div>
          <div class="summary-item">
            <span class="label">Total Quantity:</span>
            <span class="value">{{ totalCartQuantity }} item(s)</span>
          </div>
        </div>
      </div>

      <!-- Cart Items -->
      <div class="cart-section">
        <div class="cart-header">
          <h2>
            <i class="pi pi-shopping-cart"></i>
            Selected Items
          </h2>
        </div>
        <div class="cart-content">
          <CartPanel
            :cart-items="cart.map(item => ({
              ...item,
              contraindications: [...item.contraindications]
            }))"
            :selected-dates="selectedDates"
            @update-quantity="handleUpdateQuantity"
            @remove-item="handleRemoveFromCart"
            @submit-cart="handleSubmitCart"
            :loading="submitting"
            :show-submit-button="true"
            class="confirmation-cart-panel"
          />
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="cart.length === 0" class="empty-state">
        <div class="empty-content">
          <i class="pi pi-shopping-cart empty-icon"></i>
          <h3>No Items Selected</h3>
          <p>You haven't selected any items for booking yet.</p>
          <Button
            label="Start Booking"
            icon="pi pi-plus"
            @click="goBackToBooking"
            class="start-booking-btn"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'primevue/usetoast'
import { format, parseISO } from 'date-fns'
import { useItemsStore } from '@/stores/items'
import { useEpisodesStore } from '@/stores/episodes'
import { useAuthStore } from '@/stores/auth'
import CartPanel from '@/components/booking/CartPanel.vue'

// Stores
const itemsStore = useItemsStore()
const episodesStore = useEpisodesStore()
const authStore = useAuthStore()
const router = useRouter()
const toast = useToast()

// State
const submitting = ref(false)

// Computed
const cart = computed(() => episodesStore.cart)
const selectedDates = computed(() => episodesStore.selectedDates)
const selectedType = computed(() => itemsStore.selectedType)

const totalCartQuantity = computed(() => {
  return cart.value.reduce((total, item) => total + item.quantity, 0)
})

const formattedSelectedDates = computed(() => {
  return selectedDates.value
    .sort()
    .map(dateStr => format(parseISO(dateStr), 'MMM dd, yyyy'))
})

// Methods
const formatEquipmentType = (type: string) => {
  switch (type) {
    case 'resus_trainings':
      return 'Resuscitation Training Equipment'
    case 'venues':
      return 'Training Venue'
    case 'audio_visuals':
      return 'Audio Visual Equipment'
    default:
      return type
  }
}

const goBackToBooking = () => {
  router.push('/booking')
}

const clearCart = () => {
  episodesStore.clearCart()
  toast.add({
    severity: 'info',
    summary: 'Cart Cleared',
    detail: 'All items have been removed from your cart',
    life: 3000
  })
}

const handleUpdateQuantity = (itemGroupId: string, quantity: number) => {
  episodesStore.updateCartItemQuantity(itemGroupId, quantity)
}

const handleRemoveFromCart = (itemGroupId: string) => {
  episodesStore.removeFromCart(itemGroupId)
  toast.add({
    severity: 'info',
    summary: 'Removed from Cart',
    detail: 'Item removed from cart',
    life: 3000
  })
}

const handleSubmitCart = async () => {
  if (!authStore.currentUserProfile?.employee_number) {
    toast.add({
      severity: 'error',
      summary: 'Authentication Error',
      detail: 'Please log in to submit booking',
      life: 5000
    })
    return
  }

  submitting.value = true
  
  try {
    const episodeId = await episodesStore.submitCart(authStore.currentUserProfile.employee_number)
    
    toast.add({
      severity: 'success',
      summary: 'Booking Submitted',
      detail: `Your booking has been submitted successfully. Episode ID: ${episodeId}`,
      life: 5000
    })

    // Redirect to My Rentals page
    router.push('/my-rentals')
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Booking Failed',
      detail: error instanceof Error ? error.message : 'Failed to submit booking',
      life: 5000
    })
  } finally {
    submitting.value = false
  }
}

// Check if user has items in cart on mount
onMounted(() => {
  if (cart.value.length === 0) {
    // If no items in cart, redirect to booking page
    router.push('/booking')
  }
})
</script>

<style scoped>
.confirmation-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 2rem 0;
}

.confirmation-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Header */
.confirmation-header {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.header-content {
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  align-items: center;
  gap: 2rem;
}

.header-left {
  justify-self: start;
}

.header-center {
  text-align: center;
}

.header-right {
  justify-self: end;
}

.page-title {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.page-title i {
  color: #28a745;
}

.page-subtitle {
  margin: 0;
  color: #6c757d;
  font-size: 1.1rem;
}

/* Booking Summary */
.booking-summary {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.summary-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1.5rem 2rem;
  color: white;
}

.summary-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.summary-content {
  padding: 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.summary-item .label {
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-item .value {
  font-size: 1.1rem;
  color: #2c3e50;
  font-weight: 500;
}

.dates-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.date-tag {
  font-size: 0.9rem;
}

/* Cart Section */
.cart-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.cart-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
}

.cart-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.cart-header h2 i {
  color: #667eea;
}

.cart-content {
  padding: 0;
}

.confirmation-cart-panel {
  border-radius: 0;
  box-shadow: none;
}

/* Empty State */
.empty-state {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 4rem 2rem;
  text-align: center;
}

.empty-content {
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 4rem;
  color: #dee2e6;
  margin-bottom: 1.5rem;
}

.empty-content h3 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  color: #495057;
}

.empty-content p {
  margin: 0 0 2rem 0;
  color: #6c757d;
  font-size: 1.1rem;
}

.start-booking-btn {
  font-size: 1.1rem;
  padding: 0.75rem 2rem;
}

/* Responsive */
@media (max-width: 768px) {
  .confirmation-container {
    padding: 0 1rem;
  }

  .header-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 1rem;
  }

  .header-left,
  .header-right {
    justify-self: center;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .summary-content {
    grid-template-columns: 1fr;
    padding: 1.5rem;
  }

  .cart-header,
  .summary-header {
    padding: 1.25rem 1.5rem;
  }
}
</style>
