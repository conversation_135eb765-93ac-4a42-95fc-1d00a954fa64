<template>
  <div class="booking-container">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <i class="pi pi-plus-circle"></i>
            New Equipment Booking
          </h1>
          <p class="page-description">Select dates and equipment for your rental request</p>
        </div>
        <div class="header-actions">
          <Button
            icon="pi pi-refresh"
            label="Refresh"
            severity="secondary"
            outlined
            @click="refreshData"
            :loading="loading"
          />
        </div>
      </div>
    </div>

    <!-- Calendar Section -->
    <div class="calendar-section">
      <div class="section-header">
        <h2 class="section-title">
          <i class="pi pi-calendar"></i>
          Select Rental Dates
        </h2>
        <div class="section-info">
          <span class="info-text">Choose single dates, multiple dates, or date ranges</span>
        </div>
      </div>
      <div class="calendar-content">
        <DateSelector
          v-model="selectedDates"
          @update:modelValue="onDatesChanged"
          :disabled="loading"
        />
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content" v-if="selectedDates.length > 0">
      <!-- Items Region (2/3 width) -->
      <div class="items-region">
        <div class="items-header">
          <div class="header-left">
            <h3 class="items-title">
              <i class="pi pi-box"></i>
              Available Equipment
            </h3>
            <span class="items-count">{{ filteredItemGroups.length }} items available</span>
          </div>
          <div class="item-type-tabs">
            <Button
              v-for="tab in itemTabs"
              :key="tab.value"
              :label="tab.label"
              :severity="selectedType === tab.value ? 'primary' : 'secondary'"
              :outlined="selectedType !== tab.value"
              @click="setSelectedType(tab.value)"
              :disabled="loading"
              class="tab-button"
            />
          </div>
        </div>

        <div class="items-content">
          <!-- Category Sidebar -->
          <div class="category-sidebar">
            <div class="category-header">
              <h4>
                <i class="pi pi-filter"></i>
                Categories
              </h4>
              <span class="category-count">{{ categoriesByType.length + 1 }} options</span>
            </div>
            <div class="category-list">
              <div
                class="category-item"
                :class="{ 'category-active': selectedCategory === '' }"
                @click="setSelectedCategory('')"
              >
                <div class="category-icon">
                  <i class="pi pi-th-large"></i>
                </div>
                <span class="category-label">All Categories</span>
                <div class="category-badge">{{ filteredItemGroups.length }}</div>
              </div>
              <div
                v-for="category in categoriesByType"
                :key="category.id"
                class="category-item"
                :class="{ 'category-active': selectedCategory === category.id }"
                @click="setSelectedCategory(category.id)"
              >
                <div class="category-icon">
                  <i class="pi pi-tag"></i>
                </div>
                <span class="category-label">{{ category.name }}</span>
                <div class="category-badge">{{ getCategoryItemCount(category.id) }}</div>
              </div>
            </div>
          </div>

          <!-- Items Display -->
          <div class="items-display">
            <div v-if="loading" class="loading-state">
              <div class="loading-content">
                <ProgressSpinner />
                <h4>Loading Equipment</h4>
                <p>Fetching available items for your selected dates...</p>
              </div>
            </div>

            <div v-else-if="filteredItemGroups.length === 0" class="empty-state">
              <div class="empty-content">
                <i class="pi pi-inbox"></i>
                <h4>No Equipment Available</h4>
                <p>No items are available for the selected dates and category.</p>
                <Button
                  label="Clear Filters"
                  icon="pi pi-filter-slash"
                  severity="secondary"
                  outlined
                  @click="clearFilters"
                />
              </div>
            </div>

            <div v-else class="items-grid">
              <ItemCard
                v-for="itemGroup in filteredItemGroups"
                :key="itemGroup.id"
                :item-group="itemGroup"
                :available-quantity="getAvailableQuantity(itemGroup)"
                :is-available="isItemGroupAvailable(itemGroup)"
                @add-to-cart="handleAddToCart"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Cart Region (1/3 width) -->
      <div class="cart-region">
        <div class="cart-header">
          <h3 class="cart-title">
            <i class="pi pi-shopping-cart"></i>
            Booking Cart
          </h3>
          <div class="cart-summary">
            <span class="cart-count">{{ cart.length }} items</span>
          </div>
        </div>
        <CartPanel
          :cart-items="cart.map(item => ({
            ...item,
            contraindications: [...item.contraindications]
          }))"
          :selected-dates="selectedDates"
          @update-quantity="handleUpdateQuantity"
          @remove-item="handleRemoveFromCart"
          @submit-cart="handleSubmitCart"
          :loading="submitting"
        />
      </div>
    </div>

    <!-- Empty State when no dates selected -->
    <div v-else class="empty-dates-state">
      <div class="empty-dates-content">
        <div class="empty-icon">
          <i class="pi pi-calendar"></i>
        </div>
        <h3>Select Rental Dates</h3>
        <p>Choose one or more dates from the calendar above to view available equipment and start your booking.</p>
        <div class="empty-features">
          <div class="feature-item">
            <i class="pi pi-check-circle"></i>
            <span>Single or multiple date selection</span>
          </div>
          <div class="feature-item">
            <i class="pi pi-check-circle"></i>
            <span>Real-time availability checking</span>
          </div>
          <div class="feature-item">
            <i class="pi pi-check-circle"></i>
            <span>Instant booking confirmation</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Contraindication Dialog -->
    <ContraIndicationDialog
      v-model:visible="showContraDialog"
      :conflicts="contraindictionConflicts"
      :item-name="pendingCartItem?.item_group_name || ''"
      @confirm="handleContraindictionConfirm"
      @cancel="handleContraindictionCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import { useItemsStore } from '@/stores/items'
import { useEpisodesStore } from '@/stores/episodes'
import { useAuthStore } from '@/stores/auth'
import DateSelector from '@/components/booking/DateSelector.vue'
import ItemCard from '@/components/booking/ItemCard.vue'
import CartPanel from '@/components/booking/CartPanel.vue'
import ContraIndicationDialog from '@/components/booking/ContraIndicationDialog.vue'
import type { CartItem } from '@/stores/episodes'

// Stores
const itemsStore = useItemsStore()
const episodesStore = useEpisodesStore()
const authStore = useAuthStore()
const toast = useToast()

// Reactive state
const selectedDates = ref<string[]>([])
const loading = ref(false)
const submitting = ref(false)
const showContraDialog = ref(false)
const contraindictionConflicts = ref<Array<{item_group_name: string, reason: string}>>([])
const pendingCartItem = ref<CartItem | null>(null)

// Item type tabs
const itemTabs = [
  { label: 'Resuscitating Training', value: 'resus_trainings' as const },
  { label: 'Venue', value: 'venues' as const },
  { label: 'Audio Visuals', value: 'audio_visuals' as const }
]

// Computed properties
const selectedType = computed(() => itemsStore.selectedType)
const selectedCategory = computed(() => itemsStore.selectedCategory)
const categoriesByType = computed(() => itemsStore.categoriesByType)
const filteredItemGroups = computed(() => itemsStore.filteredItemGroups)
const cart = computed(() => episodesStore.cart)

// Methods
const setSelectedType = (type: 'resus_trainings' | 'venues' | 'audio_visuals') => {
  itemsStore.setSelectedType(type)
}

const setSelectedCategory = (categoryId: string) => {
  itemsStore.setSelectedCategory(categoryId)
}

const getAvailableQuantity = (itemGroup: any) => {
  return itemsStore.getAvailableQuantity(itemGroup)
}

const isItemGroupAvailable = (itemGroup: any) => {
  return itemsStore.isItemGroupAvailable(itemGroup)
}

const getCategoryItemCount = (categoryId: string) => {
  return itemsStore.getItemCountByCategory(categoryId)
}

const clearFilters = () => {
  itemsStore.setSelectedCategory('')
  itemsStore.setSearchQuery('')
}

const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      itemsStore.fetchItemGroups(),
      itemsStore.fetchCategories()
    ])
    toast.add({
      severity: 'success',
      summary: 'Data Refreshed',
      detail: 'Equipment data has been updated',
      life: 3000
    })
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Refresh Failed',
      detail: 'Failed to refresh equipment data',
      life: 5000
    })
  } finally {
    loading.value = false
  }
}

const onDatesChanged = (dates: string[]) => {
  selectedDates.value = dates
  itemsStore.setSelectedDates(dates)
  episodesStore.setSelectedDates(dates)
}

const handleAddToCart = (cartItem: CartItem) => {
  const success = episodesStore.addToCart(cartItem)
  
  if (!success) {
    // Check contraindications
    const conflicts = episodesStore.checkContraindications(cartItem)
    if (conflicts.length > 0) {
      contraindictionConflicts.value = conflicts
      pendingCartItem.value = cartItem
      showContraDialog.value = true
      return
    }
  }
  
  toast.add({
    severity: 'success',
    summary: 'Added to Cart',
    detail: `${cartItem.item_group_name} added to cart`,
    life: 3000
  })
}

const handleContraindictionConfirm = () => {
  if (pendingCartItem.value) {
    // Force add to cart despite contraindications
    const existingIndex = cart.value.findIndex(
      item => item.item_group_id === pendingCartItem.value!.item_group_id
    )
    
    if (existingIndex >= 0) {
      episodesStore.updateCartItemQuantity(
        pendingCartItem.value.item_group_id,
        cart.value[existingIndex].quantity + pendingCartItem.value.quantity
      )
    } else {
      episodesStore.addToCart(pendingCartItem.value)
    }
    
    toast.add({
      severity: 'warn',
      summary: 'Added with Warning',
      detail: `${pendingCartItem.value.item_group_name} added despite contraindications`,
      life: 5000
    })
  }
  
  showContraDialog.value = false
  pendingCartItem.value = null
  contraindictionConflicts.value = []
}

const handleContraindictionCancel = () => {
  showContraDialog.value = false
  pendingCartItem.value = null
  contraindictionConflicts.value = []
}

const handleUpdateQuantity = (itemGroupId: string, quantity: number) => {
  episodesStore.updateCartItemQuantity(itemGroupId, quantity)
}

const handleRemoveFromCart = (itemGroupId: string) => {
  episodesStore.removeFromCart(itemGroupId)
  toast.add({
    severity: 'info',
    summary: 'Removed from Cart',
    detail: 'Item removed from cart',
    life: 3000
  })
}

const handleSubmitCart = async () => {
  if (!authStore.currentUserProfile?.employee_number) {
    toast.add({
      severity: 'error',
      summary: 'Authentication Error',
      detail: 'Please log in to submit booking',
      life: 5000
    })
    return
  }

  submitting.value = true
  
  try {
    const episodeId = await episodesStore.submitCart(authStore.currentUserProfile.employee_number)
    
    toast.add({
      severity: 'success',
      summary: 'Booking Submitted',
      detail: `Your booking has been submitted successfully. Episode ID: ${episodeId}`,
      life: 5000
    })
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Booking Failed',
      detail: error instanceof Error ? error.message : 'Failed to submit booking',
      life: 5000
    })
  } finally {
    submitting.value = false
  }
}

// Lifecycle
onMounted(async () => {
  loading.value = true
  
  try {
    // Initialize real-time listeners
    itemsStore.initializeRealTimeListeners()
    
    if (authStore.currentUserProfile?.employee_number) {
      episodesStore.initializeUserEpisodesListener(authStore.currentUserProfile.employee_number)
    }
    
    // Fetch initial data
    await Promise.all([
      itemsStore.fetchItemGroups(),
      itemsStore.fetchCategories()
    ])
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Loading Error',
      detail: 'Failed to load booking data',
      life: 5000
    })
  } finally {
    loading.value = false
  }
})

onUnmounted(() => {
  itemsStore.stopRealTimeListeners()
  episodesStore.stopUserEpisodesListener()
})
</script>

<style scoped>
.booking-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0;
}

/* Page Header */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.header-content {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info {
  flex: 1;
}

.page-title {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-title i {
  font-size: 1.75rem;
}

.page-description {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

/* Calendar Section */
.calendar-section {
  margin-bottom: 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.section-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-title i {
  color: #667eea;
}

.section-info {
  display: flex;
  align-items: center;
}

.info-text {
  font-size: 0.9rem;
  color: #6c757d;
  font-style: italic;
}

.calendar-content {
  padding: 2rem;
}

/* Main Content */
.main-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  min-height: 600px;
}

/* Items Region */
.items-region {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.items-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.items-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.items-title i {
  color: #667eea;
}

.items-count {
  font-size: 0.85rem;
  color: #6c757d;
  font-weight: 500;
}

.item-type-tabs {
  display: flex;
  gap: 0.75rem;
}

.tab-button {
  border-radius: 25px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.items-content {
  display: flex;
  flex: 1;
  min-height: 0;
}

/* Category Sidebar */
.category-sidebar {
  width: 280px;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
}

.category-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  background: white;
}

.category-header h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.category-header i {
  color: #667eea;
}

.category-count {
  font-size: 0.8rem;
  color: #6c757d;
}

.category-list {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  overflow-y: auto;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  border: 1px solid #e9ecef;
}

.category-item:hover {
  background: #f8f9fa;
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

.category-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border-color: transparent !important;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

.category-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.category-active .category-icon {
  background: rgba(255, 255, 255, 0.2);
}

.category-label {
  flex: 1;
  font-weight: 500;
  font-size: 0.9rem;
}

.category-badge {
  background: #e9ecef;
  color: #495057;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
}

.category-active .category-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Items Display */
.items-display {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: white;
}

.loading-state,
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
}

.loading-content,
.empty-content {
  text-align: center;
  max-width: 400px;
}

.loading-content h4,
.empty-content h4 {
  margin: 1rem 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.25rem;
}

.loading-content p,
.empty-content p {
  margin: 0 0 1.5rem 0;
  color: #6c757d;
  line-height: 1.5;
}

.empty-content i {
  font-size: 4rem;
  color: #dee2e6;
  margin-bottom: 1rem;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

/* Cart Region */
.cart-region {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.cart-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cart-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.cart-title i {
  color: #667eea;
}

.cart-summary {
  display: flex;
  align-items: center;
}

.cart-count {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Empty Dates State */
.empty-dates-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 500px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.empty-dates-content {
  text-align: center;
  max-width: 500px;
  padding: 2rem;
}

.empty-icon {
  width: 120px;
  height: 120px;
  margin: 0 auto 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 3rem;
}

.empty-dates-content h3 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  color: #2c3e50;
  font-weight: 600;
}

.empty-dates-content p {
  margin: 0 0 2rem 0;
  color: #6c757d;
  font-size: 1.1rem;
  line-height: 1.6;
}

.empty-features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.feature-item i {
  color: #28a745;
  font-size: 1.1rem;
}

.feature-item span {
  color: #495057;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1.5fr 1fr;
    gap: 1.5rem;
  }

  .category-sidebar {
    width: 240px;
  }
}

@media (max-width: 1024px) {
  .header-content {
    padding: 0 1.5rem;
  }

  .calendar-content,
  .items-display {
    padding: 1.5rem;
  }

  .section-header,
  .items-header,
  .cart-header {
    padding: 1.25rem 1.5rem;
  }
}

@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .items-content {
    flex-direction: column;
  }

  .category-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }

  .category-list {
    flex-direction: row;
    flex-wrap: wrap;
    padding: 1rem;
  }

  .category-item {
    flex: 1;
    min-width: 140px;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .item-type-tabs {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .items-grid {
    grid-template-columns: 1fr;
  }
}
</style>
