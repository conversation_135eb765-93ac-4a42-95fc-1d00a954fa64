<template>
  <Dialog
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
    header="Contraindication Warning"
    modal
    :closable="false"
    style="width: 50vw; min-width: 400px;"
    class="contraindication-dialog"
  >
    <div class="dialog-content">
      <div class="warning-icon">
        <i class="pi pi-exclamation-triangle"></i>
      </div>
      
      <div class="warning-message">
        <h3>Contraindication Detected</h3>
        <p>
          The item <strong>"{{ itemName }}"</strong> has contraindications with items already in your cart.
        </p>
      </div>

      <div class="conflicts-section">
        <h4>Conflicting Items:</h4>
        <div class="conflicts-list">
          <div
            v-for="conflict in conflicts"
            :key="conflict.item_group_name"
            class="conflict-item"
          >
            <div class="conflict-header">
              <i class="pi pi-times-circle"></i>
              <strong>{{ conflict.item_group_name }}</strong>
            </div>
            <p class="conflict-reason">{{ conflict.reason }}</p>
          </div>
        </div>
      </div>

      <div class="recommendation">
        <div class="recommendation-header">
          <i class="pi pi-info-circle"></i>
          <strong>Recommendation</strong>
        </div>
        <p>
          It is recommended to remove the conflicting items from your cart before adding this item. 
          However, you can choose to proceed if you understand the implications.
        </p>
      </div>

      <div class="options-section">
        <h4>What would you like to do?</h4>
        <div class="option-buttons">
          <Button
            label="Cancel Addition"
            icon="pi pi-times"
            class="p-button-outlined"
            @click="handleCancel"
          />
          <Button
            label="Add Anyway"
            icon="pi pi-exclamation-triangle"
            severity="warning"
            @click="handleConfirm"
          />
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
interface Props {
  visible: boolean
  conflicts: Array<{
    item_group_name: string
    reason: string
  }>
  itemName: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const handleConfirm = () => {
  emit('confirm')
  emit('update:visible', false)
}

const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
}
</script>

<style scoped>
.contraindication-dialog {
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #3b82f6;
}

.dialog-content {
  padding: 1rem 0;
}

.warning-icon {
  text-align: center;
  margin-bottom: 1.5rem;
}

.warning-icon i {
  font-size: 4rem;
  color: var(--warning-color);
}

.warning-message {
  text-align: center;
  margin-bottom: 2rem;
}

.warning-message h3 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-size: 1.5rem;
  font-weight: 600;
}

.warning-message p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
  line-height: 1.5;
}

.conflicts-section {
  margin-bottom: 2rem;
}

.conflicts-section h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-size: 1.1rem;
  font-weight: 600;
}

.conflicts-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.conflict-item {
  padding: 1rem;
  background: var(--red-50);
  border: 1px solid var(--red-200);
  border-radius: 8px;
}

.conflict-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: var(--red-700);
  font-weight: 600;
}

.conflict-header i {
  color: var(--danger-color);
}

.conflict-reason {
  margin: 0;
  color: var(--red-600);
  font-size: 0.95rem;
  line-height: 1.4;
}

.recommendation {
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--blue-50);
  border: 1px solid var(--blue-200);
  border-radius: 8px;
}

.recommendation-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  color: var(--blue-700);
  font-weight: 600;
}

.recommendation-header i {
  color: var(--info-color);
}

.recommendation p {
  margin: 0;
  color: var(--blue-600);
  font-size: 0.95rem;
  line-height: 1.5;
}

.options-section h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-size: 1.1rem;
  font-weight: 600;
}

.option-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.option-buttons .p-button {
  min-width: 140px;
}

@media (max-width: 768px) {
  .contraindication-dialog {
    width: 95vw !important;
    min-width: unset !important;
  }
  
  .option-buttons {
    flex-direction: column;
  }
  
  .option-buttons .p-button {
    width: 100%;
    min-width: unset;
  }
  
  .warning-icon i {
    font-size: 3rem;
  }
  
  .warning-message h3 {
    font-size: 1.25rem;
  }
  
  .warning-message p {
    font-size: 1rem;
  }
}
</style>
