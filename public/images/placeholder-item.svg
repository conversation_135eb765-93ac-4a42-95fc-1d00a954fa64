<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#bg)" />
  
  <!-- Border -->
  <rect x="1" y="1" width="398" height="298" fill="none" stroke="#dee2e6" stroke-width="2" rx="8" />
  
  <!-- Icon -->
  <g transform="translate(200, 150)">
    <!-- Equipment icon -->
    <rect x="-30" y="-20" width="60" height="40" fill="#6c757d" rx="4" />
    <rect x="-25" y="-15" width="50" height="30" fill="#adb5bd" rx="2" />
    <circle cx="-15" cy="-5" r="3" fill="#6c757d" />
    <circle cx="15" cy="-5" r="3" fill="#6c757d" />
    <rect x="-10" y="5" width="20" height="3" fill="#6c757d" rx="1" />
  </g>
  
  <!-- Text -->
  <text x="200" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#6c757d" font-weight="500">
    Equipment Image
  </text>
  <text x="200" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#adb5bd">
    No image available
  </text>
</svg>
