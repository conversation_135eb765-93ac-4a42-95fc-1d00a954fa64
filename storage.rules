rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Item images - authenticated users can read, only item creators can write
    match /items/{itemId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    
    // User profile images - users can read/write their own images
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Public assets - anyone can read
    match /public/{allPaths=**} {
      allow read;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
  }
}
