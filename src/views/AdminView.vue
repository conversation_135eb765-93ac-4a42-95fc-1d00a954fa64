<template>
  <div class="admin-container">
    <div class="page-header">
      <h1>Admin Dashboard</h1>
      <p class="page-description">
        Manage items, view all rentals, and handle administrative tasks.
      </p>
    </div>

    <!-- Admin Tabs -->
    <div class="admin-tabs">
      <Button
        v-for="tab in adminTabs"
        :key="tab.value"
        :label="tab.label"
        :icon="tab.icon"
        :class="{ 'p-button-outlined': selectedTab !== tab.value }"
        @click="setSelectedTab(tab.value)"
      />
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Items Management -->
      <div v-if="selectedTab === 'items'" class="tab-panel">
        <div class="panel-header">
          <h2>Items Management</h2>
          <div class="header-actions">
            <Button
              label="Initialize Database"
              icon="pi pi-database"
              severity="info"
              outlined
              @click="initializeDatabase"
              :loading="initializingDb"
              v-tooltip="'Set up initial categories and items in Firebase'"
            />
            <Button
              label="Refresh Data"
              icon="pi pi-refresh"
              severity="secondary"
              outlined
              @click="refreshItemsData"
              :loading="loadingItems"
            />
            <Button
              label="Add New Item Group"
              icon="pi pi-plus"
              @click="showAddItemDialog = true"
            />
          </div>
        </div>

        <!-- Items Grouped by Category -->
        <div class="items-by-category">
          <!-- Resuscitation Training Equipment -->
          <div v-if="resuscitationItems.length > 0" class="category-section">
            <div class="category-header">
              <div class="category-title">
                <i class="pi pi-heart category-icon"></i>
                <h3>Resuscitation Training Equipment</h3>
                <Badge :value="resuscitationItems.length" severity="info" />
              </div>
            </div>
            <div class="items-grid">
              <Card
                v-for="itemGroup in resuscitationItems"
                :key="itemGroup.id"
                class="item-card"
              >
            <template #header>
              <div class="item-card-header">
                <div class="item-info">
                  <h3 class="item-name">{{ itemGroup.name }}</h3>
                  <Badge
                    :value="formatCategory(itemGroup.type)"
                    :severity="getCategorySeverity(itemGroup.type)"
                  />
                </div>
                <div class="item-actions">
                  <Button
                    icon="pi pi-pencil"
                    class="p-button-text p-button-sm"
                    @click="editItemGroup(itemGroup)"
                    v-tooltip="'Edit item group'"
                  />
                  <Button
                    icon="pi pi-eye"
                    class="p-button-text p-button-sm"
                    @click="viewItemDetails(itemGroup)"
                    v-tooltip="'View details'"
                  />
                </div>
              </div>
            </template>

            <template #content>
              <div class="item-content">
                <!-- Item Statistics -->
                <div class="item-stats">
                  <div class="stat-item">
                    <span class="stat-label">Total Items:</span>
                    <span class="stat-value">{{ itemGroup.items?.length || 0 }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">Available:</span>
                    <span class="stat-value available">{{ getAvailableCount(itemGroup) }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">Suspended:</span>
                    <span class="stat-value suspended">{{ getSuspendedCount(itemGroup) }}</span>
                  </div>
                </div>

                <!-- Individual Items List -->
                <div class="individual-items">
                  <h4>Individual Items</h4>
                  <div class="items-list">
                    <div
                      v-for="(item, index) in (itemGroup.items || [])"
                      :key="item.id || index"
                      class="individual-item"
                      :class="{ 'suspended': item.suspended }"
                    >
                      <div class="item-details">
                        <span class="item-serial">{{ item.serial_number || `Item ${index + 1}` }}</span>
                        <Badge
                          v-if="item.suspended"
                          value="Suspended"
                          severity="danger"
                          class="item-status"
                        />
                      </div>
                      <div class="item-controls">
                        <Button
                          :icon="item.suspended ? 'pi pi-check' : 'pi pi-ban'"
                          :class="item.suspended ? 'p-button-success' : 'p-button-warning'"
                          class="p-button-text p-button-sm"
                          @click="toggleItemSuspension(itemGroup, item)"
                          :v-tooltip="item.suspended ? 'Reactivate item' : 'Suspend item'"
                        />
                        <Button
                          icon="pi pi-pencil"
                          class="p-button-text p-button-sm"
                          @click="editIndividualItem(itemGroup, item)"
                          v-tooltip="'Edit item details'"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Baseline Quantity Management -->
                <div class="quantity-management">
                  <h4>Baseline Quantity Management</h4>
                  <div class="quantity-controls">
                    <div class="quantity-display">
                      <label>Current Baseline:</label>
                      <span class="current-quantity">{{ itemGroup.items?.length || 0 }} items</span>
                    </div>
                    <div class="quantity-actions">
                      <Button
                        label="Add Items"
                        icon="pi pi-plus"
                        size="small"
                        @click="showAddItemsDialog(itemGroup)"
                      />
                      <Button
                        label="Remove Items"
                        icon="pi pi-minus"
                        severity="secondary"
                        size="small"
                        @click="showRemoveItemsDialog(itemGroup)"
                        :disabled="!itemGroup.items?.length"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </Card>
        </div>
      </div>

      <!-- Training Venue -->
      <div v-if="venueItems.length > 0" class="category-section">
        <div class="category-header">
          <div class="category-title">
            <i class="pi pi-building category-icon"></i>
            <h3>Training Venue</h3>
            <Badge :value="venueItems.length" severity="success" />
          </div>
        </div>
        <div class="items-grid">
          <Card
            v-for="itemGroup in venueItems"
            :key="itemGroup.id"
            class="item-card"
          >
            <template #header>
              <div class="item-card-header">
                <div class="item-info">
                  <h3 class="item-name">{{ itemGroup.name }}</h3>
                  <Badge
                    :value="formatCategory(itemGroup.type)"
                    :severity="getCategorySeverity(itemGroup.type)"
                  />
                </div>
                <div class="item-actions">
                  <Button
                    icon="pi pi-pencil"
                    class="p-button-text p-button-sm"
                    @click="editItemGroup(itemGroup)"
                    v-tooltip="'Edit item group'"
                  />
                  <Button
                    icon="pi pi-eye"
                    class="p-button-text p-button-sm"
                    @click="viewItemDetails(itemGroup)"
                    v-tooltip="'View details'"
                  />
                </div>
              </div>
            </template>

            <template #content>
              <div class="item-content">
                <!-- Item Statistics -->
                <div class="item-stats">
                  <div class="stat-item">
                    <span class="stat-label">Total Items:</span>
                    <span class="stat-value">{{ itemGroup.items?.length || 0 }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">Available:</span>
                    <span class="stat-value available">{{ getAvailableCount(itemGroup) }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">Suspended:</span>
                    <span class="stat-value suspended">{{ getSuspendedCount(itemGroup) }}</span>
                  </div>
                </div>

                <!-- Individual Items List -->
                <div class="individual-items">
                  <h4>Individual Items</h4>
                  <div class="items-list">
                    <div
                      v-for="(item, index) in (itemGroup.items || [])"
                      :key="item.id || index"
                      class="individual-item"
                      :class="{ 'suspended': item.suspended }"
                    >
                      <div class="item-details">
                        <span class="item-serial">{{ item.serial_number || `Item ${index + 1}` }}</span>
                        <Badge
                          v-if="item.suspended"
                          value="Suspended"
                          severity="danger"
                          class="item-status"
                        />
                      </div>
                      <div class="item-controls">
                        <Button
                          :icon="item.suspended ? 'pi pi-check' : 'pi pi-ban'"
                          :class="item.suspended ? 'p-button-success' : 'p-button-warning'"
                          class="p-button-text p-button-sm"
                          @click="toggleItemSuspension(itemGroup, item)"
                          :v-tooltip="item.suspended ? 'Reactivate item' : 'Suspend item'"
                        />
                        <Button
                          icon="pi pi-pencil"
                          class="p-button-text p-button-sm"
                          @click="editIndividualItem(itemGroup, item)"
                          v-tooltip="'Edit item details'"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Baseline Quantity Management -->
                <div class="quantity-management">
                  <h4>Baseline Quantity Management</h4>
                  <div class="quantity-controls">
                    <div class="quantity-display">
                      <label>Current Baseline:</label>
                      <span class="current-quantity">{{ itemGroup.items?.length || 0 }} items</span>
                    </div>
                    <div class="quantity-actions">
                      <Button
                        label="Add Items"
                        icon="pi pi-plus"
                        size="small"
                        @click="showAddItemsDialog(itemGroup)"
                      />
                      <Button
                        label="Remove Items"
                        icon="pi pi-minus"
                        severity="secondary"
                        size="small"
                        @click="showRemoveItemsDialog(itemGroup)"
                        :disabled="!itemGroup.items?.length"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </Card>
        </div>
      </div>

      <!-- Audio Visual Equipment -->
      <div v-if="audioVisualItems.length > 0" class="category-section">
        <div class="category-header">
          <div class="category-title">
            <i class="pi pi-video category-icon"></i>
            <h3>Audio Visual Equipment</h3>
            <Badge :value="audioVisualItems.length" severity="warning" />
          </div>
        </div>
        <div class="items-grid">
          <Card
            v-for="itemGroup in audioVisualItems"
            :key="itemGroup.id"
            class="item-card"
          >
            <template #header>
              <div class="item-card-header">
                <div class="item-info">
                  <h3 class="item-name">{{ itemGroup.name }}</h3>
                  <Badge
                    :value="formatCategory(itemGroup.type)"
                    :severity="getCategorySeverity(itemGroup.type)"
                  />
                </div>
                <div class="item-actions">
                  <Button
                    icon="pi pi-pencil"
                    class="p-button-text p-button-sm"
                    @click="editItemGroup(itemGroup)"
                    v-tooltip="'Edit item group'"
                  />
                  <Button
                    icon="pi pi-eye"
                    class="p-button-text p-button-sm"
                    @click="viewItemDetails(itemGroup)"
                    v-tooltip="'View details'"
                  />
                </div>
              </div>
            </template>

            <template #content>
              <div class="item-content">
                <!-- Item Statistics -->
                <div class="item-stats">
                  <div class="stat-item">
                    <span class="stat-label">Total Items:</span>
                    <span class="stat-value">{{ itemGroup.items?.length || 0 }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">Available:</span>
                    <span class="stat-value available">{{ getAvailableCount(itemGroup) }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">Suspended:</span>
                    <span class="stat-value suspended">{{ getSuspendedCount(itemGroup) }}</span>
                  </div>
                </div>

                <!-- Individual Items List -->
                <div class="individual-items">
                  <h4>Individual Items</h4>
                  <div class="items-list">
                    <div
                      v-for="(item, index) in (itemGroup.items || [])"
                      :key="item.id || index"
                      class="individual-item"
                      :class="{ 'suspended': item.suspended }"
                    >
                      <div class="item-details">
                        <span class="item-serial">{{ item.serial_number || `Item ${index + 1}` }}</span>
                        <Badge
                          v-if="item.suspended"
                          value="Suspended"
                          severity="danger"
                          class="item-status"
                        />
                      </div>
                      <div class="item-controls">
                        <Button
                          :icon="item.suspended ? 'pi pi-check' : 'pi pi-ban'"
                          :class="item.suspended ? 'p-button-success' : 'p-button-warning'"
                          class="p-button-text p-button-sm"
                          @click="toggleItemSuspension(itemGroup, item)"
                          :v-tooltip="item.suspended ? 'Reactivate item' : 'Suspend item'"
                        />
                        <Button
                          icon="pi pi-pencil"
                          class="p-button-text p-button-sm"
                          @click="editIndividualItem(itemGroup, item)"
                          v-tooltip="'Edit item details'"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Baseline Quantity Management -->
                <div class="quantity-management">
                  <h4>Baseline Quantity Management</h4>
                  <div class="quantity-controls">
                    <div class="quantity-display">
                      <label>Current Baseline:</label>
                      <span class="current-quantity">{{ itemGroup.items?.length || 0 }} items</span>
                    </div>
                    <div class="quantity-actions">
                      <Button
                        label="Add Items"
                        icon="pi pi-plus"
                        size="small"
                        @click="showAddItemsDialog(itemGroup)"
                      />
                      <Button
                        label="Remove Items"
                        icon="pi pi-minus"
                        severity="secondary"
                        size="small"
                        @click="showRemoveItemsDialog(itemGroup)"
                        :disabled="!itemGroup.items?.length"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </Card>
        </div>
      </div>
    </div>

        <!-- Empty State -->
        <div v-if="!allItems.length && !loadingItems" class="empty-state">
          <i class="pi pi-box empty-icon"></i>
          <h3>No Items Found</h3>
          <p>Start by adding your first item group to manage inventory.</p>
          <Button
            label="Add First Item Group"
            icon="pi pi-plus"
            @click="showAddItemDialog = true"
          />
        </div>
      </div>

      <!-- All Rentals -->
      <div v-if="selectedTab === 'rentals'" class="tab-panel">
        <div class="panel-header">
          <h2>Booking Schedule & Management</h2>
          <div class="rental-view-toggle">
            <Button
              label="Calendar View"
              icon="pi pi-calendar"
              :class="{ 'p-button-outlined': rentalViewMode !== 'calendar' }"
              @click="rentalViewMode = 'calendar'"
              size="small"
            />
            <Button
              label="List View"
              icon="pi pi-list"
              :class="{ 'p-button-outlined': rentalViewMode !== 'list' }"
              @click="rentalViewMode = 'list'"
              size="small"
            />
          </div>
        </div>

        <!-- Calendar View -->
        <div v-if="rentalViewMode === 'calendar'" class="booking-calendar-view">
          <div class="calendar-header">
            <div class="calendar-navigation">
              <Button
                icon="pi pi-chevron-left"
                class="p-button-text"
                @click="previousMonth"
              />
              <h3 class="calendar-month">{{ formatCalendarMonth(currentCalendarDate) }}</h3>
              <Button
                icon="pi pi-chevron-right"
                class="p-button-text"
                @click="nextMonth"
              />
            </div>
            <div class="calendar-legend">
              <div class="legend-item">
                <div class="legend-color scheduled"></div>
                <span>Scheduled</span>
              </div>
              <div class="legend-item">
                <div class="legend-color rented"></div>
                <span>Active</span>
              </div>
              <div class="legend-item">
                <div class="legend-color returned"></div>
                <span>Returned</span>
              </div>
            </div>
          </div>

          <div class="calendar-grid">
            <div class="calendar-weekdays">
              <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
            </div>
            <div class="calendar-days">
              <div
                v-for="day in calendarDays"
                :key="day.date"
                class="calendar-day"
                :class="{
                  'other-month': !day.isCurrentMonth,
                  'today': day.isToday,
                  'has-bookings': day.bookings.length > 0
                }"
              >
                <div class="day-number">{{ day.dayNumber }}</div>
                <div class="day-bookings">
                  <div
                    v-for="booking in day.bookings.slice(0, 3)"
                    :key="booking.id"
                    class="booking-indicator"
                    :class="booking.status"
                    :title="`${booking.user_employee_number} - ${booking.items?.length || 0} items`"
                  >
                    <span class="booking-text">{{ booking.user_employee_number }}</span>
                  </div>
                  <div v-if="day.bookings.length > 3" class="more-bookings">
                    +{{ day.bookings.length - 3 }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- List View -->
        <div v-if="rentalViewMode === 'list'" class="booking-list-view">
          <div class="rental-filters">
            <Dropdown
              v-model="selectedRentalStatus"
              :options="rentalStatusOptions"
              option-label="label"
              option-value="value"
              placeholder="Filter by status"
              class="filter-dropdown"
            />
            <div class="date-filter-container">
              <Calendar
                v-model="selectedDateRange"
                selection-mode="range"
                :manual-input="false"
                placeholder="Filter by date range"
                class="filter-calendar"
                date-format="dd/mm/yy"
                show-icon
              />
              <Button
                v-if="selectedDateRange && selectedDateRange.length > 0"
                icon="pi pi-times"
                class="p-button-text p-button-sm clear-date-btn"
                @click="clearDateFilter"
                v-tooltip="'Clear date filter'"
              />
            </div>
          </div>

          <DataTable
          :value="filteredRentals" 
          :loading="loadingRentals"
          paginator 
          :rows="15"
          class="admin-table"
          responsive-layout="scroll"
        >
          <Column field="id" header="Episode ID" sortable>
            <template #body="{ data }">
              <code class="episode-id">{{ data.id }}</code>
            </template>
          </Column>
          <Column field="user_employee_number" header="Employee" sortable />
          <Column field="status" header="Status" sortable>
            <template #body="{ data }">
              <Badge 
                :value="data.status" 
                :severity="getStatusSeverity(data.status)"
              />
            </template>
          </Column>
          <Column header="Items">
            <template #body="{ data }">
              {{ data.items?.length || 0 }} item(s)
            </template>
          </Column>
          <Column header="Dates">
            <template #body="{ data }">
              {{ formatDateRange(data.dates) }}
            </template>
          </Column>
          <Column field="created_at" header="Created" sortable>
            <template #body="{ data }">
              {{ formatDate(data.created_at) }}
            </template>
          </Column>
          <Column header="Actions">
            <template #body="{ data }">
              <Button
                icon="pi pi-eye"
                class="p-button-text p-button-sm"
                @click="viewRentalDetails(data)"
                v-tooltip="'View details'"
              />
            </template>
          </Column>
          </DataTable>
        </div>
      </div>

      <!-- System Stats -->
      <div v-if="selectedTab === 'stats'" class="tab-panel">
        <div class="panel-header">
          <h2>System Statistics</h2>
        </div>
        
        <div class="stats-grid">
          <Card class="stat-card">
            <template #title>Total Items</template>
            <template #content>
              <div class="stat-value">{{ totalItemsCount }}</div>
              <div class="stat-label">Item groups in system</div>
            </template>
          </Card>
          
          <Card class="stat-card">
            <template #title>Active Rentals</template>
            <template #content>
              <div class="stat-value">{{ activeRentalsCount }}</div>
              <div class="stat-label">Currently rented episodes</div>
            </template>
          </Card>
          
          <Card class="stat-card">
            <template #title>Total Users</template>
            <template #content>
              <div class="stat-value">{{ totalUsersCount }}</div>
              <div class="stat-label">Registered users</div>
            </template>
          </Card>
          
          <Card class="stat-card">
            <template #title>This Month</template>
            <template #content>
              <div class="stat-value">{{ thisMonthRentalsCount }}</div>
              <div class="stat-label">Rentals this month</div>
            </template>
          </Card>
        </div>
      </div>

      <!-- User Management -->
      <div v-if="selectedTab === 'users'" class="tab-panel">
        <div class="panel-header">
          <h2>User Management</h2>
        </div>
        
        <DataTable 
          :value="allUsers" 
          :loading="loadingUsers"
          paginator 
          :rows="20"
          class="admin-table"
          responsive-layout="scroll"
        >
          <Column field="employee_number" header="Employee Number" sortable />
          <Column field="name" header="Name" sortable />
          <Column field="email" header="Email" sortable />
          <Column field="department" header="Department" sortable />
          <Column header="Total Rentals">
            <template #body="{ data }">
              {{ getUserRentalCount(data.employee_number) }}
            </template>
          </Column>
          <Column field="created_at" header="Joined" sortable>
            <template #body="{ data }">
              {{ formatDate(data.created_at) }}
            </template>
          </Column>
          <Column header="Actions">
            <template #body="{ data }">
              <Button
                icon="pi pi-eye"
                class="p-button-text p-button-sm"
                @click="viewUserDetails(data)"
                v-tooltip="'View user details'"
              />
            </template>
          </Column>
        </DataTable>
      </div>
    </div>

    <!-- Dialogs -->
    <Dialog
      v-model:visible="showAddItemDialog"
      header="Add New Item Group"
      modal
      style="width: 50vw; min-width: 400px;"
    >
      <div class="form-container">
        <div class="form-field">
          <label for="newItemName">Item Group Name *</label>
          <InputText
            id="newItemName"
            v-model="newItemName"
            placeholder="Enter item group name"
            :class="{ 'p-invalid': !newItemName.trim() }"
          />
        </div>

        <div class="form-field">
          <label for="newItemCategory">Category *</label>
          <Dropdown
            id="newItemCategory"
            v-model="newItemCategory"
            :options="categoryOptions"
            option-label="label"
            option-value="value"
            placeholder="Select category"
            :class="{ 'p-invalid': !newItemCategory }"
          />
        </div>

        <div class="form-field">
          <label for="newItemDescription">Description</label>
          <Textarea
            id="newItemDescription"
            v-model="newItemDescription"
            placeholder="Enter item description"
            rows="3"
          />
        </div>

        <div class="form-field">
          <label for="initialQuantity">Initial Quantity *</label>
          <InputNumber
            id="initialQuantity"
            v-model="newItemQuantity"
            :min="1"
            :max="50"
            placeholder="Number of items to create"
          />
        </div>

        <div class="form-field">
          <label for="serialPrefix">Serial Number Prefix</label>
          <InputText
            id="serialPrefix"
            v-model="newItemSerialPrefix"
            placeholder="e.g., MAN, CAM, TV"
            style="text-transform: uppercase"
          />
        </div>
      </div>

      <div class="dialog-actions">
        <Button
          label="Cancel"
          class="p-button-outlined"
          @click="cancelAddItem"
        />
        <Button
          label="Create Item Group"
          @click="confirmAddItem"
          :disabled="!canCreateItem"
          :loading="loading"
        />
      </div>
    </Dialog>

    <!-- Add Items Dialog -->
    <Dialog
      v-model:visible="showAddItemsDialogVisible"
      header="Add Items to Group"
      modal
      style="width: 40vw; min-width: 350px;"
    >
      <div class="dialog-content">
        <div class="form-group">
          <label for="itemsToAdd">Number of items to add:</label>
          <InputNumber
            id="itemsToAdd"
            v-model="itemsToAdd"
            :min="1"
            :max="50"
            show-buttons
            class="quantity-input"
          />
        </div>
        <div class="form-group">
          <label for="startingSerial">Starting serial number (optional):</label>
          <InputText
            id="startingSerial"
            v-model="startingSerialNumber"
            placeholder="e.g., SN001"
            class="serial-input"
          />
        </div>
        <div class="info-message">
          <i class="pi pi-info-circle"></i>
          <span>This will add {{ itemsToAdd }} new items to "{{ selectedItemGroup?.name }}"</span>
        </div>
      </div>
      <div class="dialog-actions">
        <Button
          label="Cancel"
          class="p-button-outlined"
          @click="cancelAddItems"
        />
        <Button
          label="Add Items"
          icon="pi pi-plus"
          @click="confirmAddItems"
          :disabled="!itemsToAdd || itemsToAdd < 1"
        />
      </div>
    </Dialog>

    <!-- Remove Items Dialog -->
    <Dialog
      v-model:visible="showRemoveItemsDialogVisible"
      header="Remove Items from Group"
      modal
      style="width: 40vw; min-width: 350px;"
    >
      <div class="dialog-content">
        <div class="form-group">
          <label for="itemsToRemove">Number of items to remove:</label>
          <InputNumber
            id="itemsToRemove"
            v-model="itemsToRemove"
            :min="1"
            :max="selectedItemGroup?.items?.length || 1"
            show-buttons
            class="quantity-input"
          />
        </div>
        <div class="warning-message">
          <i class="pi pi-exclamation-triangle"></i>
          <span>This will permanently remove {{ itemsToRemove }} items from "{{ selectedItemGroup?.name }}". Only non-suspended items will be removed first.</span>
        </div>
      </div>
      <div class="dialog-actions">
        <Button
          label="Cancel"
          class="p-button-outlined"
          @click="cancelRemoveItems"
        />
        <Button
          label="Remove Items"
          icon="pi pi-minus"
          severity="danger"
          @click="confirmRemoveItems"
          :disabled="!itemsToRemove || itemsToRemove < 1"
        />
      </div>
    </Dialog>

    <!-- Edit Item Group Dialog -->
    <Dialog
      v-model:visible="showEditItemGroupDialogVisible"
      header="Edit Item Group"
      modal
      :style="{
        width: '60vw',
        minWidth: '500px',
        maxWidth: '800px',
        background: 'white',
        borderRadius: '12px',
        boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',
        border: '1px solid #e0e0e0'
      }"
      :contentStyle="{
        background: 'white',
        padding: '2rem',
        borderRadius: '0 0 12px 12px',
        color: '#333'
      }"
      :headerStyle="{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        borderRadius: '12px 12px 0 0',
        padding: '1.5rem 2rem',
        borderBottom: 'none'
      }"
      class="force-white-dialog"
      :draggable="false"
      :closable="true"
      :blockScroll="true"
    >
      <div class="dialog-content" v-if="selectedItemGroup">
        <div class="dialog-header-info">
          <div class="group-icon">
            <i class="pi pi-box"></i>
          </div>
          <div class="group-basic-info">
            <h3>{{ selectedItemGroup.name }}</h3>
            <Badge
              :value="formatCategory(selectedItemGroup.type)"
              :severity="getCategorySeverity(selectedItemGroup.type)"
            />
          </div>
        </div>

        <Divider />

        <div class="form-sections">
          <!-- Basic Information Section -->
          <div class="form-section">
            <h4><i class="pi pi-info-circle"></i> Basic Information</h4>
            <div class="form-grid">
              <div class="form-group">
                <label for="groupName">Group Name:</label>
                <InputText
                  id="groupName"
                  v-model="editingGroupName"
                  placeholder="Enter group name"
                  class="form-input"
                />
              </div>
              <div class="form-group">
                <label for="groupCategory">Category:</label>
                <Dropdown
                  id="groupCategory"
                  v-model="editingGroupCategory"
                  :options="categoryOptions"
                  option-label="label"
                  option-value="value"
                  placeholder="Select category"
                  class="form-input"
                />
              </div>
            </div>
            <div class="form-group">
              <label for="groupDescription">Description:</label>
              <Textarea
                id="groupDescription"
                v-model="editingGroupDescription"
                placeholder="Enter group description"
                rows="3"
                class="form-input"
              />
            </div>
          </div>

          <!-- Rental Settings Section -->
          <div class="form-section">
            <h4><i class="pi pi-calendar"></i> Rental Settings</h4>
            <div class="form-grid">
              <div class="form-group">
                <label for="maxRentalDays">Maximum Rental Days:</label>
                <InputNumber
                  id="maxRentalDays"
                  v-model="editingMaxRentalDays"
                  :min="1"
                  :max="365"
                  show-buttons
                  suffix=" days"
                  class="form-input"
                />
              </div>
              <div class="form-group">
                <label for="advanceBookingDays">Advance Booking (Days):</label>
                <InputNumber
                  id="advanceBookingDays"
                  v-model="editingAdvanceBookingDays"
                  :min="0"
                  :max="180"
                  show-buttons
                  suffix=" days"
                  class="form-input"
                />
              </div>
            </div>
            <div class="form-group">
              <div class="checkbox-group">
                <Checkbox
                  id="requiresApproval"
                  v-model="editingRequiresApproval"
                  binary
                />
                <label for="requiresApproval">Requires approval before rental</label>
              </div>
            </div>
          </div>

          <!-- Location & Storage Section -->
          <div class="form-section">
            <h4><i class="pi pi-map-marker"></i> Location & Storage</h4>
            <div class="form-grid">
              <div class="form-group">
                <label for="storageLocation">Storage Location:</label>
                <InputText
                  id="storageLocation"
                  v-model="editingStorageLocation"
                  placeholder="e.g., Room 101, Cabinet A"
                  class="form-input"
                />
              </div>
              <div class="form-group">
                <label for="responsiblePerson">Responsible Person:</label>
                <InputText
                  id="responsiblePerson"
                  v-model="editingResponsiblePerson"
                  placeholder="Enter responsible person's name"
                  class="form-input"
                />
              </div>
            </div>
          </div>

          <!-- Additional Settings Section -->
          <div class="form-section">
            <h4><i class="pi pi-cog"></i> Additional Settings</h4>
            <div class="form-group">
              <label for="groupNotes">Internal Notes:</label>
              <Textarea
                id="groupNotes"
                v-model="editingGroupNotes"
                placeholder="Add internal notes about this item group"
                rows="3"
                class="form-input"
              />
            </div>
            <div class="checkbox-group">
              <Checkbox
                id="groupActive"
                v-model="editingGroupActive"
                binary
              />
              <label for="groupActive">Group is active and available for rental</label>
            </div>
          </div>
        </div>
      </div>
      <div class="dialog-actions">
        <Button
          label="Cancel"
          class="p-button-outlined"
          @click="cancelEditItemGroup"
        />
        <Button
          label="Save Changes"
          icon="pi pi-check"
          @click="confirmEditItemGroup"
        />
      </div>
    </Dialog>

    <!-- Edit Individual Item Dialog -->
    <Dialog
      v-model:visible="showEditItemDialogVisible"
      header="Edit Item Details"
      modal
      :style="{
        width: '50vw',
        minWidth: '400px',
        maxWidth: '600px',
        background: 'white',
        borderRadius: '12px',
        boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',
        border: '1px solid #e0e0e0'
      }"
      :contentStyle="{
        background: 'white',
        padding: '2rem',
        borderRadius: '0 0 12px 12px',
        color: '#333'
      }"
      :headerStyle="{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        borderRadius: '12px 12px 0 0',
        padding: '1.5rem 2rem',
        borderBottom: 'none'
      }"
      class="force-white-dialog"
      :draggable="false"
      :closable="true"
      :blockScroll="true"
    >
      <div class="dialog-content" v-if="selectedIndividualItem">
        <div class="dialog-header-info">
          <div class="item-icon">
            <i class="pi pi-tag"></i>
          </div>
          <div class="item-basic-info">
            <h3>{{ selectedIndividualItem.serial_number || `Item ${getItemIndex(selectedIndividualItem)}` }}</h3>
            <span class="item-group-name">{{ selectedItemGroup?.name }}</span>
          </div>
          <Badge
            v-if="selectedIndividualItem.suspended"
            value="Suspended"
            severity="danger"
          />
        </div>

        <Divider />

        <div class="form-sections">
          <!-- Item Identification Section -->
          <div class="form-section">
            <h4><i class="pi pi-id-card"></i> Item Identification</h4>
            <div class="form-grid">
              <div class="form-group">
                <label for="serialNumber">Serial Number:</label>
                <InputText
                  id="serialNumber"
                  v-model="editingSerialNumber"
                  placeholder="Enter serial number"
                  class="form-input"
                />
              </div>
              <div class="form-group">
                <label for="assetTag">Asset Tag:</label>
                <InputText
                  id="assetTag"
                  v-model="editingAssetTag"
                  placeholder="Enter asset tag"
                  class="form-input"
                />
              </div>
            </div>
            <div class="form-group">
              <label for="itemModel">Model/Version:</label>
              <InputText
                id="itemModel"
                v-model="editingItemModel"
                placeholder="Enter model or version"
                class="form-input"
              />
            </div>
          </div>

          <!-- Condition & Status Section -->
          <div class="form-section">
            <h4><i class="pi pi-shield"></i> Condition & Status</h4>
            <div class="form-grid">
              <div class="form-group">
                <label for="itemCondition">Condition:</label>
                <Dropdown
                  id="itemCondition"
                  v-model="editingItemCondition"
                  :options="conditionOptions"
                  option-label="label"
                  option-value="value"
                  placeholder="Select condition"
                  class="form-input"
                />
              </div>
              <div class="form-group">
                <label for="lastMaintenance">Last Maintenance:</label>
                <Calendar
                  id="lastMaintenance"
                  v-model="editingLastMaintenance"
                  placeholder="Select date"
                  date-format="dd/mm/yy"
                  class="form-input"
                />
              </div>
            </div>
            <div class="form-group">
              <div class="checkbox-group">
                <Checkbox
                  id="suspendItem"
                  v-model="editingItemSuspended"
                  binary
                />
                <label for="suspendItem">Suspend this item (unavailable for rental)</label>
              </div>
            </div>
          </div>

          <!-- Notes & History Section -->
          <div class="form-section">
            <h4><i class="pi pi-file-edit"></i> Notes & History</h4>
            <div class="form-group">
              <label for="itemNotes">Maintenance Notes:</label>
              <Textarea
                id="itemNotes"
                v-model="editingItemNotes"
                placeholder="Add maintenance notes, issues, or other relevant information"
                rows="4"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label for="suspensionReason">Suspension Reason (if suspended):</label>
              <Textarea
                id="suspensionReason"
                v-model="editingSuspensionReason"
                placeholder="Explain why this item is suspended"
                rows="2"
                class="form-input"
                :disabled="!editingItemSuspended"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="dialog-actions">
        <Button
          label="Cancel"
          class="p-button-outlined"
          @click="cancelEditItem"
        />
        <Button
          label="Save Changes"
          icon="pi pi-check"
          @click="confirmEditItem"
        />
      </div>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import { useItemsStore } from '@/stores/items'
import { useEpisodesStore } from '@/stores/episodes'
import { useAuthStore } from '@/stores/auth'
import { format } from 'date-fns'
import { AdminUtils } from '@/utils/adminUtils'

// PrimeVue Components
import Button from 'primevue/button'
import Card from 'primevue/card'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Badge from 'primevue/badge'
import Dialog from 'primevue/dialog'
import Divider from 'primevue/divider'
import InputNumber from 'primevue/inputnumber'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import Checkbox from 'primevue/checkbox'
import Dropdown from 'primevue/dropdown'
import Calendar from 'primevue/calendar'

// Stores
const itemsStore = useItemsStore()
const episodesStore = useEpisodesStore()
const authStore = useAuthStore()
const toast = useToast()

// Reactive state
const selectedTab = ref<'items' | 'rentals' | 'stats' | 'users'>('items')
const loadingItems = ref(false)
const loadingRentals = ref(false)
const loadingUsers = ref(false)
const initializingDb = ref(false)
const showAddItemDialog = ref(false)
const selectedRentalStatus = ref<string | null>(null)
const selectedDateRange = ref<Date[] | null>(null)

// Calendar view state
const rentalViewMode = ref<'calendar' | 'list'>('calendar')
const currentCalendarDate = ref(new Date())
const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

// Items management dialogs
const showAddItemsDialogVisible = ref(false)
const showRemoveItemsDialogVisible = ref(false)
const showEditItemDialogVisible = ref(false)
const showEditItemGroupDialogVisible = ref(false)
const selectedItemGroup = ref<any>(null)
const selectedIndividualItem = ref<any>(null)

// Form data for dialogs
const itemsToAdd = ref(1)
const itemsToRemove = ref(1)
const startingSerialNumber = ref('')

// New item group form data
const newItemName = ref('')
const newItemCategory = ref('')
const newItemDescription = ref('')
const newItemQuantity = ref(1)
const newItemSerialPrefix = ref('')

// Edit item group form data
const editingGroupName = ref('')
const editingGroupCategory = ref('')
const editingGroupDescription = ref('')
const editingMaxRentalDays = ref(7)
const editingAdvanceBookingDays = ref(30)
const editingRequiresApproval = ref(false)
const editingStorageLocation = ref('')
const editingResponsiblePerson = ref('')
const editingGroupNotes = ref('')
const editingGroupActive = ref(true)

// Edit individual item form data
const editingSerialNumber = ref('')
const editingAssetTag = ref('')
const editingItemModel = ref('')
const editingItemCondition = ref('')
const editingLastMaintenance = ref<Date | null>(null)
const editingItemNotes = ref('')
const editingItemSuspended = ref(false)
const editingSuspensionReason = ref('')

// Dropdown options
const categoryOptions = ref([
  { label: 'Resuscitation Training Equipment', value: 'resus_trainings' },
  { label: 'Venues & Spaces', value: 'venues' },
  { label: 'Audio Visual Equipment', value: 'audio_visuals' },
  { label: 'Medical Equipment', value: 'medical_equipment' },
  { label: 'Educational Materials', value: 'educational_materials' },
  { label: 'Other Equipment', value: 'other' }
])

const conditionOptions = ref([
  { label: 'Excellent', value: 'excellent' },
  { label: 'Good', value: 'good' },
  { label: 'Fair', value: 'fair' },
  { label: 'Poor', value: 'poor' },
  { label: 'Needs Repair', value: 'needs_repair' }
])

// Mock data (in real implementation, these would come from stores)
const allItems = computed(() => itemsStore.itemGroups)
const allUsers = ref<any[]>([]) // Would be populated from a users store
const allRentals = ref<any[]>([]) // Would be populated from episodes store

// Grouped items by category
const resuscitationItems = computed(() =>
  allItems.value.filter(item =>
    item.type === 'resus_trainings' ||
    item.category_id === 'resuscitation-training'
  )
)

const venueItems = computed(() =>
  allItems.value.filter(item =>
    item.type === 'venues' ||
    item.category_id === 'training-venue'
  )
)

const audioVisualItems = computed(() =>
  allItems.value.filter(item =>
    item.type === 'audio_visuals' ||
    item.category_id === 'audio-visual'
  )
)

// Tab configuration
const adminTabs = [
  { label: 'Items', value: 'items' as const, icon: 'pi pi-box' },
  { label: 'Rentals', value: 'rentals' as const, icon: 'pi pi-calendar' },
  { label: 'Statistics', value: 'stats' as const, icon: 'pi pi-chart-bar' },
  { label: 'Users', value: 'users' as const, icon: 'pi pi-users' }
]

const rentalStatusOptions = [
  { label: 'All Statuses', value: null },
  { label: 'Scheduled', value: 'scheduled' },
  { label: 'Rented', value: 'rented' },
  { label: 'Returned', value: 'returned' },
  { label: 'Cancelled', value: 'cancelled' }
]

// Computed properties
const filteredRentals = computed(() => {
  let rentals = allRentals.value

  if (selectedRentalStatus.value) {
    rentals = rentals.filter(rental => rental.status === selectedRentalStatus.value)
  }

  // Date range filtering
  if (selectedDateRange.value && selectedDateRange.value.length === 2) {
    const [startDate, endDate] = selectedDateRange.value
    rentals = rentals.filter(rental => {
      if (!rental.created_at) return false
      const rentalDate = new Date(rental.created_at)
      return rentalDate >= startDate && rentalDate <= endDate
    })
  }

  return rentals
})

const totalItemsCount = computed(() => allItems.value.length)
const activeRentalsCount = computed(() => 
  allRentals.value.filter(rental => rental.status === 'rented').length
)
const totalUsersCount = computed(() => allUsers.value.length)
const thisMonthRentalsCount = computed(() => {
  const now = new Date()
  const thisMonth = now.getMonth()
  const thisYear = now.getFullYear()
  
  return allRentals.value.filter(rental => {
    const createdDate = new Date(rental.created_at)
    return createdDate.getMonth() === thisMonth && createdDate.getFullYear() === thisYear
  }).length
})

// Methods
const setSelectedTab = (tab: 'items' | 'rentals' | 'stats' | 'users') => {
  selectedTab.value = tab
}

const clearDateFilter = () => {
  selectedDateRange.value = null
}



const getAvailableCount = (itemGroup: any) => {
  // This would use the actual availability checking logic
  return itemGroup.items?.filter((item: any) => !item.suspended).length || 0
}

const getSuspendedCount = (itemGroup: any) => {
  return itemGroup.items?.filter((item: any) => item.suspended).length || 0
}

const getCategorySeverity = (type: string) => {
  switch (type) {
    case 'resus_trainings':
      return 'info'
    case 'venues':
      return 'success'
    case 'audio_visuals':
      return 'warning'
    case 'medical_equipment':
      return 'danger'
    case 'educational_materials':
      return 'secondary'
    default:
      return 'info'
  }
}

const formatCategory = (type: string) => {
  const category = categoryOptions.value.find(cat => cat.value === type)
  return category ? category.label : type
}

// Check if new item can be created
const canCreateItem = computed(() => {
  return newItemName.value.trim() &&
         newItemCategory.value &&
         newItemQuantity.value > 0 &&
         !loading.value
})

const getItemIndex = (item: any) => {
  if (!selectedItemGroup.value?.items) return 1
  return selectedItemGroup.value.items.indexOf(item) + 1
}

const initializeDatabase = async () => {
  initializingDb.value = true
  try {
    const result = await AdminUtils.initializeDatabase()
    if (result.success) {
      toast.add({
        severity: 'success',
        summary: 'Database Initialized',
        detail: result.message,
        life: 5000
      })
      // Refresh data after initialization
      await refreshItemsData()
    } else {
      toast.add({
        severity: 'error',
        summary: 'Initialization Failed',
        detail: result.message,
        life: 5000
      })
    }
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Initialization Error',
      detail: 'An unexpected error occurred during database initialization',
      life: 5000
    })
  } finally {
    initializingDb.value = false
  }
}

const refreshItemsData = async () => {
  loadingItems.value = true
  try {
    await itemsStore.fetchItemGroups()
    toast.add({
      severity: 'success',
      summary: 'Data Refreshed',
      detail: 'Items data has been refreshed successfully',
      life: 3000
    })
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Refresh Failed',
      detail: 'Failed to refresh items data',
      life: 5000
    })
  } finally {
    loadingItems.value = false
  }
}

const getStatusSeverity = (status: string) => {
  switch (status) {
    case 'scheduled':
      return 'info'
    case 'rented':
      return 'warning'
    case 'returned':
      return 'success'
    case 'cancelled':
      return 'danger'
    default:
      return 'info'
  }
}

const formatDate = (date: Date | string) => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return format(dateObj, 'MMM dd, yyyy')
}

const formatDateRange = (dates: string[]) => {
  if (!dates || dates.length === 0) return 'No dates'
  if (dates.length === 1) return formatDate(dates[0])
  
  const sortedDates = [...dates].sort()
  return `${formatDate(sortedDates[0])} - ${formatDate(sortedDates[sortedDates.length - 1])}`
}

const getUserRentalCount = (employeeNumber: string) => {
  return allRentals.value.filter(rental => rental.user_employee_number === employeeNumber).length
}

const editItemGroup = (itemGroup: any) => {
  selectedItemGroup.value = itemGroup

  // Populate form with current values
  editingGroupName.value = itemGroup.name || ''
  editingGroupCategory.value = itemGroup.type || ''
  editingGroupDescription.value = itemGroup.description || ''
  editingMaxRentalDays.value = itemGroup.max_rental_days || 7
  editingAdvanceBookingDays.value = itemGroup.advance_booking_days || 30
  editingRequiresApproval.value = itemGroup.requires_approval || false
  editingStorageLocation.value = itemGroup.storage_location || ''
  editingResponsiblePerson.value = itemGroup.responsible_person || ''
  editingGroupNotes.value = itemGroup.notes || ''
  editingGroupActive.value = itemGroup.active !== false

  showEditItemGroupDialogVisible.value = true
}

const cancelEditItemGroup = () => {
  showEditItemGroupDialogVisible.value = false
  selectedItemGroup.value = null

  // Reset form
  editingGroupName.value = ''
  editingGroupCategory.value = ''
  editingGroupDescription.value = ''
  editingMaxRentalDays.value = 7
  editingAdvanceBookingDays.value = 30
  editingRequiresApproval.value = false
  editingStorageLocation.value = ''
  editingResponsiblePerson.value = ''
  editingGroupNotes.value = ''
  editingGroupActive.value = true
}

const confirmEditItemGroup = async () => {
  if (!selectedItemGroup.value) return

  try {
    // Update the item group in the store
    const updatedData = {
      ...selectedItemGroup.value,
      name: editingGroupName.value,
      type: editingGroupCategory.value,
      description: editingGroupDescription.value,
      max_rental_days: editingMaxRentalDays.value,
      advance_booking_days: editingAdvanceBookingDays.value,
      requires_approval: editingRequiresApproval.value,
      storage_location: editingStorageLocation.value,
      responsible_person: editingResponsiblePerson.value,
      notes: editingGroupNotes.value,
      isActive: editingGroupActive.value
    }

    // Update in the items store
    await itemsStore.updateItemGroup(selectedItemGroup.value.id, updatedData)

    toast.add({
      severity: 'success',
      summary: 'Item Group Updated',
      detail: `${editingGroupName.value} has been updated successfully`,
      life: 3000
    })

    cancelEditItemGroup()
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Update Failed',
      detail: error instanceof Error ? error.message : 'Failed to update item group',
      life: 5000
    })
  }
}

// Quantity management methods
const showAddItemsDialog = (itemGroup: any) => {
  selectedItemGroup.value = itemGroup
  itemsToAdd.value = 1
  startingSerialNumber.value = ''
  showAddItemsDialogVisible.value = true
}

const showRemoveItemsDialog = (itemGroup: any) => {
  selectedItemGroup.value = itemGroup
  itemsToRemove.value = 1
  showRemoveItemsDialogVisible.value = true
}

const cancelAddItems = () => {
  showAddItemsDialogVisible.value = false
  selectedItemGroup.value = null
  itemsToAdd.value = 1
  startingSerialNumber.value = ''
}

const cancelRemoveItems = () => {
  showRemoveItemsDialogVisible.value = false
  selectedItemGroup.value = null
  itemsToRemove.value = 1
}

const confirmAddItems = () => {
  if (!selectedItemGroup.value || !itemsToAdd.value) return

  // In a real implementation, this would call the store to add items
  toast.add({
    severity: 'success',
    summary: 'Items Added',
    detail: `Added ${itemsToAdd.value} items to ${selectedItemGroup.value.name}`,
    life: 3000
  })

  cancelAddItems()
}

const confirmRemoveItems = () => {
  if (!selectedItemGroup.value || !itemsToRemove.value) return

  // In a real implementation, this would call the store to remove items
  toast.add({
    severity: 'success',
    summary: 'Items Removed',
    detail: `Removed ${itemsToRemove.value} items from ${selectedItemGroup.value.name}`,
    life: 3000
  })

  cancelRemoveItems()
}

// New item group creation methods
const cancelAddItem = () => {
  showAddItemDialog.value = false
  // Reset form
  newItemName.value = ''
  newItemCategory.value = ''
  newItemDescription.value = ''
  newItemQuantity.value = 1
  newItemSerialPrefix.value = ''
}

const confirmAddItem = async () => {
  if (!canCreateItem.value) return

  try {
    loading.value = true

    // Generate unique ID for the item group
    const itemGroupId = `${newItemCategory.value}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Generate individual items
    const items = []
    const prefix = newItemSerialPrefix.value.toUpperCase() || 'ITEM'

    for (let i = 1; i <= newItemQuantity.value; i++) {
      const serialNumber = `${prefix}-${String(i).padStart(3, '0')}`
      items.push({
        id: `${itemGroupId}_item_${i}`,
        serial_number: serialNumber,
        condition: 'excellent',
        status: 'available',
        notes: '',
        episodes: {},
        use_count: 0,
        suspended: false
      })
    }

    // Create the new item group
    const newItemGroup = {
      id: itemGroupId,
      name: newItemName.value.trim(),
      description: newItemDescription.value.trim() || '',
      category_id: newItemCategory.value,
      type: newItemCategory.value,
      image_url: '',
      items: items,
      contraindications: [],
      created_at: new Date(),
      updated_at: new Date(),
      isActive: true,
      max_rental_days: 7,
      advance_booking_days: 30,
      requires_approval: false,
      storage_location: '',
      responsible_person: '',
      notes: ''
    }

    // Add to Firebase using the items store
    await itemsStore.createItemGroup(newItemGroup)

    toast.add({
      severity: 'success',
      summary: 'Item Group Created',
      detail: `${newItemName.value} has been created with ${newItemQuantity.value} items`,
      life: 3000
    })

    cancelAddItem()
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Creation Failed',
      detail: error instanceof Error ? error.message : 'Failed to create item group',
      life: 5000
    })
  } finally {
    loading.value = false
  }
}

const viewItemDetails = (itemGroup: any) => {
  toast.add({
    severity: 'info',
    summary: 'Item Details',
    detail: `Detailed view for ${itemGroup.name} would be implemented here`,
    life: 3000
  })
}

// Individual item management
const toggleItemSuspension = (itemGroup: any, item: any) => {
  // In a real implementation, this would update the item in the store
  toast.add({
    severity: 'info',
    summary: item.suspended ? 'Item Reactivated' : 'Item Suspended',
    detail: `${item.serial_number || 'Item'} has been ${item.suspended ? 'reactivated' : 'suspended'}`,
    life: 3000
  })
}

const editIndividualItem = (itemGroup: any, item: any) => {
  selectedItemGroup.value = itemGroup
  selectedIndividualItem.value = item

  // Populate form with current values
  editingSerialNumber.value = item.serial_number || ''
  editingAssetTag.value = item.asset_tag || ''
  editingItemModel.value = item.model || ''
  editingItemCondition.value = item.condition || 'good'
  editingLastMaintenance.value = item.last_maintenance ? new Date(item.last_maintenance) : null
  editingItemNotes.value = item.notes || ''
  editingItemSuspended.value = item.suspended || false
  editingSuspensionReason.value = item.suspension_reason || ''

  showEditItemDialogVisible.value = true
}

const cancelEditItem = () => {
  showEditItemDialogVisible.value = false
  selectedItemGroup.value = null
  selectedIndividualItem.value = null

  // Reset form
  editingSerialNumber.value = ''
  editingAssetTag.value = ''
  editingItemModel.value = ''
  editingItemCondition.value = ''
  editingLastMaintenance.value = null
  editingItemNotes.value = ''
  editingItemSuspended.value = false
  editingSuspensionReason.value = ''
}

const confirmEditItem = async () => {
  if (!selectedIndividualItem.value || !selectedItemGroup.value) return

  try {
    const updatedData = {
      serial_number: editingSerialNumber.value,
      asset_tag: editingAssetTag.value,
      model: editingItemModel.value,
      condition: editingItemCondition.value,
      last_maintenance: editingLastMaintenance.value,
      notes: editingItemNotes.value,
      suspended: editingItemSuspended.value,
      suspension_reason: editingItemSuspended.value ? editingSuspensionReason.value : ''
    }

    // Update the individual item in the store
    await itemsStore.updateIndividualItem(
      selectedItemGroup.value.id,
      selectedIndividualItem.value.id,
      updatedData
    )

    toast.add({
      severity: 'success',
      summary: 'Item Updated',
      detail: `Item details have been updated successfully`,
      life: 3000
    })

    cancelEditItem()
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Update Failed',
      detail: error instanceof Error ? error.message : 'Failed to update item',
      life: 5000
    })
  }
}

const viewRentalDetails = (rental: any) => {
  toast.add({
    severity: 'info',
    summary: 'Rental Details',
    detail: `Detailed view for episode ${rental.id} would be implemented here`,
    life: 3000
  })
}

const viewUserDetails = (user: any) => {
  toast.add({
    severity: 'info',
    summary: 'User Details',
    detail: `Detailed view for ${user.name} would be implemented here`,
    life: 3000
  })
}

// Calendar functionality
const formatCalendarMonth = (date: Date) => {
  return format(date, 'MMMM yyyy')
}

const previousMonth = () => {
  currentCalendarDate.value = new Date(
    currentCalendarDate.value.getFullYear(),
    currentCalendarDate.value.getMonth() - 1,
    1
  )
}

const nextMonth = () => {
  currentCalendarDate.value = new Date(
    currentCalendarDate.value.getFullYear(),
    currentCalendarDate.value.getMonth() + 1,
    1
  )
}

const calendarDays = computed(() => {
  const year = currentCalendarDate.value.getFullYear()
  const month = currentCalendarDate.value.getMonth()

  // Get first day of month and how many days in month
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const daysInMonth = lastDay.getDate()
  const startingDayOfWeek = firstDay.getDay()

  // Get days from previous month to fill the grid
  const daysFromPrevMonth = startingDayOfWeek
  const prevMonth = new Date(year, month - 1, 0)
  const daysInPrevMonth = prevMonth.getDate()

  // Get days from next month to fill the grid
  const totalCells = Math.ceil((daysInMonth + startingDayOfWeek) / 7) * 7
  const daysFromNextMonth = totalCells - daysInMonth - daysFromPrevMonth

  const days = []

  // Previous month days
  for (let i = daysFromPrevMonth; i > 0; i--) {
    const day = daysInPrevMonth - i + 1
    const date = new Date(year, month - 1, day)
    days.push({
      date: format(date, 'yyyy-MM-dd'),
      dayNumber: day,
      isCurrentMonth: false,
      isToday: false,
      bookings: getBookingsForDate(format(date, 'yyyy-MM-dd'))
    })
  }

  // Current month days
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month, day)
    const dateStr = format(date, 'yyyy-MM-dd')
    const today = format(new Date(), 'yyyy-MM-dd')

    days.push({
      date: dateStr,
      dayNumber: day,
      isCurrentMonth: true,
      isToday: dateStr === today,
      bookings: getBookingsForDate(dateStr)
    })
  }

  // Next month days
  for (let day = 1; day <= daysFromNextMonth; day++) {
    const date = new Date(year, month + 1, day)
    days.push({
      date: format(date, 'yyyy-MM-dd'),
      dayNumber: day,
      isCurrentMonth: false,
      isToday: false,
      bookings: getBookingsForDate(format(date, 'yyyy-MM-dd'))
    })
  }

  return days
})

const getBookingsForDate = (dateStr: string) => {
  // In a real implementation, this would filter from episodesStore
  return allRentals.value.filter(rental =>
    rental.dates && rental.dates.includes(dateStr)
  )
}

// Lifecycle
onMounted(async () => {
  // Load initial data
  loadingItems.value = true
  try {
    await itemsStore.initializeRealTimeListeners()
    // TODO: Load rental data from episodesStore
    // await episodesStore.fetchAllEpisodes()
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Loading Error',
      detail: 'Failed to load items data',
      life: 5000
    })
  } finally {
    loadingItems.value = false
  }
})
</script>

<style scoped>
.admin-container {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 2rem;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 2.5rem;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
}

.admin-tabs {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--surface-ground);
  border-radius: 8px;
}

.tab-content {
  background: var(--surface-card);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tab-panel {
  padding: 1.5rem;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.panel-header h2 {
  margin: 0;
  color: var(--text-color);
  font-size: 1.5rem;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

/* Category Sections */
.items-by-category {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.category-section {
  background: var(--surface-card);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid var(--surface-border);
}

.category-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--surface-border);
}

.category-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.category-icon {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.category-title h3 {
  margin: 0;
  color: var(--text-color);
  font-size: 1.5rem;
  font-weight: 600;
  flex: 1;
}

/* Items Grid Layout */
.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.item-card {
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.item-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.item-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.25rem;
  background: linear-gradient(135deg, var(--surface-50) 0%, var(--surface-100) 100%);
  border-bottom: 1px solid var(--surface-border);
}

.item-info h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 1.25rem;
  font-weight: 600;
}

.item-actions {
  display: flex;
  gap: 0.25rem;
}

.item-content {
  padding: 1.25rem;
}

.item-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--surface-50);
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.85rem;
  color: var(--text-color-secondary);
  margin-bottom: 0.25rem;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
}

.stat-value.available {
  color: var(--green-500);
}

.stat-value.suspended {
  color: var(--red-500);
}

/* Individual Items Section */
.individual-items {
  margin-bottom: 1.5rem;
}

.individual-items h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-size: 1rem;
  font-weight: 600;
}

.items-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--surface-border);
  border-radius: 6px;
}

.individual-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--surface-border);
  transition: background-color 0.2s ease;
}

.individual-item:last-child {
  border-bottom: none;
}

.individual-item:hover {
  background: var(--surface-50);
}

.individual-item.suspended {
  background: var(--red-50);
  opacity: 0.7;
}

.item-details {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.item-serial {
  font-weight: 500;
  color: var(--text-color);
}

.item-controls {
  display: flex;
  gap: 0.25rem;
}

/* Quantity Management Section */
.quantity-management {
  border-top: 1px solid var(--surface-border);
  padding-top: 1.5rem;
}

.quantity-management h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-size: 1rem;
  font-weight: 600;
}

.quantity-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--surface-50);
  border-radius: 8px;
}

.quantity-display {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.quantity-display label {
  font-size: 0.85rem;
  color: var(--text-color-secondary);
  font-weight: 500;
}

.current-quantity {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
}

.quantity-actions {
  display: flex;
  gap: 0.5rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--text-color-secondary);
}

.empty-icon {
  font-size: 4rem;
  color: var(--surface-400);
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 1.5rem;
}

.empty-state p {
  margin: 0 0 2rem 0;
  font-size: 1rem;
}

.rental-filters {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.filter-dropdown,
.filter-calendar {
  min-width: 200px;
}

.date-filter-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.clear-date-btn {
  padding: 0.25rem !important;
  min-width: auto !important;
  width: 2rem !important;
  height: 2rem !important;
}

/* Calendar View Styles */
.rental-view-toggle {
  display: flex;
  gap: 0.5rem;
}

.booking-calendar-view {
  background: var(--surface-card);
  border-radius: 12px;
  overflow: hidden;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--surface-50) 0%, var(--surface-100) 100%);
  border-bottom: 1px solid var(--surface-border);
}

.calendar-navigation {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.calendar-month {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  min-width: 200px;
  text-align: center;
}

.calendar-legend {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-color-secondary);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
}

.legend-color.scheduled {
  background: var(--blue-500);
}

.legend-color.rented {
  background: var(--green-500);
}

.legend-color.returned {
  background: var(--gray-500);
}

.calendar-grid {
  padding: 1.5rem;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  margin-bottom: 1px;
}

.weekday {
  padding: 1rem;
  text-align: center;
  font-weight: 600;
  color: var(--text-color-secondary);
  background: var(--surface-100);
  font-size: 0.9rem;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: var(--surface-border);
}

.calendar-day {
  min-height: 120px;
  background: var(--surface-card);
  padding: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.calendar-day:hover {
  background: var(--surface-50);
}

.calendar-day.other-month {
  background: var(--surface-100);
  opacity: 0.6;
}

.calendar-day.today {
  background: var(--primary-50);
  border: 2px solid var(--primary-color);
}

.calendar-day.has-bookings {
  border-left: 4px solid var(--primary-color);
}

.day-number {
  font-weight: 600;
  color: var(--text-color);
  font-size: 1rem;
}

.calendar-day.other-month .day-number {
  color: var(--text-color-secondary);
}

.calendar-day.today .day-number {
  color: var(--primary-color);
  font-weight: 700;
}

.day-bookings {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  overflow: hidden;
}

.booking-indicator {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.booking-indicator:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.booking-indicator.scheduled {
  background: var(--blue-100);
  color: var(--blue-700);
  border: 1px solid var(--blue-200);
}

.booking-indicator.rented {
  background: var(--green-100);
  color: var(--green-700);
  border: 1px solid var(--green-200);
}

.booking-indicator.returned {
  background: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-200);
}

.booking-indicator.cancelled {
  background: var(--red-100);
  color: var(--red-700);
  border: 1px solid var(--red-200);
}

.booking-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.more-bookings {
  font-size: 0.7rem;
  color: var(--text-color-secondary);
  text-align: center;
  font-style: italic;
  margin-top: 0.25rem;
}

.admin-table {
  margin-top: 1rem;
}

.episode-id {
  font-family: 'Courier New', monospace;
  background: var(--surface-100);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  text-align: center;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

/* Dialog Styles */
.dialog-content {
  padding: 0.5rem 0;
}

.dialog-header-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 0;
  background: var(--surface-50);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.group-icon,
.item-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-600) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.group-basic-info,
.item-basic-info {
  flex: 1;
}

.group-basic-info h3,
.item-basic-info h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 1.25rem;
  font-weight: 600;
}

.item-group-name {
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.form-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  border: 1px solid var(--surface-border);
  border-radius: 8px;
  padding: 1.5rem;
  background: var(--surface-0);
}

.form-section h4 {
  margin: 0 0 1.5rem 0;
  color: var(--text-color);
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--surface-border);
}

.form-section h4 i {
  color: var(--primary-color);
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.9rem;
}

.form-input {
  width: 100%;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--surface-50);
  border-radius: 6px;
  border: 1px solid var(--surface-border);
}

.checkbox-group label {
  margin: 0;
  font-weight: 500;
  color: var(--text-color);
}

.info-message,
.warning-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.9rem;
}

.info-message {
  background: var(--blue-50);
  color: var(--blue-700);
  border: 1px solid var(--blue-200);
}

.warning-message {
  background: var(--orange-50);
  color: var(--orange-700);
  border: 1px solid var(--orange-200);
}

.dialog-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--surface-border);
}

/* Force White Dialog Styles - Very Aggressive */
:deep(.force-white-dialog),
:deep(.force-white-dialog .p-dialog),
:deep(.force-white-dialog .p-dialog-content),
:deep(.force-white-dialog .p-component) {
  background: white !important;
  background-color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #e0e0e0 !important;
}

:deep(.force-white-dialog .p-dialog-header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  background-color: #667eea !important;
  color: white !important;
  border-radius: 12px 12px 0 0 !important;
  padding: 1.5rem 2rem !important;
  border-bottom: none !important;
}

:deep(.force-white-dialog .p-dialog-header .p-dialog-title) {
  color: white !important;
  font-weight: 600 !important;
  font-size: 1.25rem !important;
}

:deep(.force-white-dialog .p-dialog-header .p-dialog-header-icon),
:deep(.force-white-dialog .p-dialog-header .p-dialog-header-close) {
  color: white !important;
}

:deep(.force-white-dialog .p-dialog-content) {
  background: white !important;
  background-color: white !important;
  padding: 2rem !important;
  border-radius: 0 0 12px 12px !important;
  color: #333 !important;
}

:deep(.force-white-dialog .p-dialog-mask) {
  background: rgba(0, 0, 0, 0.5) !important;
}

/* Additional force styles */
.force-white-dialog {
  background: white !important;
}

.force-white-dialog * {
  background-color: inherit;
}

/* Fix All PrimeVue Dropdown and Overlay Transparency Issues */
:deep(.p-dropdown-panel),
:deep(.p-multiselect-panel),
:deep(.p-calendar-panel),
:deep(.p-overlay-panel),
:deep(.p-menu),
:deep(.p-contextmenu),
:deep(.p-tooltip),
:deep(.p-autocomplete-panel) {
  background: white !important;
  background-color: white !important;
  border: 1px solid #e0e0e0 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border-radius: 6px !important;
}

:deep(.p-dropdown-items),
:deep(.p-multiselect-items),
:deep(.p-calendar-panel .p-datepicker),
:deep(.p-autocomplete-items) {
  background: white !important;
  background-color: white !important;
}

:deep(.p-dropdown-item),
:deep(.p-multiselect-item),
:deep(.p-calendar-panel .p-datepicker-calendar td),
:deep(.p-autocomplete-item) {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

:deep(.p-dropdown-item:hover),
:deep(.p-multiselect-item:hover),
:deep(.p-calendar-panel .p-datepicker-calendar td:hover),
:deep(.p-autocomplete-item:hover) {
  background: #f8f9fa !important;
  background-color: #f8f9fa !important;
  color: #333 !important;
}

:deep(.p-dropdown-item.p-highlight),
:deep(.p-multiselect-item.p-highlight),
:deep(.p-calendar-panel .p-datepicker-calendar td.p-highlight),
:deep(.p-autocomplete-item.p-highlight) {
  background: var(--primary-color) !important;
  background-color: var(--primary-color) !important;
  color: white !important;
}

/* Additional Calendar specific fixes */
:deep(.p-calendar .p-inputtext) {
  background: white !important;
  color: #333 !important;
}

:deep(.p-calendar-panel .p-datepicker-header) {
  background: white !important;
  color: #333 !important;
}

:deep(.p-calendar-panel .p-datepicker-calendar thead th) {
  background: #f8f9fa !important;
  color: #333 !important;
}

@media (max-width: 768px) {
  .admin-container {
    padding: 1rem;
  }

  .admin-tabs {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .panel-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .category-section {
    padding: 1rem;
  }

  .category-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .category-title h3 {
    font-size: 1.25rem;
  }

  .items-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .item-card-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .item-stats {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .quantity-controls {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .quantity-actions {
    justify-content: center;
  }

  .rental-filters {
    flex-direction: column;
    gap: 0.75rem;
  }

  .filter-dropdown,
  .filter-calendar {
    min-width: unset;
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .individual-item {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .item-controls {
    justify-content: center;
  }

  /* Dialog responsive styles */
  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .dialog-header-info {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .form-section {
    padding: 1rem;
  }

  .custom-edit-dialog {
    width: 95vw !important;
    min-width: unset !important;
    max-width: unset !important;
  }

  .dialog-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .dialog-actions .p-button {
    width: 100%;
  }

  /* Calendar responsive styles */
  .calendar-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .calendar-legend {
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }

  .calendar-month {
    font-size: 1.25rem;
    min-width: unset;
  }

  .calendar-day {
    min-height: 80px;
    padding: 0.5rem;
  }

  .day-number {
    font-size: 0.9rem;
  }

  .booking-indicator {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }

  .rental-view-toggle {
    width: 100%;
    justify-content: center;
  }

  .rental-view-toggle .p-button {
    flex: 1;
    max-width: 150px;
  }
}
</style>
