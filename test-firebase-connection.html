<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Firebase Connection & Initialize Database</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #5a6fd8;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .config-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 14px;
            margin: 15px 0;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #667eea;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Firebase Connection Test & Database Initialization</h1>
        
        <div class="status info">
            <strong>📋 Firebase Configuration Loaded</strong><br>
            Testing connection to your Firebase project: <strong>tkoh-nsd-renting-platform</strong>
        </div>

        <div class="config-display">
            <strong>Firebase Config:</strong><br>
            Project ID: tkoh-nsd-renting-platform<br>
            Auth Domain: tkoh-nsd-renting-platform.firebaseapp.com<br>
            Storage Bucket: tkoh-nsd-renting-platform.firebasestorage.app<br>
            App ID: 1:196085217905:web:d406f7c569834ef30b79d0
        </div>

        <div class="step">
            <h3>Step 1: Test Firebase Connection</h3>
            <button class="button" onclick="testConnection()" id="testBtn">
                🔗 Test Firebase Connection
            </button>
            <div id="connectionStatus"></div>
        </div>

        <div class="step">
            <h3>Step 2: Initialize Database Structure</h3>
            <button class="button" onclick="initializeDatabase()" id="initBtn" disabled>
                🗄️ Create Database Structure
            </button>
            <div id="initStatus"></div>
        </div>

        <div class="step">
            <h3>Step 3: Verify Database</h3>
            <button class="button" onclick="verifyDatabase()" id="verifyBtn" disabled>
                ✅ Verify Database Creation
            </button>
            <div id="verifyStatus"></div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="button" onclick="window.location.href='http://localhost:5174/admin'" style="background: #28a745;">
                ⚙️ Go to Admin Panel
            </button>
            <button class="button" onclick="window.location.href='http://localhost:5174/booking'" style="background: #17a2b8;">
                📅 Test Booking System
            </button>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, collection, doc, setDoc, getDocs } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBkGFsQnyuYtdTl3vz7VqUOWwrLgfjigmI",
            authDomain: "tkoh-nsd-renting-platform.firebaseapp.com",
            projectId: "tkoh-nsd-renting-platform",
            storageBucket: "tkoh-nsd-renting-platform.firebasestorage.app",
            messagingSenderId: "196085217905",
            appId: "1:196085217905:web:d406f7c569834ef30b79d0"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Database structure
        const databaseStructure = {
            categories: [
                {
                    id: 'resuscitation-training',
                    name: 'Resuscitation Training Equipment',
                    type: 'resus_trainings',
                    description: 'Equipment for resuscitation and emergency response training',
                    icon: 'pi pi-heart',
                    order: 1
                },
                {
                    id: 'training-venue',
                    name: 'Training Venue',
                    type: 'venues',
                    description: 'Training venues and meeting rooms',
                    icon: 'pi pi-building',
                    order: 2
                },
                {
                    id: 'audio-visual',
                    name: 'Audio Visual Equipment',
                    type: 'audio_visuals',
                    description: 'Audio visual equipment for presentations and training',
                    icon: 'pi pi-video',
                    order: 3
                }
            ],
            itemGroups: [
                {
                    id: 'manikin',
                    name: 'Manikin',
                    category_id: 'resuscitation-training',
                    type: 'resus_trainings',
                    description: 'Training manikin for CPR and resuscitation practice',
                    image_url: '',
                    items: [
                        { id: 'manikin-001', serial_number: 'MAN-001', condition: 'excellent', notes: 'Adult CPR training manikin', status: 'available' },
                        { id: 'manikin-002', serial_number: 'MAN-002', condition: 'good', notes: 'Adult CPR training manikin', status: 'available' }
                    ],
                    created_at: new Date(),
                    updated_at: new Date()
                },
                {
                    id: 'sim-train-equipment',
                    name: 'Sim Train Equipment',
                    category_id: 'resuscitation-training',
                    type: 'resus_trainings',
                    description: 'Simulation training equipment for emergency scenarios',
                    image_url: '',
                    items: [
                        { id: 'sim-001', serial_number: 'SIM-001', condition: 'excellent', notes: 'Complete simulation training kit', status: 'available' }
                    ],
                    created_at: new Date(),
                    updated_at: new Date()
                },
                {
                    id: 'rtrc',
                    name: 'RTRC',
                    category_id: 'training-venue',
                    type: 'venues',
                    description: 'Resuscitation Training Resource Centre - Main training venue',
                    image_url: '',
                    items: [
                        { id: 'rtrc-001', serial_number: 'VENUE-001', condition: 'excellent', notes: 'Main training room with full equipment', status: 'available' }
                    ],
                    created_at: new Date(),
                    updated_at: new Date()
                },
                {
                    id: 'smv-tv',
                    name: 'SMV TV',
                    category_id: 'audio-visual',
                    type: 'audio_visuals',
                    description: 'Smart TV for presentations and training videos',
                    image_url: '',
                    items: [
                        { id: 'tv-001', serial_number: 'TV-001', condition: 'excellent', notes: '55-inch Smart TV with wireless connectivity', status: 'available' }
                    ],
                    created_at: new Date(),
                    updated_at: new Date()
                },
                {
                    id: 'mobile-phone-stabilizer',
                    name: 'Mobile Phone Stabilizer',
                    category_id: 'audio-visual',
                    type: 'audio_visuals',
                    description: 'Gimbal stabilizer for mobile phone video recording',
                    image_url: '',
                    items: [
                        { id: 'stabilizer-001', serial_number: 'STAB-001', condition: 'excellent', notes: '3-axis gimbal stabilizer', status: 'available' },
                        { id: 'stabilizer-002', serial_number: 'STAB-002', condition: 'good', notes: '3-axis gimbal stabilizer', status: 'available' }
                    ],
                    created_at: new Date(),
                    updated_at: new Date()
                },
                {
                    id: 'digital-camera',
                    name: 'Digital Camera',
                    category_id: 'audio-visual',
                    type: 'audio_visuals',
                    description: 'Professional digital camera for training documentation',
                    image_url: '',
                    items: [
                        { id: 'camera-001', serial_number: 'CAM-001', condition: 'excellent', notes: 'DSLR camera with lens kit', status: 'available' }
                    ],
                    created_at: new Date(),
                    updated_at: new Date()
                }
            ]
        };

        // Make functions available globally
        window.testConnection = async function() {
            const btn = document.getElementById('testBtn');
            const status = document.getElementById('connectionStatus');
            
            btn.disabled = true;
            btn.textContent = '🔄 Testing Connection...';
            
            try {
                // Test connection by trying to access Firestore
                const testCollection = collection(db, 'test');
                await getDocs(testCollection);
                
                status.innerHTML = '<div class="status success">✅ <strong>Firebase Connection Successful!</strong><br>Connected to project: tkoh-nsd-renting-platform</div>';
                
                // Enable next step
                document.getElementById('initBtn').disabled = false;
                btn.textContent = '✅ Connection Tested';
                btn.style.background = '#28a745';
                
            } catch (error) {
                status.innerHTML = `<div class="status error">❌ <strong>Connection Failed:</strong><br>${error.message}</div>`;
                btn.disabled = false;
                btn.textContent = '🔗 Test Firebase Connection';
            }
        };

        window.initializeDatabase = async function() {
            const btn = document.getElementById('initBtn');
            const status = document.getElementById('initStatus');
            
            btn.disabled = true;
            btn.textContent = '⏳ Creating Database...';
            
            try {
                status.innerHTML = '<div class="status info">🔄 Creating categories...</div>';
                
                // Create categories
                for (const category of databaseStructure.categories) {
                    await setDoc(doc(db, 'categories', category.id), category);
                }
                
                status.innerHTML = '<div class="status info">🔄 Creating item groups...</div>';
                
                // Create item groups
                for (const itemGroup of databaseStructure.itemGroups) {
                    await setDoc(doc(db, 'item_groups', itemGroup.id), itemGroup);
                }
                
                status.innerHTML = `
                    <div class="status success">
                        ✅ <strong>Database Created Successfully!</strong><br><br>
                        📁 Created ${databaseStructure.categories.length} categories<br>
                        📦 Created ${databaseStructure.itemGroups.length} item groups<br>
                        🔧 Created ${databaseStructure.itemGroups.reduce((total, group) => total + group.items.length, 0)} individual items
                    </div>
                `;
                
                // Enable verification
                document.getElementById('verifyBtn').disabled = false;
                btn.textContent = '✅ Database Created';
                btn.style.background = '#28a745';
                
            } catch (error) {
                status.innerHTML = `<div class="status error">❌ <strong>Database Creation Failed:</strong><br>${error.message}</div>`;
                btn.disabled = false;
                btn.textContent = '🗄️ Create Database Structure';
            }
        };

        window.verifyDatabase = async function() {
            const btn = document.getElementById('verifyBtn');
            const status = document.getElementById('verifyStatus');
            
            btn.disabled = true;
            btn.textContent = '🔍 Verifying...';
            
            try {
                // Check categories
                const categoriesSnapshot = await getDocs(collection(db, 'categories'));
                const categoriesCount = categoriesSnapshot.size;
                
                // Check item groups
                const itemGroupsSnapshot = await getDocs(collection(db, 'item_groups'));
                const itemGroupsCount = itemGroupsSnapshot.size;
                
                status.innerHTML = `
                    <div class="status success">
                        ✅ <strong>Database Verification Complete!</strong><br><br>
                        📁 Found ${categoriesCount} categories in Firebase<br>
                        📦 Found ${itemGroupsCount} item groups in Firebase<br><br>
                        <strong>🎉 Your rental platform database is ready!</strong><br>
                        You can now use the admin panel and booking system.
                    </div>
                `;
                
                btn.textContent = '✅ Verified';
                btn.style.background = '#28a745';
                
            } catch (error) {
                status.innerHTML = `<div class="status error">❌ <strong>Verification Failed:</strong><br>${error.message}</div>`;
                btn.disabled = false;
                btn.textContent = '✅ Verify Database Creation';
            }
        };
    </script>
</body>
</html>
