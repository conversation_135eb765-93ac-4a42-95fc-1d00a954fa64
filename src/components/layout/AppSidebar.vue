<template>
  <aside class="app-sidebar" :class="{ 'sidebar-collapsed': collapsed }">
    <div class="sidebar-content">
      <!-- Sidebar Header -->
      <div class="sidebar-header">
        <div class="sidebar-logo" v-if="!collapsed">
          <i class="pi pi-th-large"></i>
          <span>Navigation</span>
        </div>
        <Button
          icon="pi pi-bars"
          severity="secondary"
          text
          rounded
          class="sidebar-toggle"
          @click="toggleSidebar"
          v-tooltip.right="collapsed ? 'Expand Menu' : 'Collapse Menu'"
        />
      </div>

      <!-- Navigation menu -->
      <nav class="sidebar-nav">
        <div class="nav-section">
          <h5 v-if="!collapsed" class="nav-section-title">Main Menu</h5>
          <ul class="nav-list">
            <li v-for="item in mainNavigationItems" :key="item.name" class="nav-item">
              <router-link
                :to="item.to"
                class="nav-link"
                :class="{ 'nav-link-active': isActiveRoute(item.to) }"
                v-tooltip.right="collapsed ? item.label : ''"
              >
                <div class="nav-icon-wrapper">
                  <i :class="item.icon" class="nav-icon"></i>
                </div>
                <span v-if="!collapsed" class="nav-label">{{ item.label }}</span>
                <Badge
                  v-if="item.badge && !collapsed"
                  :value="item.badge"
                  severity="danger"
                  class="nav-badge"
                />
              </router-link>
            </li>
          </ul>
        </div>

        <div v-if="authStore.isAdmin" class="nav-section">
          <h5 v-if="!collapsed" class="nav-section-title">Administration</h5>
          <ul class="nav-list">
            <li v-for="item in adminNavigationItems" :key="item.name" class="nav-item">
              <router-link
                :to="item.to"
                class="nav-link"
                :class="{ 'nav-link-active': isActiveRoute(item.to) }"
                v-tooltip.right="collapsed ? item.label : ''"
              >
                <div class="nav-icon-wrapper">
                  <i :class="item.icon" class="nav-icon"></i>
                </div>
                <span v-if="!collapsed" class="nav-label">{{ item.label }}</span>
                <Badge
                  v-if="item.badge && !collapsed"
                  :value="item.badge"
                  severity="warning"
                  class="nav-badge"
                />
              </router-link>
            </li>
          </ul>
        </div>
      </nav>

      <!-- Quick stats (when expanded) -->
      <div v-if="!collapsed" class="sidebar-footer">
        <div class="stats-panel">
          <h4 class="stats-title">
            <i class="pi pi-chart-bar"></i>
            Quick Overview
          </h4>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="pi pi-box"></i>
              </div>
              <div class="stat-info">
                <span class="stat-value">{{ availableItemsCount }}</span>
                <span class="stat-label">Available</span>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <i class="pi pi-calendar"></i>
              </div>
              <div class="stat-info">
                <span class="stat-value">{{ userActiveRentalsCount }}</span>
                <span class="stat-label">My Rentals</span>
              </div>
            </div>
            <div v-if="authStore.isAdmin" class="stat-card">
              <div class="stat-icon">
                <i class="pi pi-clock"></i>
              </div>
              <div class="stat-info">
                <span class="stat-value">{{ pendingApprovalsCount }}</span>
                <span class="stat-label">Pending</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useItemsStore } from '@/stores/items'
import { useRentalsStore } from '@/stores/rentals'

const route = useRoute()
const authStore = useAuthStore()
const itemsStore = useItemsStore()
const rentalsStore = useRentalsStore()

const collapsed = ref(false)

const mainNavigationItems = computed(() => {
  const userRole = authStore.currentUserProfile?.role
  console.log('AppSidebar - Current user role:', userRole)
  console.log('AppSidebar - Current user profile:', authStore.currentUserProfile)
  const baseItems = []

  // For Admin role, show all navigation items
  if (userRole === 'Admin') {
    baseItems.push(
      {
        name: 'home',
        label: 'Dashboard',
        icon: 'pi pi-home',
        to: '/'
      },
      {
        name: 'booking',
        label: 'New Booking',
        icon: 'pi pi-plus-circle',
        to: '/booking'
      },
      {
        name: 'items',
        label: 'Browse Items',
        icon: 'pi pi-box',
        to: '/items'
      },
      {
        name: 'my-rentals',
        label: 'My Rentals',
        icon: 'pi pi-calendar',
        to: '/my-rentals',
        badge: rentalsStore.userActiveRentals.length || undefined
      },
      {
        name: 'profile',
        label: 'Profile',
        icon: 'pi pi-user',
        to: '/profile'
      }
    )
  } else {
    // For Personal and Unit roles, only show New Booking and My Rentals

    // Only show booking for Personal role (not Unit)
    if (userRole === 'Personal') {
      baseItems.push({
        name: 'booking',
        label: 'New Booking',
        icon: 'pi pi-plus-circle',
        to: '/booking'
      })
    }

    // Show My Rentals for both Personal and Unit roles
    baseItems.push({
      name: 'my-rentals',
      label: userRole === 'Unit' ? 'Unit Rentals' : 'My Rentals',
      icon: 'pi pi-calendar',
      to: '/my-rentals',
      badge: rentalsStore.userActiveRentals.length || undefined
    })
  }

  return baseItems
})

const adminNavigationItems = computed(() => [
  {
    name: 'admin',
    label: 'Admin Panel',
    icon: 'pi pi-cog',
    to: '/admin',
    badge: rentalsStore.pendingRentals.length || undefined
  }
])

const availableItemsCount = computed(() => 
  itemsStore.availableItems?.length || 0
)

const userActiveRentalsCount = computed(() => 
  rentalsStore.userActiveRentals.length
)

const pendingApprovalsCount = computed(() => 
  rentalsStore.pendingRentals.length
)

const toggleSidebar = () => {
  collapsed.value = !collapsed.value
}

const isActiveRoute = (to: string) => {
  if (to === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(to)
}
</script>

<style scoped>
.app-sidebar {
  width: 280px;
  background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  border-right: 1px solid #e9ecef;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
}

.sidebar-collapsed {
  width: 80px;
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Sidebar Header */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1rem;
  border-bottom: 1px solid #e9ecef;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  font-size: 1rem;
}

.sidebar-logo i {
  font-size: 1.25rem;
}

.sidebar-toggle {
  color: rgba(255, 255, 255, 0.9) !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 2rem;
}

.nav-section:last-child {
  margin-bottom: 0;
}

.nav-section-title {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #6c757d;
  margin: 0 0 1rem 1rem;
  padding: 0 0.5rem;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  margin: 0 0.75rem;
  text-decoration: none;
  color: #495057;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  font-weight: 500;
}

.nav-link:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #667eea;
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.nav-link-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.nav-link-active:hover {
  transform: translateX(2px);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.nav-icon-wrapper {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  background: rgba(102, 126, 234, 0.1);
  margin-right: 0.75rem;
  transition: all 0.3s ease;
}

.nav-link:hover .nav-icon-wrapper {
  background: rgba(102, 126, 234, 0.2);
}

.nav-link-active .nav-icon-wrapper {
  background: rgba(255, 255, 255, 0.2);
}

.nav-icon {
  font-size: 1.1rem;
  color: #667eea;
  transition: all 0.3s ease;
}

.nav-link:hover .nav-icon {
  color: #667eea;
}

.nav-link-active .nav-icon {
  color: white;
}

.nav-label {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  font-size: 0.95rem;
}

.nav-badge {
  margin-left: auto;
  font-size: 0.7rem;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.stats-panel {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stats-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  font-weight: 600;
  color: #495057;
  margin: 0 0 1rem 0;
}

.stats-title i {
  color: #667eea;
}

.stats-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.stat-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  font-size: 0.9rem;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  color: #6c757d;
  line-height: 1;
}

/* Collapsed state adjustments */
.sidebar-collapsed .nav-icon-wrapper {
  margin-right: 0;
}

.sidebar-collapsed .nav-link {
  justify-content: center;
  margin: 0 0.5rem;
}

.sidebar-collapsed .nav-section-title,
.sidebar-collapsed .nav-label,
.sidebar-collapsed .nav-badge {
  display: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .app-sidebar {
    width: 260px;
  }

  .sidebar-collapsed {
    width: 70px;
  }
}

@media (max-width: 768px) {
  .app-sidebar {
    position: fixed;
    left: -280px;
    top: 0;
    height: 100vh;
    z-index: 1001;
    transition: left 0.3s ease;
    width: 280px;
  }

  .app-sidebar.sidebar-open {
    left: 0;
  }

  .sidebar-collapsed {
    left: -70px;
    width: 70px;
  }

  .sidebar-collapsed.sidebar-open {
    left: 0;
  }
}
</style>
