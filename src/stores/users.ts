import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy,
  onSnapshot,
  type Unsubscribe
} from 'firebase/firestore'
import { db } from '@/firebase/config'

export type UserRole = 'Personal' | 'Unit' | 'Admin'

export interface UserAccount {
  id: string
  employee_number: string
  email: string
  displayName: string
  department?: string
  unit?: string
  role: UserRole
  password: string
  isActive: boolean
  createdAt: Date
  lastLogin?: Date
  createdBy: string
  notes?: string
}

export interface CreateUserData {
  employee_number: string
  email: string
  displayName: string
  department?: string
  unit?: string
  role: UserRole
  notes?: string
}

export const useUsersStore = defineStore('users', () => {
  // State
  const users = ref<UserAccount[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // Real-time listener
  let unsubscribeUsers: Unsubscribe | null = null

  // Getters
  const activeUsers = computed(() => users.value.filter(user => user.isActive))
  const usersByRole = computed(() => {
    const grouped: Record<UserRole, UserAccount[]> = {
      'Personal': [],
      'Unit': [],
      'Admin': []
    }
    
    activeUsers.value.forEach(user => {
      grouped[user.role].push(user)
    })
    
    return grouped
  })

  const getUserByEmployeeNumber = computed(() => {
    return (employeeNumber: string) => 
      users.value.find(user => user.employee_number === employeeNumber && user.isActive)
  })

  // Actions
  const fetchUsers = async () => {
    loading.value = true
    error.value = null
    
    try {
      const usersRef = collection(db, 'user_list')
      const q = query(usersRef, orderBy('createdAt', 'desc'))
      const snapshot = await getDocs(q)
      
      users.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        lastLogin: doc.data().lastLogin?.toDate()
      })) as UserAccount[]
      
    } catch (err) {
      console.error('Error fetching users:', err)
      error.value = 'Failed to fetch users'
      throw err
    } finally {
      loading.value = false
    }
  }

  const startUsersListener = () => {
    if (unsubscribeUsers) {
      unsubscribeUsers()
    }

    const usersRef = collection(db, 'user_list')
    const q = query(usersRef, orderBy('createdAt', 'desc'))
    
    unsubscribeUsers = onSnapshot(q, (snapshot) => {
      users.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        lastLogin: doc.data().lastLogin?.toDate()
      })) as UserAccount[]
    }, (err) => {
      console.error('Error in users listener:', err)
      error.value = 'Failed to listen to user updates'
    })
  }

  const stopUsersListener = () => {
    if (unsubscribeUsers) {
      unsubscribeUsers()
      unsubscribeUsers = null
    }
  }

  const createUser = async (userData: CreateUserData, createdBy: string): Promise<string> => {
    loading.value = true
    error.value = null
    
    try {
      // Check if employee number already exists
      const existingUser = users.value.find(user => 
        user.employee_number === userData.employee_number && user.isActive
      )
      
      if (existingUser) {
        throw new Error(`Employee number ${userData.employee_number} already exists`)
      }

      // Check if email already exists
      const existingEmail = users.value.find(user => 
        user.email === userData.email && user.isActive
      )
      
      if (existingEmail) {
        throw new Error(`Email ${userData.email} already exists`)
      }

      const userDoc = doc(collection(db, 'user_list'))
      const newUser: Omit<UserAccount, 'id'> = {
        ...userData,
        password: userData.employee_number, // Default password is employee number
        isActive: true,
        createdAt: new Date(),
        createdBy
      }

      await setDoc(userDoc, newUser)
      return userDoc.id
      
    } catch (err) {
      console.error('Error creating user:', err)
      error.value = err instanceof Error ? err.message : 'Failed to create user'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateUser = async (userId: string, updates: Partial<UserAccount>) => {
    loading.value = true
    error.value = null
    
    try {
      const userRef = doc(db, 'user_list', userId)
      await updateDoc(userRef, {
        ...updates,
        updatedAt: new Date()
      })
      
    } catch (err) {
      console.error('Error updating user:', err)
      error.value = 'Failed to update user'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deactivateUser = async (userId: string) => {
    await updateUser(userId, { isActive: false })
  }

  const reactivateUser = async (userId: string) => {
    await updateUser(userId, { isActive: true })
  }

  const deleteUser = async (userId: string) => {
    loading.value = true
    error.value = null
    
    try {
      const userRef = doc(db, 'user_list', userId)
      await deleteDoc(userRef)
      
    } catch (err) {
      console.error('Error deleting user:', err)
      error.value = 'Failed to delete user'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateLastLogin = async (employeeNumber: string) => {
    try {
      const user = getUserByEmployeeNumber.value(employeeNumber)
      if (user) {
        await updateUser(user.id, { lastLogin: new Date() })
      }
    } catch (err) {
      console.error('Error updating last login:', err)
      // Don't throw error for login tracking
    }
  }

  const clearError = () => {
    error.value = null
  }

  const changePassword = async (employeeNumber: string, newPassword: string) => {
    if (!db) throw new Error('Database not initialized')

    loading.value = true
    error.value = null

    try {
      const user = users.value.find(u => u.employee_number === employeeNumber && u.isActive)
      if (!user) {
        throw new Error('User not found')
      }

      const userDocRef = doc(db, 'user_list', user.id)
      await updateDoc(userDocRef, {
        password: newPassword
      })

      // Update local state
      const userIndex = users.value.findIndex(u => u.id === user.id)
      if (userIndex !== -1) {
        users.value[userIndex].password = newPassword
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to change password'
      throw err
    } finally {
      loading.value = false
    }
  }

  const resetPassword = async (employeeNumber: string) => {
    return changePassword(employeeNumber, employeeNumber)
  }

  return {
    // State
    users: readonly(users),
    loading: readonly(loading),
    error: readonly(error),
    
    // Getters
    activeUsers,
    usersByRole,
    getUserByEmployeeNumber,
    
    // Actions
    fetchUsers,
    startUsersListener,
    stopUsersListener,
    createUser,
    updateUser,
    deactivateUser,
    reactivateUser,
    deleteUser,
    updateLastLogin,
    changePassword,
    resetPassword,
    clearError
  }
})
