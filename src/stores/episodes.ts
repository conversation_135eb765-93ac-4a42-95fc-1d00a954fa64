import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  query, 
  where, 
  orderBy,
  onSnapshot,
  runTransaction,
  type Unsubscribe
} from 'firebase/firestore'
import { db, isDemoMode } from '@/firebase/config'

// User episode structure
export interface UserEpisode {
  id: string
  employee_number: string
  dates: string[] // Array of dates in YYYY-MM-DD format
  items: Array<{
    item_group_id: string
    item_group_name: string
    item_group_type: 'resus_trainings' | 'venues' | 'audio_visuals'
    individual_item_id: string
    quantity: number
  }>
  status: 'cancelled' | 'scheduled' | 'rented' | 'returned'
  created_at: Date
  updated_at: Date
}

// Cart item structure
export interface CartItem {
  item_group_id: string
  item_group_name: string
  item_group_type: 'resus_trainings' | 'venues' | 'audio_visuals'
  quantity: number
  max_quantity: number
  contraindications: Array<{
    item_group_name: string
    reason: string
  }>
}

export const useEpisodesStore = defineStore('episodes', () => {
  // State
  const userEpisodes = ref<UserEpisode[]>([])
  const cart = ref<CartItem[]>([])
  const selectedDates = ref<string[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // Real-time listeners
  let userEpisodesUnsubscribe: Unsubscribe | null = null

  // Getters
  const episodesByStatus = computed(() => {
    const grouped: Record<string, UserEpisode[]> = {
      scheduled: [],
      rented: [],
      returned: [],
      cancelled: []
    }
    
    userEpisodes.value.forEach(episode => {
      if (grouped[episode.status]) {
        grouped[episode.status].push(episode)
      }
    })
    
    return grouped
  })

  const cartTotal = computed(() => {
    return cart.value.reduce((total, item) => total + item.quantity, 0)
  })

  const cartItemGroups = computed(() => {
    return cart.value.map(item => item.item_group_name)
  })

  // Actions
  const initializeUserEpisodesListener = (employeeNumber: string) => {
    if (isDemoMode) {
      // Demo mode - create some sample episodes for demonstration
      const now = new Date()
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)

      userEpisodes.value = [
        {
          id: 'demo_episode_1',
          employee_number: employeeNumber,
          dates: [tomorrow.toISOString().split('T')[0]],
          items: [
            {
              item_group_id: 'demo_group_1',
              item_group_name: 'Demo Training Equipment',
              item_group_type: 'resus_trainings',
              individual_item_id: 'demo_item_1',
              quantity: 1
            }
          ],
          status: 'scheduled',
          created_at: yesterday,
          updated_at: yesterday
        },
        {
          id: 'demo_episode_2',
          employee_number: employeeNumber,
          dates: [yesterday.toISOString().split('T')[0]],
          items: [
            {
              item_group_id: 'demo_group_2',
              item_group_name: 'Demo Venue',
              item_group_type: 'venues',
              individual_item_id: 'demo_item_2',
              quantity: 1
            }
          ],
          status: 'returned',
          created_at: new Date(yesterday.getTime() - 24 * 60 * 60 * 1000),
          updated_at: yesterday
        }
      ]
      return
    }

    const userEpisodesQuery = query(
      collection(db!, `users/${employeeNumber}/episodes`),
      orderBy('created_at', 'desc')
    )
    
    userEpisodesUnsubscribe = onSnapshot(userEpisodesQuery, (snapshot) => {
      userEpisodes.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        created_at: doc.data().created_at?.toDate() || new Date(),
        updated_at: doc.data().updated_at?.toDate() || new Date()
      })) as UserEpisode[]
    }, (err) => {
      console.error('Error listening to user episodes:', err)
      error.value = 'Failed to load user episodes'
    })
  }

  const stopUserEpisodesListener = () => {
    if (userEpisodesUnsubscribe) {
      userEpisodesUnsubscribe()
      userEpisodesUnsubscribe = null
    }
  }

  const addToCart = (item: CartItem): boolean => {
    // Check contraindications
    const conflictingItems = checkContraindications(item)
    if (conflictingItems.length > 0) {
      // Return false to indicate contraindication check needed
      return false
    }

    const existingIndex = cart.value.findIndex(
      cartItem => cartItem.item_group_id === item.item_group_id
    )

    if (existingIndex >= 0) {
      // Update quantity if item already in cart
      const newQuantity = cart.value[existingIndex].quantity + item.quantity
      if (newQuantity <= item.max_quantity) {
        cart.value[existingIndex].quantity = newQuantity
      }
    } else {
      // Add new item to cart
      cart.value.push(item)
    }

    return true
  }

  const removeFromCart = (itemGroupId: string) => {
    const index = cart.value.findIndex(item => item.item_group_id === itemGroupId)
    if (index >= 0) {
      cart.value.splice(index, 1)
    }
  }

  const updateCartItemQuantity = (itemGroupId: string, quantity: number) => {
    const item = cart.value.find(item => item.item_group_id === itemGroupId)
    if (item) {
      if (quantity <= 0) {
        removeFromCart(itemGroupId)
      } else if (quantity <= item.max_quantity) {
        item.quantity = quantity
      }
    }
  }

  const clearCart = () => {
    cart.value = []
  }

  const checkContraindications = (newItem: CartItem): Array<{item_group_name: string, reason: string}> => {
    const conflicts: Array<{item_group_name: string, reason: string}> = []
    
    cart.value.forEach(cartItem => {
      // Check if new item has contraindications with existing cart items
      newItem.contraindications.forEach(contraindication => {
        if (contraindication.item_group_name === cartItem.item_group_name) {
          conflicts.push(contraindication)
        }
      })
      
      // Check if existing cart items have contraindications with new item
      cartItem.contraindications.forEach(contraindication => {
        if (contraindication.item_group_name === newItem.item_group_name) {
          conflicts.push(contraindication)
        }
      })
    })
    
    return conflicts
  }

  const setSelectedDates = (dates: string[]) => {
    selectedDates.value = dates
  }

  const submitCart = async (employeeNumber: string): Promise<string> => {
    if (cart.value.length === 0 || selectedDates.value.length === 0) {
      throw new Error('Cart is empty or no dates selected')
    }

    loading.value = true
    error.value = null

    try {
      if (isDemoMode) {
        // Demo mode - simulate successful submission and add to episodes
        const episodeId = `demo_episode_${Date.now()}`
        console.log('Demo mode: Simulating cart submission for employee:', employeeNumber)

        // Create a new episode from cart data
        const newEpisode: UserEpisode = {
          id: episodeId,
          employee_number: employeeNumber,
          dates: [...selectedDates.value],
          items: cart.value.map(cartItem => ({
            item_group_id: cartItem.item_group_id,
            item_group_name: cartItem.item_group_name,
            item_group_type: cartItem.item_group_type,
            individual_item_id: `demo_item_${Date.now()}`,
            quantity: cartItem.quantity
          })),
          status: 'scheduled',
          created_at: new Date(),
          updated_at: new Date()
        }

        // Add to the beginning of the episodes array
        userEpisodes.value.unshift(newEpisode)

        // Clear cart and dates
        clearCart()
        selectedDates.value = []

        return episodeId
      }

      const episodeId = await runTransaction(db!, async (transaction) => {
        const now = new Date()
        const episodeId = `episode_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

        // STEP 1: DO ALL READS FIRST
        const itemGroupDocs: any[] = []
        const userRef = doc(db, 'users', employeeNumber)

        // Read all item groups first
        for (const cartItem of cart.value) {
          const itemGroupRef = doc(db, 'item_groups', cartItem.item_group_id)
          const itemGroupDoc = await transaction.get(itemGroupRef)

          if (!itemGroupDoc.exists()) {
            throw new Error(`Item group ${cartItem.item_group_name} not found`)
          }

          itemGroupDocs.push({
            ref: itemGroupRef,
            doc: itemGroupDoc,
            cartItem: cartItem
          })
        }

        // Read user document
        const userDoc = await transaction.get(userRef)

        // STEP 2: PROCESS DATA AND PREPARE WRITES
        const episodeItems: Array<{
          item_group_id: string
          item_group_name: string
          item_group_type: 'resus_trainings' | 'venues' | 'audio_visuals'
          individual_item_id: string
          quantity: number
        }> = []

        // Process each item group
        for (const { ref: itemGroupRef, doc: itemGroupDoc, cartItem } of itemGroupDocs) {
          const groupData = itemGroupDoc.data()
          const items = groupData.items || []

          // Find available items with lowest use_count
          const availableItems = items
            .filter((item: any) => {
              if (item.suspended) return false

              // Check if item is available on selected dates
              for (const episodeId in item.episodes || {}) {
                const episode = item.episodes[episodeId]
                if (episode.status === 'scheduled' || episode.status === 'rented') {
                  const hasConflict = selectedDates.value.some(date =>
                    episode.dates.includes(date)
                  )
                  if (hasConflict) return false
                }
              }
              return true
            })
            .sort((a: any, b: any) => (a.use_count || 0) - (b.use_count || 0))

          if (availableItems.length < cartItem.quantity) {
            throw new Error(`Insufficient stock for ${cartItem.item_group_name}`)
          }

          // Select items with lowest use_count
          const selectedItems = availableItems.slice(0, cartItem.quantity)

          selectedItems.forEach((item: any) => {
            episodeItems.push({
              item_group_id: cartItem.item_group_id,
              item_group_name: cartItem.item_group_name,
              item_group_type: cartItem.item_group_type,
              individual_item_id: item.id,
              quantity: 1
            })

            // Update item's episodes and use_count
            if (!item.episodes) item.episodes = {}
            item.episodes[episodeId] = {
              dates: [...selectedDates.value],
              status: 'scheduled'
            }
            item.use_count = (item.use_count || 0) + 1
          })

          // STEP 3: DO ALL WRITES
          transaction.update(itemGroupRef, { items, updated_at: now })
        }

        // Create user episode
        const userEpisodeRef = doc(db, `users/${employeeNumber}/episodes`, episodeId)
        transaction.set(userEpisodeRef, {
          id: episodeId,
          employee_number: employeeNumber,
          dates: [...selectedDates.value],
          items: episodeItems,
          status: 'scheduled',
          created_at: now,
          updated_at: now
        })

        // Update user's episodes in main user document
        if (userDoc.exists()) {
          const userData = userDoc.data()
          const episodes = userData.episodes || {}
          episodes[episodeId] = {
            id: episodeId,
            dates: [...selectedDates.value],
            items: episodeItems,
            status: 'scheduled'
          }
          transaction.update(userRef, { episodes, updated_at: now })
        }

        return episodeId
      })

      // Clear cart after successful submission
      clearCart()
      selectedDates.value = []

      return episodeId
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to submit booking'
      throw err
    } finally {
      loading.value = false
    }
  }

  const cancelEpisode = async (employeeNumber: string, episodeId: string): Promise<void> => {
    loading.value = true
    error.value = null

    try {
      if (isDemoMode) {
        // Demo mode - simulate successful cancellation
        console.log('Demo mode: Simulating episode cancellation:', episodeId)
        return
      }

      await runTransaction(db!, async (transaction) => {
        // Get user episode
        const userEpisodeRef = doc(db, `users/${employeeNumber}/episodes`, episodeId)
        const userEpisodeDoc = await transaction.get(userEpisodeRef)

        if (!userEpisodeDoc.exists()) {
          throw new Error('Episode not found')
        }

        const episodeData = userEpisodeDoc.data()

        if (episodeData.status !== 'scheduled') {
          throw new Error('Can only cancel scheduled episodes')
        }

        // Update episode status
        transaction.update(userEpisodeRef, {
          status: 'cancelled',
          updated_at: new Date()
        })

        // Update individual items
        for (const item of episodeData.items) {
          const itemGroupRef = doc(db, 'item_groups', item.item_group_id)
          const itemGroupDoc = await transaction.get(itemGroupRef)

          if (itemGroupDoc.exists()) {
            const groupData = itemGroupDoc.data()
            const items = groupData.items || []

            const itemIndex = items.findIndex((i: any) => i.id === item.individual_item_id)
            if (itemIndex >= 0 && items[itemIndex].episodes && items[itemIndex].episodes[episodeId]) {
              items[itemIndex].episodes[episodeId].status = 'cancelled'
              transaction.update(itemGroupRef, { items, updated_at: new Date() })
            }
          }
        }

        // Update user's main episodes record
        const userRef = doc(db, 'users', employeeNumber)
        const userDoc = await transaction.get(userRef)

        if (userDoc.exists()) {
          const userData = userDoc.data()
          const episodes = userData.episodes || {}
          if (episodes[episodeId]) {
            episodes[episodeId].status = 'cancelled'
            transaction.update(userRef, { episodes })
          }
        }
      })
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to cancel episode'
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    userEpisodes: readonly(userEpisodes),
    cart: readonly(cart),
    selectedDates: readonly(selectedDates),
    loading: readonly(loading),
    error: readonly(error),

    // Getters
    episodesByStatus,
    cartTotal,
    cartItemGroups,

    // Actions
    initializeUserEpisodesListener,
    stopUserEpisodesListener,
    addToCart,
    removeFromCart,
    updateCartItemQuantity,
    clearCart,
    checkContraindications,
    setSelectedDates,
    submitCart,
    cancelEpisode,
    clearError
  }
})
