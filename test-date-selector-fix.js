// Test script to verify DateSelector fixes
console.log('🧪 Testing DateSelector Component Fixes...\n');

// Test 1: Check for Vue errors in console
console.log('1. Checking for Vue errors...');
setTimeout(() => {
  // Check if there are any Vue warnings or errors
  const hasVueErrors = window.console.error.toString().includes('Vue') || 
                      window.console.warn.toString().includes('Vue');
  
  console.log(hasVueErrors ? '❌ Vue errors detected' : '✅ No Vue errors detected');
}, 1000);

// Test 2: Check for DateSelector component presence
console.log('\n2. Testing DateSelector component...');
setTimeout(() => {
  const dateSelector = document.querySelector('.date-selector');
  console.log(dateSelector ? '✅ DateSelector component found' : '❌ DateSelector component not found');
  
  if (dateSelector) {
    const modeButtons = dateSelector.querySelectorAll('.mode-buttons button');
    console.log(`Found ${modeButtons.length} date mode buttons`);
    
    const dateButtons = dateSelector.querySelectorAll('.date-button');
    console.log(`Found ${dateButtons.length} date selection buttons`);
  }
}, 1500);

// Test 3: Check for date selection functionality
console.log('\n3. Testing date selection functionality...');
setTimeout(() => {
  const dateButtons = document.querySelectorAll('.date-button:not(.disabled)');
  if (dateButtons.length > 0) {
    console.log('✅ Date buttons are available for selection');
    
    // Test if buttons are clickable
    const firstButton = dateButtons[0];
    if (firstButton && !firstButton.disabled) {
      console.log('✅ Date buttons appear to be interactive');
    }
  } else {
    console.log('❌ No available date buttons found');
  }
}, 2000);

// Test 4: Check for proper button styling
console.log('\n4. Testing button styling...');
setTimeout(() => {
  const buttons = document.querySelectorAll('.date-selector button');
  let hasProperStyling = false;
  
  buttons.forEach(button => {
    const styles = window.getComputedStyle(button);
    if (styles.borderRadius && styles.fontWeight) {
      hasProperStyling = true;
    }
  });
  
  console.log(hasProperStyling ? '✅ Button styling applied' : '❌ Button styling issues');
  console.log(`Total buttons in DateSelector: ${buttons.length}`);
}, 2500);

// Test 5: Check for error-free component mounting
console.log('\n5. Testing component mounting...');
setTimeout(() => {
  const bookingView = document.querySelector('[class*="booking"]');
  const hasErrors = document.querySelector('.error, [class*="error"]');
  
  if (bookingView && !hasErrors) {
    console.log('✅ BookingView and DateSelector mounted without errors');
  } else {
    console.log('❌ Component mounting issues detected');
  }
}, 3000);

console.log('\n🔍 DateSelector test completed. Check the results above for any issues.');
