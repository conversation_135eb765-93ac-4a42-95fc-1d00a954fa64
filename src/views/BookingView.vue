<template>
  <div class="booking-container">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <i class="pi pi-plus-circle"></i>
            New Equipment Booking
          </h1>
          <p class="page-description">Select dates and equipment for your rental request</p>
        </div>
        <div class="header-actions">
          <Button
            icon="pi pi-refresh"
            label="Refresh"
            severity="secondary"
            outlined
            @click="refreshData"
            :loading="loading"
          />
        </div>
      </div>
    </div>

    <!-- Step 1: Equipment Type Selection -->
    <div class="step-section" :class="{ 'step-completed': selectedType }">
      <div class="section-header">
        <h2 class="section-title">
          <span class="step-number">1</span>
          <i class="pi pi-box"></i>
          Select Equipment Type
        </h2>
        <div class="section-info">
          <span class="info-text">Choose the type of equipment you need to rent</span>
        </div>
      </div>
      <div class="type-selection-content">
        <div class="type-cards">
          <div
            v-for="type in itemTabs"
            :key="type.value"
            class="type-card"
            :class="{ 'type-card-selected': selectedType === type.value }"
            @click="selectEquipmentType(type.value)"
          >
            <div class="type-card-icon">
              <i :class="getTypeIcon(type.value)"></i>
            </div>
            <h3 class="type-card-title">{{ type.label }}</h3>
            <p class="type-card-description">{{ getTypeDescription(type.value) }}</p>
            <div class="type-card-count">
              {{ getItemCountByType(type.value) }} items available
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 2: Calendar Section -->
    <div class="step-section" :class="{ 'step-disabled': !selectedType, 'step-completed': selectedDates.length > 0 }" v-if="selectedType">
      <div class="section-header">
        <h2 class="section-title">
          <span class="step-number">2</span>
          <i class="pi pi-calendar"></i>
          Select Rental Dates
        </h2>
        <div class="section-info">
          <span class="info-text">Choose single dates, multiple dates, or date ranges for {{ getSelectedTypeLabel() }}</span>
        </div>
      </div>
      <div class="calendar-content">
        <DateSelector
          v-model="selectedDates"
          @update:modelValue="onDatesChanged"
          :disabled="loading || !selectedType"
        />
      </div>
    </div>

    <!-- Step 3: Equipment Selection -->
    <div class="step-section" v-if="selectedType && selectedDates.length > 0">
      <!-- Available Equipment Section -->
      <div class="equipment-section">
        <div class="section-header">
          <div class="header-left">
            <h2 class="section-title">
              <span class="step-number">3</span>
              <i class="pi pi-shopping-cart"></i>
              Select Equipment Items
            </h2>
            <div class="section-stats">
              <div class="stat-item">
                <span class="stat-label">Available Items:</span>
                <span class="stat-value">{{ filteredItemGroupsWithAllFilters.length }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Selected Dates:</span>
                <span class="stat-value">{{ selectedDates.length }} day{{ selectedDates.length !== 1 ? 's' : '' }}</span>
              </div>
            </div>
          </div>
          <div class="header-actions">
            <div class="search-container">
              <InputText
                v-model="searchQuery"
                placeholder="Search equipment..."
                class="search-input"
              >
                <template #prefix>
                  <i class="pi pi-search"></i>
                </template>
              </InputText>
            </div>
            <div class="view-toggle">
              <Button
                icon="pi pi-th-large"
                :severity="viewMode === 'grid' ? 'primary' : 'secondary'"
                :outlined="viewMode !== 'grid'"
                @click="setViewMode('grid')"
                v-tooltip="'Grid View'"
              />
              <Button
                icon="pi pi-list"
                :severity="viewMode === 'list' ? 'primary' : 'secondary'"
                :outlined="viewMode !== 'list'"
                @click="setViewMode('list')"
                v-tooltip="'List View'"
              />
            </div>
          </div>
        </div>

        <!-- Filters Row -->
        <div class="filters-row">
          <div class="filter-group">
            <label class="filter-label">Category:</label>
            <Dropdown
              v-model="selectedCategory"
              :options="categoryFilterOptions"
              option-label="label"
              option-value="value"
              placeholder="All Categories"
              class="filter-dropdown"
              @change="onCategoryChange"
            />
          </div>
          <div class="filter-group">
            <label class="filter-label">Type:</label>
            <Dropdown
              v-model="selectedType"
              :options="itemTabs"
              option-label="label"
              option-value="value"
              placeholder="Suggested Package"
              class="filter-dropdown"
              @change="onTypeChange"
            />
          </div>
          <div class="filter-group">
            <label class="filter-label">Availability:</label>
            <Dropdown
              v-model="availabilityFilter"
              :options="availabilityOptions"
              option-label="label"
              option-value="value"
              placeholder="All Items"
              class="filter-dropdown"
              @change="onAvailabilityChange"
            />
          </div>
          <div class="filter-actions">
            <Button
              label="Clear Filters"
              icon="pi pi-filter-slash"
              severity="secondary"
              outlined
              @click="clearAllFilters"
              :disabled="!hasActiveFilters"
            />
          </div>
        </div>

        <!-- Equipment Display -->
        <div class="equipment-display">
          <div v-if="loading" class="loading-state">
            <div class="loading-content">
              <ProgressSpinner />
              <h4>Loading Equipment</h4>
              <p>Fetching available items for your selected dates...</p>
            </div>
          </div>

          <div v-else-if="filteredItemGroupsWithAllFilters.length === 0" class="empty-state">
            <div class="empty-content">
              <i class="pi pi-inbox"></i>
              <h4>No Equipment Available</h4>
              <p>No items match your current filters and selected dates.</p>
              <Button
                label="Clear All Filters"
                icon="pi pi-filter-slash"
                severity="secondary"
                outlined
                @click="clearAllFilters"
              />
            </div>
          </div>

          <div v-else>
            <!-- CPR Drill Package Special Display -->
            <div v-if="selectedType === 'suggested_package'" class="package-section">
              <div class="package-header">
                <div class="package-title">
                  <i class="pi pi-heart package-icon"></i>
                  <h3>CPR Drill Package</h3>
                  <Badge value="Suggested" severity="success" />
                </div>
                <p class="package-description">
                  Complete package for CPR training sessions including training manikin and venue
                </p>
              </div>

              <div class="package-items">
                <div class="package-grid">
                  <ItemCard
                    v-for="itemGroup in filteredItemGroupsWithAllFilters"
                    :key="itemGroup.id"
                    :item-group="itemGroup"
                    :available-quantity="getAvailableQuantity(itemGroup)"
                    :is-available="isItemGroupAvailable(itemGroup)"
                    @add-to-cart="handleAddToCart"
                    class="package-item-card"
                  />
                </div>

                <div v-if="filteredItemGroupsWithAllFilters.length === 0" class="package-empty">
                  <i class="pi pi-exclamation-triangle"></i>
                  <h4>Package Items Not Available</h4>
                  <p>Some items in the CPR Drill Package are not currently available. Please check individual categories.</p>
                </div>
              </div>
            </div>

            <!-- Regular Equipment Display -->
            <div v-else>
              <!-- Grid View -->
              <div v-if="viewMode === 'grid'" class="equipment-grid">
              <ItemCard
                v-for="itemGroup in filteredItemGroupsWithAllFilters"
                :key="itemGroup.id"
                :item-group="itemGroup"
                :available-quantity="getAvailableQuantity(itemGroup)"
                :is-available="isItemGroupAvailable(itemGroup)"
                @add-to-cart="handleAddToCart"
                class="equipment-card"
              />
            </div>

            <!-- List View -->
            <div v-else class="equipment-list">
              <div class="list-header">
                <div class="list-column">Equipment</div>
                <div class="list-column">Category</div>
                <div class="list-column">Available</div>
                <div class="list-column">Actions</div>
              </div>
              <div
                v-for="itemGroup in filteredItemGroupsWithAllFilters"
                :key="itemGroup.id"
                class="list-item"
                :class="{ 'list-item-unavailable': !isItemGroupAvailable(itemGroup) }"
              >
                <div class="list-column equipment-info">
                  <div class="equipment-name">{{ itemGroup.name }}</div>
                  <div class="equipment-description">{{ itemGroup.description }}</div>
                </div>
                <div class="list-column">
                  <Badge
                    :value="formatCategoryName(itemGroup.type)"
                    :severity="getCategorySeverity(itemGroup.type)"
                  />
                </div>
                <div class="list-column availability-info">
                  <div class="availability-count">
                    {{ getAvailableQuantity(itemGroup) }} / {{ itemGroup.items?.length || 0 }}
                  </div>
                  <div class="availability-label">available</div>
                </div>
                <div class="list-column">
                  <Button
                    label="Add to Cart"
                    icon="pi pi-plus"
                    size="small"
                    :disabled="!isItemGroupAvailable(itemGroup)"
                    @click="handleAddToCart(itemGroup, 1)"
                  />
                </div>
              </div>
            </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Cart Summary and Proceed Button -->
      <div class="cart-summary-section" v-if="cart.length > 0">
        <div class="cart-summary-content">
          <div class="cart-info">
            <div class="cart-icon">
              <i class="pi pi-shopping-cart"></i>
            </div>
            <div class="cart-details">
              <h3>{{ cart.length }} item group(s) selected</h3>
              <p>{{ totalCartQuantity }} total items for {{ selectedDates.length }} date(s)</p>
            </div>
          </div>
          <div class="cart-actions">
            <Button
              label="Clear Cart"
              icon="pi pi-trash"
              severity="danger"
              outlined
              @click="clearCart"
              :disabled="cart.length === 0"
              class="clear-btn"
            />
            <Button
              label="Proceed to Confirmation"
              icon="pi pi-arrow-right"
              @click="proceedToConfirmation"
              :disabled="cart.length === 0"
              class="proceed-btn"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State when no dates selected -->
    <div v-else class="empty-dates-state">
      <div class="empty-dates-content">
        <div class="empty-icon">
          <i class="pi pi-calendar"></i>
        </div>
        <h3>Select Rental Dates</h3>
        <p>Choose one or more dates from the calendar above to view available equipment and start your booking.</p>
        <div class="empty-features">
          <div class="feature-item">
            <i class="pi pi-check-circle"></i>
            <span>Single or multiple date selection</span>
          </div>
          <div class="feature-item">
            <i class="pi pi-check-circle"></i>
            <span>Real-time availability checking</span>
          </div>
          <div class="feature-item">
            <i class="pi pi-check-circle"></i>
            <span>Instant booking confirmation</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Contraindication Dialog -->
    <ContraIndicationDialog
      v-model:visible="showContraDialog"
      :conflicts="contraindictionConflicts"
      :item-name="pendingCartItem?.item_group_name || ''"
      @confirm="handleContraindictionConfirm"
      @cancel="handleContraindictionCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'primevue/usetoast'
import { useItemsStore } from '@/stores/items'
import { useEpisodesStore } from '@/stores/episodes'
import { useAuthStore } from '@/stores/auth'
import DateSelector from '@/components/booking/DateSelector.vue'
import ItemCard from '@/components/booking/ItemCard.vue'
import ContraIndicationDialog from '@/components/booking/ContraIndicationDialog.vue'
import type { CartItem } from '@/stores/episodes'

// Stores
const itemsStore = useItemsStore()
const episodesStore = useEpisodesStore()
const authStore = useAuthStore()
const router = useRouter()
const toast = useToast()

// Reactive state
const selectedDates = ref<string[]>([])
const loading = ref(false)
const submitting = ref(false)
const showContraDialog = ref(false)
const contraindictionConflicts = ref<Array<{item_group_name: string, reason: string}>>([])
const pendingCartItem = ref<CartItem | null>(null)

// New UI state
const searchQuery = ref('')
const viewMode = ref<'grid' | 'list'>('grid')
const availabilityFilter = ref('all')

// Item type tabs
const itemTabs = [
  { label: 'Suggested Package', value: 'suggested_package' as const },
  { label: 'Resuscitating Training', value: 'resus_trainings' as const },
  { label: 'Venue', value: 'venues' as const },
  { label: 'Audio Visuals', value: 'audio_visuals' as const }
]

// Filter options
const availabilityOptions = [
  { label: 'All Items', value: 'all' },
  { label: 'Available Only', value: 'available' },
  { label: 'Unavailable Only', value: 'unavailable' }
]

// Local state for selected type (to handle suggested_package)
const localSelectedType = ref<string>('')

// Computed properties with setters for dropdowns
const selectedType = computed({
  get: () => localSelectedType.value || itemsStore.selectedType,
  set: (value) => {
    localSelectedType.value = value
    if (value && value !== 'suggested_package') {
      itemsStore.setSelectedType(value)
    }
  }
})

const selectedCategory = computed({
  get: () => itemsStore.selectedCategory,
  set: (value) => itemsStore.setSelectedCategory(value)
})
const categoriesByType = computed(() => itemsStore.categoriesByType)
const filteredItemGroups = computed(() => itemsStore.filteredItemGroups)
const cart = computed(() => episodesStore.cart)

// New methods for step-based flow
const selectEquipmentType = (type: string) => {
  selectedType.value = type
  // Clear dates when changing type to force re-selection
  selectedDates.value = []
  itemsStore.setSelectedDates([])
}

const getSelectedTypeLabel = () => {
  const selectedTab = itemTabs.find(tab => tab.value === selectedType.value)
  return selectedTab ? selectedTab.label : 'equipment'
}

const getTypeIcon = (type: string) => {
  switch (type) {
    case 'resus_trainings':
      return 'pi pi-heart'
    case 'venues':
      return 'pi pi-building'
    case 'audio_visuals':
      return 'pi pi-video'
    default:
      return 'pi pi-box'
  }
}

const getTypeDescription = (type: string) => {
  switch (type) {
    case 'resus_trainings':
      return 'Training equipment for resuscitation and emergency response'
    case 'venues':
      return 'Training venues and meeting rooms'
    case 'audio_visuals':
      return 'Audio visual equipment for presentations and training'
    default:
      return 'All available equipment types'
  }
}

const getItemCountByType = (type: string) => {
  if (!type) return itemsStore.itemGroups.length
  return itemsStore.itemGroups.filter(item => item.type === type).length
}

// New computed properties
const categoryFilterOptions = computed(() => [
  { label: 'All Categories', value: '' },
  ...categoriesByType.value.map(cat => ({
    label: cat.name,
    value: cat.id
  }))
])

// CPR Drill Package definition
const cprDrillPackage = computed(() => {
  const allItems = itemsStore.itemGroups

  // Find Manikin from resuscitation training
  const manikin = allItems.find(item =>
    item.name.toLowerCase().includes('manikin') &&
    item.type === 'resus_trainings'
  )

  // Find RTRC from venues
  const rtrc = allItems.find(item =>
    item.name.toLowerCase().includes('rtrc') &&
    item.type === 'venues'
  )

  const packageItems = []
  if (manikin) packageItems.push(manikin)
  if (rtrc) packageItems.push(rtrc)

  return packageItems
})

// Computed for filtered items with all filters applied
const filteredItemGroupsWithAllFilters = computed(() => {
  // Handle suggested package
  if (selectedType.value === 'suggested_package') {
    return cprDrillPackage.value
  }

  let items = filteredItemGroups.value

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    items = items.filter(item =>
      item.name.toLowerCase().includes(query) ||
      item.description.toLowerCase().includes(query)
    )
  }

  // Apply availability filter
  if (availabilityFilter.value === 'available') {
    items = items.filter(item => isItemGroupAvailable(item))
  } else if (availabilityFilter.value === 'unavailable') {
    items = items.filter(item => !isItemGroupAvailable(item))
  }

  return items
})

const totalCartQuantity = computed(() => {
  return cart.value.reduce((total, item) => total + item.quantity, 0)
})

const hasActiveFilters = computed(() => {
  return searchQuery.value !== '' ||
         selectedCategory.value !== '' ||
         selectedType.value !== '' ||
         availabilityFilter.value !== 'all'
})

// Methods
const setSelectedType = (type: 'resus_trainings' | 'venues' | 'audio_visuals') => {
  itemsStore.setSelectedType(type)
}

const setSelectedCategory = (categoryId: string) => {
  itemsStore.setSelectedCategory(categoryId)
}

const getAvailableQuantity = (itemGroup: any) => {
  return itemsStore.getAvailableQuantity(itemGroup)
}

const isItemGroupAvailable = (itemGroup: any) => {
  return itemsStore.isItemGroupAvailable(itemGroup)
}

const getCategoryItemCount = (categoryId: string) => {
  return itemsStore.getItemCountByCategory(categoryId)
}

const clearFilters = () => {
  itemsStore.setSelectedCategory('')
  itemsStore.setSearchQuery('')
}

// New methods
const setViewMode = (mode: 'grid' | 'list') => {
  viewMode.value = mode
}

const onCategoryChange = () => {
  // Category change is handled by the v-model binding to selectedCategory
  itemsStore.setSelectedCategory(selectedCategory.value)
}

const onTypeChange = () => {
  // Type change is handled by the v-model binding to selectedType
  if (selectedType.value && selectedType.value !== 'suggested_package') {
    itemsStore.setSelectedType(selectedType.value)
  }
  // Clear category when type changes
  itemsStore.setSelectedCategory('')
}

const onAvailabilityChange = () => {
  // Availability filter change is handled by the v-model
  // This is a local filter, no store update needed
}

const clearAllFilters = () => {
  searchQuery.value = ''
  itemsStore.setSelectedCategory('')
  itemsStore.setSelectedType('')
  availabilityFilter.value = 'all'
  itemsStore.setSearchQuery('')
}

const clearCart = () => {
  episodesStore.clearCart()
  toast.add({
    severity: 'info',
    summary: 'Cart Cleared',
    detail: 'All items have been removed from your cart',
    life: 3000
  })
}

const proceedToConfirmation = () => {
  if (cart.value.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'No Items Selected',
      detail: 'Please select at least one item before proceeding',
      life: 3000
    })
    return
  }

  // Navigate to confirmation page
  router.push('/confirmation')
}

const formatCategoryName = (type: string) => {
  const category = categoriesByType.value.find(cat => cat.id === type)
  return category ? category.name : type
}

const getCategorySeverity = (type: string) => {
  switch (type) {
    case 'resus_trainings':
      return 'info'
    case 'venues':
      return 'success'
    case 'audio_visuals':
      return 'warning'
    default:
      return 'secondary'
  }
}

const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      itemsStore.fetchItemGroups(),
      itemsStore.fetchCategories()
    ])
    toast.add({
      severity: 'success',
      summary: 'Data Refreshed',
      detail: 'Equipment data has been updated',
      life: 3000
    })
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Refresh Failed',
      detail: 'Failed to refresh equipment data',
      life: 5000
    })
  } finally {
    loading.value = false
  }
}

const onDatesChanged = (dates: string[]) => {
  selectedDates.value = dates
  itemsStore.setSelectedDates(dates)
  episodesStore.setSelectedDates(dates)
}

const handleAddToCart = (cartItem: CartItem) => {
  const success = episodesStore.addToCart(cartItem)
  
  if (!success) {
    // Check contraindications
    const conflicts = episodesStore.checkContraindications(cartItem)
    if (conflicts.length > 0) {
      contraindictionConflicts.value = conflicts
      pendingCartItem.value = cartItem
      showContraDialog.value = true
      return
    }
  }
  
  toast.add({
    severity: 'success',
    summary: 'Added to Cart',
    detail: `${cartItem.item_group_name} added to cart`,
    life: 3000
  })
}

const handleContraindictionConfirm = () => {
  if (pendingCartItem.value) {
    // Force add to cart despite contraindications
    const existingIndex = cart.value.findIndex(
      item => item.item_group_id === pendingCartItem.value!.item_group_id
    )
    
    if (existingIndex >= 0) {
      episodesStore.updateCartItemQuantity(
        pendingCartItem.value.item_group_id,
        cart.value[existingIndex].quantity + pendingCartItem.value.quantity
      )
    } else {
      episodesStore.addToCart(pendingCartItem.value)
    }
    
    toast.add({
      severity: 'warn',
      summary: 'Added with Warning',
      detail: `${pendingCartItem.value.item_group_name} added despite contraindications`,
      life: 5000
    })
  }
  
  showContraDialog.value = false
  pendingCartItem.value = null
  contraindictionConflicts.value = []
}

const handleContraindictionCancel = () => {
  showContraDialog.value = false
  pendingCartItem.value = null
  contraindictionConflicts.value = []
}





// Lifecycle
onMounted(async () => {
  loading.value = true
  
  try {
    // Initialize real-time listeners
    itemsStore.initializeRealTimeListeners()
    
    if (authStore.currentUserProfile?.employee_number) {
      episodesStore.initializeUserEpisodesListener(authStore.currentUserProfile.employee_number)
    }
    
    // Fetch initial data
    await Promise.all([
      itemsStore.fetchItemGroups(),
      itemsStore.fetchCategories()
    ])
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Loading Error',
      detail: 'Failed to load booking data',
      life: 5000
    })
  } finally {
    loading.value = false
  }
})

onUnmounted(() => {
  itemsStore.stopRealTimeListeners()
  episodesStore.stopUserEpisodesListener()
})
</script>

<style scoped>
.booking-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0;
}

/* Page Header */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.header-content {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info {
  flex: 1;
}

.page-title {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-title i {
  font-size: 1.75rem;
}

.page-description {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

/* Step-based Flow Styles */
.step-section {
  margin-bottom: 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  border-left: 4px solid #e0e0e0;
}

.step-section.step-completed {
  border-left-color: #4caf50;
  background: linear-gradient(135deg, #f8fff8 0%, #ffffff 100%);
}

.step-section.step-disabled {
  opacity: 0.6;
  pointer-events: none;
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: #667eea;
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.9rem;
  margin-right: 0.75rem;
}

.step-completed .step-number {
  background: #4caf50;
}

/* Type Selection Cards */
.type-selection-content {
  padding: 2rem;
}

.type-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.type-card {
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.type-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.type-card-selected {
  border-color: #667eea;
  background: linear-gradient(135deg, #f0f4ff 0%, #ffffff 100%);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.type-card-icon {
  font-size: 3rem;
  color: #667eea;
  margin-bottom: 1rem;
}

.type-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.type-card-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
}

.type-card-count {
  background: #f8f9fa;
  color: #667eea;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  display: inline-block;
}

.type-card-selected .type-card-count {
  background: #667eea;
  color: white;
}

.section-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-title i {
  color: #667eea;
}

.section-info {
  display: flex;
  align-items: center;
}

.info-text {
  font-size: 0.9rem;
  color: #6c757d;
  font-style: italic;
}

.calendar-content {
  padding: 2rem;
}

/* Main Content */
.main-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  min-height: 600px;
}

/* Items Region */
.items-region {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.items-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.items-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.items-title i {
  color: #667eea;
}

.items-count {
  font-size: 0.85rem;
  color: #6c757d;
  font-weight: 500;
}

.item-type-tabs {
  display: flex;
  gap: 0.75rem;
}

.tab-button {
  border-radius: 25px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.items-content {
  display: flex;
  flex: 1;
  min-height: 0;
}

/* Category Sidebar */
.category-sidebar {
  width: 280px;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
}

.category-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  background: white;
}

.category-header h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.category-header i {
  color: #667eea;
}

.category-count {
  font-size: 0.8rem;
  color: #6c757d;
}

.category-list {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  overflow-y: auto;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  border: 1px solid #e9ecef;
}

.category-item:hover {
  background: #f8f9fa;
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

.category-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border-color: transparent !important;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

.category-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.category-active .category-icon {
  background: rgba(255, 255, 255, 0.2);
}

.category-label {
  flex: 1;
  font-weight: 500;
  font-size: 0.9rem;
}

.category-badge {
  background: #e9ecef;
  color: #495057;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
}

.category-active .category-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Items Display */
.items-display {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: white;
}

.loading-state,
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
}

.loading-content,
.empty-content {
  text-align: center;
  max-width: 400px;
}

.loading-content h4,
.empty-content h4 {
  margin: 1rem 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.25rem;
}

.loading-content p,
.empty-content p {
  margin: 0 0 1.5rem 0;
  color: #6c757d;
  line-height: 1.5;
}

.empty-content i {
  font-size: 4rem;
  color: #dee2e6;
  margin-bottom: 1rem;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

/* Cart Region */
.cart-region {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.cart-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cart-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.cart-title i {
  color: #667eea;
}

.cart-summary {
  display: flex;
  align-items: center;
}

.cart-count {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Empty Dates State */
.empty-dates-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 500px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.empty-dates-content {
  text-align: center;
  max-width: 500px;
  padding: 2rem;
}

.empty-icon {
  width: 120px;
  height: 120px;
  margin: 0 auto 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 3rem;
}

.empty-dates-content h3 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  color: #2c3e50;
  font-weight: 600;
}

.empty-dates-content p {
  margin: 0 0 2rem 0;
  color: #6c757d;
  font-size: 1.1rem;
  line-height: 1.6;
}

.empty-features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.feature-item i {
  color: #28a745;
  font-size: 1.1rem;
}

.feature-item span {
  color: #495057;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1.5fr 1fr;
    gap: 1.5rem;
  }

  .category-sidebar {
    width: 240px;
  }
}

@media (max-width: 1024px) {
  .header-content {
    padding: 0 1.5rem;
  }

  .calendar-content,
  .items-display {
    padding: 1.5rem;
  }

  .section-header,
  .items-header,
  .cart-header {
    padding: 1.25rem 1.5rem;
  }
}

/* New Enhanced Layout Styles */

/* Main Content - Updated for vertical layout */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-top: 2rem;
}

/* Equipment Section Styles */
.equipment-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.section-stats {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.85rem;
  opacity: 0.9;
}

.stat-value {
  font-size: 1.1rem;
  font-weight: 600;
}

.search-container {
  position: relative;
}

.search-input {
  min-width: 250px;
}

.view-toggle {
  display: flex;
  gap: 0.5rem;
}

/* Filters Row */
.filters-row {
  background: #f8f9fa;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  gap: 2rem;
  align-items: center;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 150px;
}

.filter-label {
  font-size: 0.85rem;
  font-weight: 600;
  color: #495057;
  margin: 0;
}

.filter-dropdown {
  min-width: 150px;
}

.filter-actions {
  margin-left: auto;
}

/* Equipment Display */
.equipment-display {
  padding: 2rem;
  min-height: 400px;
}

/* Equipment Grid - Enhanced */
.equipment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

.equipment-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.equipment-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Equipment List View */
.equipment-list {
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.list-header {
  background: #f8f9fa;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 1rem;
  padding: 1rem 1.5rem;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #e9ecef;
}

.list-item {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 1rem;
  padding: 1.5rem;
  border-bottom: 1px solid #f8f9fa;
  transition: background-color 0.2s ease;
}

.list-item:hover {
  background: #f8f9fa;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-unavailable {
  opacity: 0.6;
}

.list-column {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.equipment-info {
  gap: 0.5rem;
}

.equipment-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.equipment-description {
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.4;
}

.availability-info {
  text-align: center;
  gap: 0.25rem;
}

.availability-count {
  font-weight: 600;
  font-size: 1.1rem;
  color: #2c3e50;
}

.availability-label {
  font-size: 0.85rem;
  color: #6c757d;
}

/* Cart Summary Section */
.cart-summary-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-top: 2rem;
}

.cart-summary-content {
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.cart-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.cart-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.cart-details h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
}

.cart-details p {
  margin: 0;
  color: #6c757d;
  font-size: 0.95rem;
}

.cart-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.clear-btn {
  min-width: 120px;
}

.proceed-btn {
  min-width: 180px;
  font-weight: 600;
}

@media (max-width: 768px) {
  .main-content {
    gap: 1rem;
  }

  .filters-row {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .filter-group {
    min-width: unset;
  }

  .filter-actions {
    margin-left: 0;
  }

  .equipment-grid {
    grid-template-columns: 1fr;
  }

  .list-header,
  .list-item {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .list-header {
    display: none;
  }

  .list-item {
    padding: 1rem;
  }

  .list-column {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
  }

  .list-column:last-child {
    border-bottom: none;
  }

  .search-input {
    min-width: unset;
    width: 100%;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .section-stats {
    gap: 1rem;
  }

  .cart-summary-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
  }

  .cart-info {
    justify-content: center;
    text-align: center;
  }

  .cart-actions {
    justify-content: center;
  }
}

/* Package Section Styles */
.package-section {
  background: var(--surface-card);
  border-radius: 12px;
  padding: 2rem;
  border: 2px solid var(--primary-color);
  margin-bottom: 2rem;
}

.package-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid var(--surface-border);
}

.package-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.package-icon {
  font-size: 2rem;
  color: var(--primary-color);
}

.package-title h3 {
  margin: 0;
  color: var(--text-color);
  font-size: 2rem;
  font-weight: 600;
}

.package-description {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

.package-items {
  margin-top: 1.5rem;
}

.package-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.package-item-card {
  border: 2px solid var(--primary-color-text);
  border-radius: 8px;
  background: var(--surface-ground);
}

.package-empty {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--text-color-secondary);
}

.package-empty i {
  font-size: 3rem;
  color: var(--orange-500);
  margin-bottom: 1rem;
}

.package-empty h4 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 1.5rem;
}

.package-empty p {
  margin: 0;
  font-size: 1rem;
}

@media (max-width: 768px) {
  .package-section {
    padding: 1.5rem;
  }

  .package-title {
    flex-direction: column;
    gap: 0.5rem;
  }

  .package-title h3 {
    font-size: 1.5rem;
  }

  .package-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
</style>
