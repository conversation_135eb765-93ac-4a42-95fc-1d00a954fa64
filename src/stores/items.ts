import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  type Unsubscribe
} from 'firebase/firestore'
import { db } from '@/firebase/config'

// Individual item within an item group
export interface IndividualItem {
  id: string
  remarks?: string
  episodes: Record<string, {
    dates: string[] // Array of dates in YYYY-MM-DD format
    status: 'scheduled' | 'rented' | 'returned' | 'cancelled'
  }>
  use_count: number
  suspended: boolean
}

// Item group (what users see and select)
export interface ItemGroup {
  id: string
  name: string
  description: string
  contraindications: Array<{
    item_group_name: string
    reason: string
  }>
  items: IndividualItem[] // Array of individual items of the same model
  imageUrl?: string
  category: string
  type: 'resus_trainings' | 'venues' | 'audio_visuals'
  createdAt: Date
  updatedAt: Date
  isActive: boolean
}

// Category within each type
export interface ItemCategory {
  id: string
  name: string
  description: string
  type: 'resus_trainings' | 'venues' | 'audio_visuals'
  icon?: string
  color?: string
}

export const useItemsStore = defineStore('items', () => {
  // State
  const itemGroups = ref<ItemGroup[]>([])
  const categories = ref<ItemCategory[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const selectedType = ref<'resus_trainings' | 'venues' | 'audio_visuals'>('resus_trainings')
  const selectedCategory = ref<string>('')
  const selectedDates = ref<string[]>([]) // Array of selected dates in YYYY-MM-DD format
  const searchQuery = ref('')

  // Real-time listeners
  let itemGroupsUnsubscribe: Unsubscribe | null = null
  let categoriesUnsubscribe: Unsubscribe | null = null

  // Getters
  const itemGroupsByType = computed(() => {
    return itemGroups.value.filter(group =>
      group.isActive && group.type === selectedType.value
    )
  })

  const categoriesByType = computed(() => {
    return categories.value.filter(category => category.type === selectedType.value)
  })

  const filteredItemGroups = computed(() => {
    let filtered = itemGroupsByType.value

    if (selectedCategory.value) {
      filtered = filtered.filter(group => group.category === selectedCategory.value)
    }

    return filtered
  })

  const itemGroupsByCategory = computed(() => {
    const grouped: Record<string, ItemGroup[]> = {}
    filteredItemGroups.value.forEach(group => {
      if (!grouped[group.category]) {
        grouped[group.category] = []
      }
      grouped[group.category].push(group)
    })
    return grouped
  })

  // Get available quantity for an item group on selected dates
  const getAvailableQuantity = (itemGroup: ItemGroup): number => {
    if (selectedDates.value.length === 0) {
      return itemGroup.items.length // Return total items if no dates selected
    }

    let availableCount = 0

    itemGroup.items.forEach(item => {
      if (item.suspended) return // Skip suspended items

      let isAvailable = true

      // Check if any selected date conflicts with item's episodes
      for (const episodeId in item.episodes) {
        const episode = item.episodes[episodeId]
        if (episode.status === 'scheduled' || episode.status === 'rented') {
          // Check if any selected date overlaps with episode dates
          const hasConflict = selectedDates.value.some(selectedDate =>
            episode.dates.includes(selectedDate)
          )
          if (hasConflict) {
            isAvailable = false
            break
          }
        }
      }

      if (isAvailable) {
        availableCount++
      }
    })

    return availableCount
  }

  // Check if item group is available (has at least one available item)
  const isItemGroupAvailable = (itemGroup: ItemGroup): boolean => {
    return getAvailableQuantity(itemGroup) > 0
  }

  // Actions
  const initializeRealTimeListeners = () => {
    // Listen to item groups from the three main collections
    const collections = ['resus_trainings', 'venues', 'audio_visuals']

    collections.forEach(collectionName => {
      const itemGroupsQuery = query(
        collection(db, `items/${collectionName}/groups`),
        orderBy('name')
      )

      const unsubscribe = onSnapshot(itemGroupsQuery, (snapshot) => {
        const newGroups = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          type: collectionName as 'resus_trainings' | 'venues' | 'audio_visuals',
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date()
        })) as ItemGroup[]

        // Update the itemGroups array by replacing groups of this type
        itemGroups.value = [
          ...itemGroups.value.filter(group => group.type !== collectionName),
          ...newGroups
        ]
      }, (err) => {
        console.error(`Error listening to ${collectionName}:`, err)
        error.value = `Failed to load ${collectionName}`
      })

      // Store unsubscribe function (simplified for now)
      if (!itemGroupsUnsubscribe) {
        itemGroupsUnsubscribe = unsubscribe
      }
    })

    // Listen to categories collection
    const categoriesQuery = query(
      collection(db, 'categories'),
      orderBy('name')
    )

    categoriesUnsubscribe = onSnapshot(categoriesQuery, (snapshot) => {
      categories.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ItemCategory[]
    }, (err) => {
      console.error('Error listening to categories:', err)
    })
  }

  const stopRealTimeListeners = () => {
    if (itemGroupsUnsubscribe) {
      itemGroupsUnsubscribe()
      itemGroupsUnsubscribe = null
    }
    if (categoriesUnsubscribe) {
      categoriesUnsubscribe()
      categoriesUnsubscribe = null
    }
  }

  const fetchItemGroups = async () => {
    loading.value = true
    error.value = null

    try {
      const collections = ['resus_trainings', 'venues', 'audio_visuals']
      const allGroups: ItemGroup[] = []

      for (const collectionName of collections) {
        const q = query(
          collection(db, `items/${collectionName}/groups`),
          where('isActive', '==', true),
          orderBy('name')
        )

        const snapshot = await getDocs(q)
        const groups = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          type: collectionName as 'resus_trainings' | 'venues' | 'audio_visuals',
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date()
        })) as ItemGroup[]

        allGroups.push(...groups)
      }

      itemGroups.value = allGroups
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch item groups'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchItemGroupById = async (type: string, id: string): Promise<ItemGroup | null> => {
    try {
      const docRef = doc(db, `items/${type}/groups`, id)
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        const data = docSnap.data()
        return {
          id: docSnap.id,
          ...data,
          type: type as 'resus_trainings' | 'venues' | 'audio_visuals',
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date()
        } as ItemGroup
      }
      return null
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch item group'
      throw err
    }
  }

  const fetchCategories = async () => {
    try {
      const snapshot = await getDocs(collection(db, 'categories'))
      categories.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ItemCategory[]
    } catch (err) {
      console.error('Error fetching categories:', err)
    }
  }

  const setSelectedType = (type: 'resus_trainings' | 'venues' | 'audio_visuals') => {
    selectedType.value = type
    selectedCategory.value = '' // Reset category when type changes
  }

  const setSelectedCategory = (categoryId: string) => {
    selectedCategory.value = categoryId
  }

  const setSelectedDates = (dates: string[]) => {
    selectedDates.value = dates
  }

  const clearFilters = () => {
    selectedCategory.value = ''
    selectedDates.value = []
  }



  const clearError = () => {
    error.value = null
  }

  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }

  // Computed properties
  const availableItems = computed(() => {
    let filtered = itemGroups.value

    // Filter by search query
    if (searchQuery.value.trim()) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(query) ||
        item.description.toLowerCase().includes(query)
      )
    }

    // Filter by selected type
    if (selectedType.value) {
      filtered = filtered.filter(item => item.type === selectedType.value)
    }

    // Filter by selected category
    if (selectedCategory.value) {
      filtered = filtered.filter(item => item.category === selectedCategory.value)
    }

    return filtered
  })

  return {
    // State
    itemGroups: readonly(itemGroups),
    categories: readonly(categories),
    loading: readonly(loading),
    error: readonly(error),
    selectedType: readonly(selectedType),
    selectedCategory: readonly(selectedCategory),
    selectedDates: readonly(selectedDates),
    searchQuery: readonly(searchQuery),

    // Getters
    itemGroupsByType,
    categoriesByType,
    filteredItemGroups,
    itemGroupsByCategory,
    availableItems,
    getAvailableQuantity,
    isItemGroupAvailable,

    // Actions
    initializeRealTimeListeners,
    stopRealTimeListeners,
    fetchItemGroups,
    fetchItemGroupById,
    fetchCategories,
    setSelectedType,
    setSelectedCategory,
    setSelectedDates,
    setSearchQuery,
    clearFilters,
    clearError
  }
})
