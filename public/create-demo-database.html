<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Demo Database - TKOH NSD Renting Platform</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #5a6fd8;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .database-structure {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .category {
            margin: 15px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #667eea;
        }
        .item-group {
            margin: 8px 0 8px 20px;
            padding: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            font-size: 14px;
        }
        .item {
            margin: 4px 0 4px 20px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ Create Demo Database</h1>
        
        <div class="status info">
            <strong>📋 Database Structure Preview</strong><br>
            This will create the complete database structure for the TKOH NSD Renting Platform.
        </div>

        <div class="database-structure">
            <h3>📁 Categories & Items to be Created:</h3>
            
            <div class="category">
                <strong>🫀 Resuscitation Training Equipment</strong>
                <div class="item-group">📦 Manikin
                    <div class="item">• MAN-001: Adult CPR training manikin (Excellent)</div>
                    <div class="item">• MAN-002: Adult CPR training manikin (Good)</div>
                </div>
                <div class="item-group">📦 Sim Train Equipment
                    <div class="item">• SIM-001: Complete simulation training kit (Excellent)</div>
                </div>
            </div>

            <div class="category">
                <strong>🏢 Training Venue</strong>
                <div class="item-group">📦 RTRC
                    <div class="item">• VENUE-001: Main training room with full equipment (Excellent)</div>
                </div>
            </div>

            <div class="category">
                <strong>📺 Audio Visual Equipment</strong>
                <div class="item-group">📦 SMV TV
                    <div class="item">• TV-001: 55-inch Smart TV with wireless connectivity (Excellent)</div>
                </div>
                <div class="item-group">📦 Mobile Phone Stabilizer
                    <div class="item">• STAB-001: 3-axis gimbal stabilizer (Excellent)</div>
                    <div class="item">• STAB-002: 3-axis gimbal stabilizer (Good)</div>
                </div>
                <div class="item-group">📦 Digital Camera
                    <div class="item">• CAM-001: DSLR camera with lens kit (Excellent)</div>
                </div>
            </div>
        </div>

        <div style="text-align: center;">
            <button class="button" onclick="createDemoDatabase()" id="createBtn">
                🚀 Create Database Structure
            </button>
            <button class="button" onclick="window.location.href='/admin'" style="background: #28a745;">
                ⚙️ Go to Admin Panel
            </button>
        </div>

        <div id="status"></div>

        <div class="status info" style="margin-top: 30px;">
            <strong>ℹ️ Instructions:</strong><br>
            1. Click "Create Database Structure" to initialize the database<br>
            2. Go to Admin Panel to manage items<br>
            3. Use the booking system to rent equipment<br><br>
            <strong>Note:</strong> This creates a demo database structure. For production use, configure Firebase in your .env file.
        </div>
    </div>

    <script>
        async function createDemoDatabase() {
            const btn = document.getElementById('createBtn');
            const status = document.getElementById('status');
            
            btn.disabled = true;
            btn.textContent = '⏳ Creating Database...';
            
            status.innerHTML = '<div class="status info">🔄 Initializing database structure...</div>';

            try {
                // Simulate database creation (in demo mode, this would be stored locally)
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Create the database structure in localStorage for demo
                const databaseStructure = {
                    categories: [
                        {
                            id: 'resuscitation-training',
                            name: 'Resuscitation Training Equipment',
                            type: 'resus_trainings',
                            description: 'Equipment for resuscitation and emergency response training',
                            icon: 'pi pi-heart',
                            order: 1
                        },
                        {
                            id: 'training-venue',
                            name: 'Training Venue',
                            type: 'venues',
                            description: 'Training venues and meeting rooms',
                            icon: 'pi pi-building',
                            order: 2
                        },
                        {
                            id: 'audio-visual',
                            name: 'Audio Visual Equipment',
                            type: 'audio_visuals',
                            description: 'Audio visual equipment for presentations and training',
                            icon: 'pi pi-video',
                            order: 3
                        }
                    ],
                    itemGroups: [
                        {
                            id: 'manikin',
                            name: 'Manikin',
                            category_id: 'resuscitation-training',
                            type: 'resus_trainings',
                            description: 'Training manikin for CPR and resuscitation practice',
                            items: [
                                { id: 'manikin-001', serial_number: 'MAN-001', condition: 'excellent', notes: 'Adult CPR training manikin', status: 'available' },
                                { id: 'manikin-002', serial_number: 'MAN-002', condition: 'good', notes: 'Adult CPR training manikin', status: 'available' }
                            ]
                        },
                        {
                            id: 'sim-train-equipment',
                            name: 'Sim Train Equipment',
                            category_id: 'resuscitation-training',
                            type: 'resus_trainings',
                            description: 'Simulation training equipment for emergency scenarios',
                            items: [
                                { id: 'sim-001', serial_number: 'SIM-001', condition: 'excellent', notes: 'Complete simulation training kit', status: 'available' }
                            ]
                        },
                        {
                            id: 'rtrc',
                            name: 'RTRC',
                            category_id: 'training-venue',
                            type: 'venues',
                            description: 'Resuscitation Training Resource Centre - Main training venue',
                            items: [
                                { id: 'rtrc-001', serial_number: 'VENUE-001', condition: 'excellent', notes: 'Main training room with full equipment', status: 'available' }
                            ]
                        },
                        {
                            id: 'smv-tv',
                            name: 'SMV TV',
                            category_id: 'audio-visual',
                            type: 'audio_visuals',
                            description: 'Smart TV for presentations and training videos',
                            items: [
                                { id: 'tv-001', serial_number: 'TV-001', condition: 'excellent', notes: '55-inch Smart TV with wireless connectivity', status: 'available' }
                            ]
                        },
                        {
                            id: 'mobile-phone-stabilizer',
                            name: 'Mobile Phone Stabilizer',
                            category_id: 'audio-visual',
                            type: 'audio_visuals',
                            description: 'Gimbal stabilizer for mobile phone video recording',
                            items: [
                                { id: 'stabilizer-001', serial_number: 'STAB-001', condition: 'excellent', notes: '3-axis gimbal stabilizer', status: 'available' },
                                { id: 'stabilizer-002', serial_number: 'STAB-002', condition: 'good', notes: '3-axis gimbal stabilizer', status: 'available' }
                            ]
                        },
                        {
                            id: 'digital-camera',
                            name: 'Digital Camera',
                            category_id: 'audio-visual',
                            type: 'audio_visuals',
                            description: 'Professional digital camera for training documentation',
                            items: [
                                { id: 'camera-001', serial_number: 'CAM-001', condition: 'excellent', notes: 'DSLR camera with lens kit', status: 'available' }
                            ]
                        }
                    ]
                };

                // Store in localStorage for demo mode
                localStorage.setItem('demo_categories', JSON.stringify(databaseStructure.categories));
                localStorage.setItem('demo_item_groups', JSON.stringify(databaseStructure.itemGroups));

                status.innerHTML = `
                    <div class="status success">
                        ✅ <strong>Database Created Successfully!</strong><br><br>
                        📁 Created 3 categories<br>
                        📦 Created 6 item groups<br>
                        🔧 Created 9 individual items<br><br>
                        <strong>Ready to use!</strong> Go to the Admin Panel to manage items or start booking equipment.
                    </div>
                `;

                btn.textContent = '✅ Database Created';
                btn.style.background = '#28a745';

            } catch (error) {
                status.innerHTML = `
                    <div class="status error">
                        ❌ <strong>Error creating database:</strong><br>
                        ${error.message}
                    </div>
                `;
                btn.disabled = false;
                btn.textContent = '🚀 Create Database Structure';
            }
        }
    </script>
</body>
</html>
