@import './base.css';

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  font-weight: normal;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }

  #app {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0 2rem;
  }
}

/* ULTRA AGGRESSIVE PrimeVue Component Fixes - Force White Backgrounds */

/* All Dialog Components */
.p-dialog,
.p-dialog-content,
.p-dialog-header,
.p-dialog-footer,
.p-component,
.p-element {
  background: white !important;
  background-color: white !important;
}

/* All Overlay Components */
.p-dropdown-panel,
.p-multiselect-panel,
.p-calendar-panel,
.p-overlay-panel,
.p-menu,
.p-contextmenu,
.p-tooltip,
.p-autocomplete-panel,
.p-overlaypanel,
.p-sidebar,
.p-confirmdialog,
.p-toast,
.p-message {
  background: white !important;
  background-color: white !important;
  border: 1px solid #e0e0e0 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border-radius: 6px !important;
}

/* All List Items */
.p-dropdown-items,
.p-multiselect-items,
.p-calendar-panel .p-datepicker,
.p-autocomplete-items,
.p-menu-list,
.p-menubar-root-list,
.p-contextmenu-root-list {
  background: white !important;
  background-color: white !important;
}

/* Individual Items */
.p-dropdown-item,
.p-multiselect-item,
.p-calendar-panel .p-datepicker-calendar td,
.p-autocomplete-item,
.p-menuitem-link,
.p-menuitem-content {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

/* Hover States */
.p-dropdown-item:hover,
.p-multiselect-item:hover,
.p-calendar-panel .p-datepicker-calendar td:hover,
.p-autocomplete-item:hover,
.p-menuitem-link:hover {
  background: #f8f9fa !important;
  background-color: #f8f9fa !important;
  color: #333 !important;
}

/* Selected/Highlighted States */
.p-dropdown-item.p-highlight,
.p-multiselect-item.p-highlight,
.p-calendar-panel .p-datepicker-calendar td.p-highlight,
.p-autocomplete-item.p-highlight,
.p-menuitem-link.p-focus {
  background: #667eea !important;
  background-color: #667eea !important;
  color: white !important;
}

/* Calendar Specific */
.p-datepicker,
.p-datepicker-header,
.p-datepicker-calendar,
.p-datepicker-calendar thead,
.p-datepicker-calendar tbody {
  background: white !important;
  background-color: white !important;
}

/* Input Fields */
.p-inputtext,
.p-dropdown,
.p-multiselect,
.p-calendar .p-inputtext {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

/* Universal Override - Nuclear Option */
[class*="p-"]:not(.p-button):not(.p-badge):not(.p-progressbar):not(.p-slider) {
  background-color: inherit;
}

.p-component:not(.p-button):not(.p-badge):not(.p-progressbar):not(.p-slider) {
  background: white !important;
}

/* Force all overlays to be visible */
.p-component-overlay,
.p-dialog-mask,
.p-overlaypanel,
.p-tooltip {
  z-index: 9999 !important;
}

/* Ensure dialog masks don't interfere */
.p-dialog-mask {
  background: rgba(0, 0, 0, 0.5) !important;
}

/* Additional safety net for any missed components */
div[data-pc-section="panel"],
div[data-pc-section="content"],
div[data-pc-section="list"],
div[data-pc-section="item"] {
  background: white !important;
  background-color: white !important;
}
