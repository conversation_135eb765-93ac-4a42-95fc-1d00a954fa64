import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

// PrimeVue imports
import PrimeVue from 'primevue/config'
import 'primeicons/primeicons.css'

// Import our aggressive PrimeVue overrides AFTER PrimeVue CSS
import './assets/primevue-overrides.css'

// PrimeVue components
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Calendar from 'primevue/calendar'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Card from 'primevue/card'
import Dialog from 'primevue/dialog'
import Toast from 'primevue/toast'
import ToastService from 'primevue/toastservice'
import ConfirmDialog from 'primevue/confirmdialog'
import ConfirmationService from 'primevue/confirmationservice'
import Dropdown from 'primevue/dropdown'
import InputNumber from 'primevue/inputnumber'
import Toolbar from 'primevue/toolbar'
import Badge from 'primevue/badge'
import Tag from 'primevue/tag'
import ProgressSpinner from 'primevue/progressspinner'
import Tooltip from 'primevue/tooltip'

import App from './App.vue'
import router from './router'

const app = createApp(App)

// Configure PrimeVue
app.use(PrimeVue)

// Register PrimeVue services
app.use(ToastService)
app.use(ConfirmationService)

// Register PrimeVue components globally
app.component('Button', Button)
app.component('InputText', InputText)
app.component('Calendar', Calendar)
app.component('DataTable', DataTable)
app.component('Column', Column)
app.component('Card', Card)
app.component('Dialog', Dialog)
app.component('Toast', Toast)
app.component('ConfirmDialog', ConfirmDialog)
app.component('Dropdown', Dropdown)
app.component('InputNumber', InputNumber)
app.component('Toolbar', Toolbar)
app.component('Badge', Badge)
app.component('Tag', Tag)
app.component('ProgressSpinner', ProgressSpinner)

// Register PrimeVue directives
app.directive('tooltip', Tooltip)

app.use(createPinia())
app.use(router)

app.mount('#app')
