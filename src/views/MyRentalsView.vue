<template>
  <div class="my-rentals-container">
    <div class="page-header">
      <h1>My Rentals</h1>
      <p class="page-description">
        View and manage your rental episodes. Episodes are automatically updated based on rental dates.
      </p>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <ProgressSpinner />
      <p>Loading your rentals...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <i class="pi pi-exclamation-triangle" style="font-size: 3rem; color: var(--red-500);"></i>
      <h3>Error Loading Rentals</h3>
      <p>{{ error }}</p>
      <Button label="Retry" @click="loadRentals" />
    </div>

    <!-- Main Content -->
    <div v-else class="rentals-content">
      <!-- Status Tabs -->
      <div class="status-tabs">
        <Button
          v-for="status in statusTabs"
          :key="status.value"
          :label="`${status.label} (${getEpisodeCountByStatus(status.value)})`"
          :class="{ 'p-button-outlined': selectedStatus !== status.value }"
          @click="setSelectedStatus(status.value)"
          :severity="status.severity"
        />
      </div>

      <!-- Episodes Display -->
      <div class="episodes-section">
        <div v-if="filteredEpisodes.length === 0" class="empty-state">
          <i class="pi pi-calendar-times" style="font-size: 3rem; color: var(--text-color-secondary);"></i>
          <h3>No {{ selectedStatus.toLowerCase() }} rentals</h3>
          <p>You don't have any {{ selectedStatus.toLowerCase() }} rental episodes.</p>
          <Button
            v-if="selectedStatus === 'scheduled'"
            label="Create New Booking"
            @click="$router.push('/booking')"
          />
        </div>

        <div v-else class="episodes-grid">
          <EpisodeCard
            v-for="episode in filteredEpisodes"
            :key="episode.id"
            :episode="episode"
            @cancel-episode="handleCancelEpisode"
            @view-details="handleViewDetails"
          />
        </div>
      </div>
    </div>

    <!-- Episode Details Dialog -->
    <Dialog
      v-model:visible="showDetailsDialog"
      :header="`Episode Details - ${selectedEpisode?.id}`"
      modal
      style="width: 70vw; min-width: 600px;"
      class="episode-details-dialog"
    >
      <EpisodeDetails
        v-if="selectedEpisode"
        :episode="selectedEpisode"
        @cancel-episode="handleCancelEpisode"
        @close="showDetailsDialog = false"
      />
    </Dialog>

    <Toast />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import { useConfirm } from 'primevue/useconfirm'
import { useEpisodesStore } from '@/stores/episodes'
import { useAuthStore } from '@/stores/auth'
import EpisodeCard from '@/components/rentals/EpisodeCard.vue'
import EpisodeDetails from '@/components/rentals/EpisodeDetails.vue'
import type { UserEpisode } from '@/stores/episodes'

// Stores
const episodesStore = useEpisodesStore()
const authStore = useAuthStore()
const toast = useToast()
const confirm = useConfirm()

// Reactive state
const loading = ref(false)
const error = ref<string | null>(null)
const selectedStatus = ref<'scheduled' | 'rented' | 'returned' | 'cancelled'>('scheduled')
const showDetailsDialog = ref(false)
const selectedEpisode = ref<UserEpisode | null>(null)

// Status tabs configuration
const statusTabs = [
  { label: 'Scheduled', value: 'scheduled' as const, severity: 'info' },
  { label: 'Rented', value: 'rented' as const, severity: 'warning' },
  { label: 'Returned', value: 'returned' as const, severity: 'success' },
  { label: 'Cancelled', value: 'cancelled' as const, severity: 'danger' }
]

// Computed properties
const episodesByStatus = computed(() => episodesStore.episodesByStatus)

const filteredEpisodes = computed(() => {
  return episodesByStatus.value[selectedStatus.value] || []
})

const getEpisodeCountByStatus = (status: string) => {
  return episodesByStatus.value[status]?.length || 0
}

// Methods
const setSelectedStatus = (status: 'scheduled' | 'rented' | 'returned' | 'cancelled') => {
  selectedStatus.value = status
}

const loadRentals = async () => {
  if (!authStore.currentUserProfile?.employee_number) {
    error.value = 'User not authenticated'
    return
  }

  loading.value = true
  error.value = null

  try {
    const userRole = authStore.currentUserProfile.role

    if (userRole === 'Unit') {
      // Unit role: Show all rentals from the same unit
      if (!authStore.currentUserProfile.unit) {
        error.value = 'Unit information not found for user'
        return
      }
      episodesStore.initializeUnitEpisodesListener(authStore.currentUserProfile.unit)
    } else {
      // Personal and Admin roles: Show only user's own rentals
      episodesStore.initializeUserEpisodesListener(authStore.currentUserProfile.employee_number)
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load rentals'
    toast.add({
      severity: 'error',
      summary: 'Loading Error',
      detail: error.value,
      life: 5000
    })
  } finally {
    loading.value = false
  }
}

const handleCancelEpisode = async (episode: UserEpisode) => {
  if (episode.status !== 'scheduled') {
    toast.add({
      severity: 'warn',
      summary: 'Cannot Cancel',
      detail: 'Only scheduled episodes can be cancelled',
      life: 5000
    })
    return
  }

  // Close details dialog immediately if it's open for this episode
  if (selectedEpisode.value?.id === episode.id) {
    showDetailsDialog.value = false
    selectedEpisode.value = null
  }

  confirm.require({
    message: `Are you sure you want to cancel episode ${episode.id}? This action cannot be undone.`,
    header: 'Confirm Cancellation',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Keep Episode',
    acceptLabel: 'Cancel Episode',
    accept: async () => {
      try {
        if (!authStore.currentUserProfile?.employee_number) {
          throw new Error('User not authenticated')
        }

        await episodesStore.cancelEpisode(
          authStore.currentUserProfile.employee_number,
          episode.id
        )

        toast.add({
          severity: 'success',
          summary: 'Episode Cancelled',
          detail: `Episode ${episode.id} has been cancelled successfully`,
          life: 5000
        })
      } catch (err) {
        toast.add({
          severity: 'error',
          summary: 'Cancellation Failed',
          detail: err instanceof Error ? err.message : 'Failed to cancel episode',
          life: 5000
        })
      }
    }
  })
}

const handleViewDetails = (episode: UserEpisode) => {
  selectedEpisode.value = episode
  showDetailsDialog.value = true
}

// Lifecycle
onMounted(() => {
  loadRentals()
})

onUnmounted(() => {
  episodesStore.stopUserEpisodesListener()
})
</script>

<style scoped>
.my-rentals-container {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 2rem;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 2.5rem;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
  line-height: 1.5;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
  color: var(--text-color-secondary);
}

.error-container h3 {
  margin: 1rem 0 0.5rem 0;
  color: var(--text-color);
}

.rentals-content {
  background: var(--surface-card);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.status-tabs {
  display: flex;
  padding: 1.5rem;
  gap: 0.75rem;
  background: var(--surface-ground);
  border-bottom: 1px solid var(--surface-border);
}

.episodes-section {
  padding: 1.5rem;
  min-height: 400px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  color: var(--text-color-secondary);
}

.empty-state h3 {
  margin: 1rem 0 0.5rem 0;
  color: var(--text-color);
}

.empty-state p {
  margin: 0 0 1.5rem 0;
}

.episodes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .my-rentals-container {
    padding: 1rem;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
  
  .status-tabs {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .episodes-grid {
    grid-template-columns: 1fr;
  }
  
  .episode-details-dialog {
    width: 95vw !important;
    min-width: unset !important;
  }
}
</style>
