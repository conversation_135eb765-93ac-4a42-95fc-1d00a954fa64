{"indexes": [{"collectionGroup": "rentals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "startDate", "order": "DESCENDING"}]}, {"collectionGroup": "rentals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "startDate", "order": "ASCENDING"}]}, {"collectionGroup": "items", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "available", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}], "fieldOverrides": []}