/* PrimeVue Component Override CSS - Load this AFTER PrimeVue CSS */

/* FORCE ALL DIALOGS TO BE WHITE */
.p-dialog,
.p-dialog .p-dialog-content,
.p-dialog .p-dialog-header,
.p-dialog .p-dialog-footer {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

/* FORCE ALL OVERLAYS TO BE WHITE */
.p-dropdown-panel,
.p-multiselect-panel,
.p-calendar-panel,
.p-overlay-panel,
.p-overlaypanel,
.p-menu,
.p-contextmenu,
.p-tooltip,
.p-autocomplete-panel,
.p-confirmdialog,
.p-sidebar {
  background: white !important;
  background-color: white !important;
  border: 1px solid #ddd !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

/* FORCE ALL LIST CONTAINERS TO BE WHITE */
.p-dropdown-items,
.p-multiselect-items,
.p-autocomplete-items,
.p-menu-list,
.p-contextmenu-root-list {
  background: white !important;
  background-color: white !important;
}

/* FORCE ALL LIST ITEMS TO BE WHITE */
.p-dropdown-item,
.p-multiselect-item,
.p-autocomplete-item,
.p-menuitem-link,
.p-menuitem-content {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

/* HOVER STATES */
.p-dropdown-item:hover,
.p-multiselect-item:hover,
.p-autocomplete-item:hover,
.p-menuitem-link:hover {
  background: #f5f5f5 !important;
  background-color: #f5f5f5 !important;
  color: #333 !important;
}

/* SELECTED STATES */
.p-dropdown-item.p-highlight,
.p-multiselect-item.p-highlight,
.p-autocomplete-item.p-highlight {
  background: #667eea !important;
  background-color: #667eea !important;
  color: white !important;
}

/* CALENDAR SPECIFIC FIXES */
.p-datepicker,
.p-datepicker-header,
.p-datepicker-calendar,
.p-datepicker-calendar thead,
.p-datepicker-calendar tbody,
.p-datepicker-calendar td {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

.p-datepicker-calendar td:hover {
  background: #f5f5f5 !important;
  background-color: #f5f5f5 !important;
}

.p-datepicker-calendar td.p-highlight {
  background: #667eea !important;
  background-color: #667eea !important;
  color: white !important;
}

/* INPUT FIELDS */
.p-inputtext,
.p-dropdown .p-dropdown-label,
.p-multiselect .p-multiselect-label,
.p-calendar .p-inputtext {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

/* TOAST MESSAGES */
.p-toast .p-toast-message {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
  border: 1px solid #ddd !important;
}

/* NUCLEAR OPTION - Apply to everything */
[class*="p-"]:not(.p-button):not(.p-badge):not(.p-progressbar):not(.p-slider):not(.p-avatar) {
  background-color: white !important;
}

/* Ensure proper z-index for overlays */
.p-component-overlay {
  z-index: 10000 !important;
}

.p-dialog {
  z-index: 10001 !important;
}

.p-dropdown-panel,
.p-multiselect-panel,
.p-calendar-panel,
.p-overlay-panel {
  z-index: 10002 !important;
}
