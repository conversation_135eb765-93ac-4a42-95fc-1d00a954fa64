// Test script to verify key functionality
console.log('🧪 Testing Rental Platform Functionality...\n');

// Test 1: Check if CSS overrides are loaded
console.log('1. Testing CSS Overrides...');
const cssOverrides = document.querySelector('link[href*="primevue-overrides"]') || 
                    document.querySelector('style[data-vite-dev-id*="primevue-overrides"]');
console.log(cssOverrides ? '✅ PrimeVue overrides loaded' : '❌ PrimeVue overrides not found');

// Test 2: Check if main components are rendered
console.log('\n2. Testing Component Rendering...');
const app = document.getElementById('app');
console.log(app ? '✅ App container found' : '❌ App container not found');

// Test 3: Check for Vue app instance
console.log('\n3. Testing Vue App...');
const vueApp = window.__VUE__;
console.log(vueApp ? '✅ Vue app detected' : '❌ Vue app not detected');

// Test 4: Check for router
console.log('\n4. Testing Router...');
setTimeout(() => {
  const routerLinks = document.querySelectorAll('a[href*="/"]');
  console.log(routerLinks.length > 0 ? '✅ Router links found' : '❌ No router links found');
  
  // Test 5: Check for PrimeVue components
  console.log('\n5. Testing PrimeVue Components...');
  const primeComponents = document.querySelectorAll('[class*="p-"]');
  console.log(primeComponents.length > 0 ? '✅ PrimeVue components found' : '❌ No PrimeVue components found');
  
  // Test 6: Check for dialogs
  console.log('\n6. Testing Dialog Elements...');
  const dialogs = document.querySelectorAll('.p-dialog, [role="dialog"]');
  console.log(`Found ${dialogs.length} dialog elements`);
  
  // Test 7: Check for dropdowns
  console.log('\n7. Testing Dropdown Elements...');
  const dropdowns = document.querySelectorAll('.p-dropdown, .p-multiselect');
  console.log(`Found ${dropdowns.length} dropdown elements`);
  
  console.log('\n🎉 Test completed! Check browser console for detailed results.');
}, 2000);
