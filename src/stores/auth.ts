import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import {
  signInWithCustomToken,
  signOut,
  onAuthStateChanged,
  type User
} from 'firebase/auth'
import { doc, getDoc, setDoc, updateDoc, collection, getDocs, query, where } from 'firebase/firestore'
import { auth, db } from '@/firebase/config'

export interface UserProfile {
  uid: string
  employee_number: string
  email: string
  displayName: string
  department?: string
  role: 'user' | 'admin'
  episodes: Record<string, {
    id: string
    dates: string[]
    items: Array<{
      item_group_id: string
      item_group_name: string
      item_group_type: 'resus_trainings' | 'venues' | 'audio_visuals'
      individual_item_id: string
      quantity: number
    }>
    status: 'cancelled' | 'scheduled' | 'rented' | 'returned'
  }>
  createdAt: Date
  lastLogin: Date
}

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const userProfile = ref<UserProfile | null>(null)
  const loading = ref(false)
  const initialized = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isAuthenticated = computed(() => !!user.value)
  const isAdmin = computed(() => userProfile.value?.role === 'admin')
  const currentUser = computed(() => user.value)
  const currentUserProfile = computed(() => userProfile.value)

  // Actions
  const initializeAuth = async () => {
    return new Promise<void>((resolve) => {
      const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
        if (firebaseUser) {
          user.value = firebaseUser
          await loadUserProfile(firebaseUser.uid)
        } else {
          user.value = null
          userProfile.value = null
        }
        initialized.value = true
        unsubscribe()
        resolve()
      })
    })
  }

  const loginWithExternalAPI = async (credentials: { username: string; password: string }) => {
    loading.value = true
    error.value = null

    try {
      // Call external API for authentication
      const response = await fetch(import.meta.env.VITE_EXTERNAL_AUTH_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      })

      if (!response.ok) {
        throw new Error('Invalid credentials')
      }

      const data = await response.json()
      
      // Assuming the external API returns a custom token for Firebase
      if (data.customToken) {
        const userCredential = await signInWithCustomToken(auth, data.customToken)
        user.value = userCredential.user
        
        // Create or update user profile
        await createOrUpdateUserProfile({
          uid: userCredential.user.uid,
          employee_number: data.employee_number || '',
          email: data.email || userCredential.user.email || '',
          displayName: data.displayName || userCredential.user.displayName || '',
          department: data.department,
          role: data.role || 'user',
          episodes: {},
          createdAt: new Date(),
          lastLogin: new Date()
        })
        
        await loadUserProfile(userCredential.user.uid)
      } else {
        throw new Error('Authentication failed')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Login failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    loading.value = true
    try {
      await signOut(auth)
      user.value = null
      userProfile.value = null
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Logout failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  const loadUserProfile = async (uid: string) => {
    try {
      // First, find the user by UID to get their employee number
      const usersQuery = query(collection(db, 'users'), where('uid', '==', uid))
      const usersSnapshot = await getDocs(usersQuery)

      if (!usersSnapshot.empty) {
        const userDoc = usersSnapshot.docs[0]
        const data = userDoc.data()
        const employeeNumber = userDoc.id // Document ID is the employee number

        userProfile.value = {
          uid,
          employee_number: employeeNumber,
          email: data.email,
          displayName: data.displayName,
          department: data.department,
          role: data.role || 'user',
          episodes: data.episodes || {},
          createdAt: data.createdAt?.toDate() || new Date(),
          lastLogin: data.lastLogin?.toDate() || new Date()
        }

        // Update episode statuses based on current date
        await updateEpisodeStatuses(employeeNumber)
      }
    } catch (err) {
      console.error('Error loading user profile:', err)
    }
  }

  const createOrUpdateUserProfile = async (profile: UserProfile) => {
    try {
      // Use employee number as document ID
      await setDoc(doc(db, 'users', profile.employee_number), {
        uid: profile.uid,
        email: profile.email,
        displayName: profile.displayName,
        department: profile.department,
        role: profile.role,
        episodes: profile.episodes || {},
        createdAt: profile.createdAt,
        lastLogin: profile.lastLogin
      }, { merge: true })
    } catch (err) {
      console.error('Error creating/updating user profile:', err)
      throw err
    }
  }

  const updateEpisodeStatuses = async (employeeNumber: string) => {
    try {
      const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD format
      const userDocRef = doc(db, 'users', employeeNumber)
      const userDoc = await getDoc(userDocRef)

      if (!userDoc.exists()) return

      const userData = userDoc.data()
      const episodes = userData.episodes || {}
      let hasUpdates = false

      for (const episodeId in episodes) {
        const episode = episodes[episodeId]
        const episodeDates = episode.dates || []
        const latestDate = episodeDates.sort().pop()

        // Check if today's date is in the episode dates and status is scheduled
        if (episodeDates.includes(today) && episode.status === 'scheduled') {
          episode.status = 'rented'
          hasUpdates = true

          // Update individual items status
          for (const item of episode.items) {
            await updateIndividualItemStatus(item.item_group_type, item.item_group_id, item.individual_item_id, episodeId, 'rented')
          }
        }

        // Check if episode is finished (latest date is before today and status is rented)
        if (latestDate && latestDate < today && episode.status === 'rented') {
          episode.status = 'returned'
          hasUpdates = true

          // Update individual items status
          for (const item of episode.items) {
            await updateIndividualItemStatus(item.item_group_type, item.item_group_id, item.individual_item_id, episodeId, 'returned')
          }
        }
      }

      if (hasUpdates) {
        await updateDoc(userDocRef, { episodes })
      }
    } catch (err) {
      console.error('Error updating episode statuses:', err)
    }
  }

  const updateIndividualItemStatus = async (
    type: string,
    groupId: string,
    itemId: string,
    episodeId: string,
    status: 'scheduled' | 'rented' | 'returned' | 'cancelled'
  ) => {
    try {
      const itemGroupRef = doc(db, `items/${type}/groups`, groupId)
      const itemGroupDoc = await getDoc(itemGroupRef)

      if (itemGroupDoc.exists()) {
        const groupData = itemGroupDoc.data()
        const items = groupData.items || []

        const itemIndex = items.findIndex((item: any) => item.id === itemId)
        if (itemIndex >= 0 && items[itemIndex].episodes[episodeId]) {
          items[itemIndex].episodes[episodeId].status = status
          await updateDoc(itemGroupRef, { items })
        }
      }
    } catch (err) {
      console.error('Error updating individual item status:', err)
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    user: readonly(user),
    userProfile: readonly(userProfile),
    loading: readonly(loading),
    initialized: readonly(initialized),
    error: readonly(error),
    
    // Getters
    isAuthenticated,
    isAdmin,
    currentUser,
    currentUserProfile,
    
    // Actions
    initializeAuth,
    loginWithExternalAPI,
    logout,
    loadUserProfile,
    createOrUpdateUserProfile,
    updateEpisodeStatuses,
    updateIndividualItemStatus,
    clearError
  }
})
