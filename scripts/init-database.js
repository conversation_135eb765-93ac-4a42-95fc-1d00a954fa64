// Database initialization script for TKOH NSD Renting Platform
// Run this script to set up the initial database structure

import { initializeApp } from 'firebase/app'
import { getFirestore, collection, doc, setDoc } from 'firebase/firestore'

// Firebase configuration - replace with your actual config
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID
}

// Initialize Firebase
const app = initializeApp(firebaseConfig)
const db = getFirestore(app)

// Sample data structure
const sampleData = {
  // Categories for each item type
  categories: [
    // Resuscitating Training categories
    { id: 'rt_dummies', name: 'Training Dummies', type: 'resus_trainings', description: 'CPR and medical training dummies' },
    { id: 'rt_equipment', name: 'Training Equipment', type: 'resus_trainings', description: 'Medical training equipment' },
    
    // Venue categories
    { id: 'v_classrooms', name: 'Classrooms', type: 'venues', description: 'Training and meeting rooms' },
    { id: 'v_labs', name: 'Laboratories', type: 'venues', description: 'Practical training labs' },
    
    // Audio Visual categories
    { id: 'av_projectors', name: 'Projectors', type: 'audio_visuals', description: 'Projection equipment' },
    { id: 'av_sound', name: 'Sound Systems', type: 'audio_visuals', description: 'Audio equipment' },
    { id: 'av_cameras', name: 'Cameras', type: 'audio_visuals', description: 'Recording equipment' }
  ],

  // Sample item groups
  itemGroups: {
    resus_trainings: [
      {
        id: 'dummy_fatguy',
        name: 'FAT GUY Training Dummy',
        description: 'Adult CPR training dummy with realistic features',
        category: 'rt_dummies',
        type: 'resus_trainings',
        contraindications: [
          { item_group_name: 'Pediatric Dummy', reason: 'Cannot use adult and pediatric dummies simultaneously for training consistency' }
        ],
        items: [
          { id: 'dummy_fatguy_001', remarks: 'Good condition', episodes: {}, use_count: 0, suspended: false },
          { id: 'dummy_fatguy_002', remarks: 'Excellent condition', episodes: {}, use_count: 0, suspended: false },
          { id: 'dummy_fatguy_003', remarks: 'Fair condition', episodes: {}, use_count: 0, suspended: false }
        ],
        imageUrl: '/images/fat-guy-dummy.jpg',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ],
    venues: [
      {
        id: 'classroom_a',
        name: 'Classroom A',
        description: 'Main training classroom with 30 seats',
        category: 'v_classrooms',
        type: 'venues',
        contraindications: [
          { item_group_name: 'Classroom B', reason: 'Adjacent rooms - noise interference during simultaneous use' }
        ],
        items: [
          { id: 'classroom_a_001', remarks: 'Main classroom', episodes: {}, use_count: 0, suspended: false }
        ],
        imageUrl: '/images/classroom-a.jpg',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ],
    audio_visuals: [
      {
        id: 'projector_epson',
        name: 'Epson Projector XYZ',
        description: 'High-definition projector for presentations',
        category: 'av_projectors',
        type: 'audio_visuals',
        contraindications: [],
        items: [
          { id: 'projector_epson_001', remarks: 'Excellent condition', episodes: {}, use_count: 0, suspended: false },
          { id: 'projector_epson_002', remarks: 'Good condition', episodes: {}, use_count: 0, suspended: false }
        ],
        imageUrl: '/images/epson-projector.jpg',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]
  }
}

async function initializeDatabase() {
  try {
    console.log('Initializing database structure...')

    // Create categories
    console.log('Creating categories...')
    for (const category of sampleData.categories) {
      await setDoc(doc(db, 'categories', category.id), category)
      console.log(`Created category: ${category.name}`)
    }

    // Create item groups for each type
    for (const [type, groups] of Object.entries(sampleData.itemGroups)) {
      console.log(`Creating ${type} item groups...`)
      for (const group of groups) {
        await setDoc(doc(db, `items/${type}/groups`, group.id), group)
        console.log(`Created item group: ${group.name}`)
      }
    }

    console.log('Database initialization completed successfully!')
    console.log('\nNext steps:')
    console.log('1. Set up your Firebase authentication')
    console.log('2. Configure your external authentication API')
    console.log('3. Deploy Firestore rules: firebase deploy --only firestore:rules')
    console.log('4. Start your Vue.js application: npm run dev')

  } catch (error) {
    console.error('Error initializing database:', error)
  }
}

// Run the initialization
initializeDatabase()
