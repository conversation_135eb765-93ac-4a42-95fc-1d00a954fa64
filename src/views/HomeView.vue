<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useItemsStore } from '@/stores/items'
import { useRentalsStore } from '@/stores/rentals'

const router = useRouter()
const authStore = useAuthStore()
const itemsStore = useItemsStore()
const rentalsStore = useRentalsStore()

const loading = ref(false)

// Computed properties for dashboard stats
const totalItems = computed(() => itemsStore.allItems?.length || 0)
const availableItems = computed(() => itemsStore.availableItems?.length || 0)
const myActiveRentals = computed(() => rentalsStore.userActiveRentals?.length || 0)
const pendingApprovals = computed(() => rentalsStore.pendingRentals?.length || 0)

// Quick actions
const quickActions = [
  {
    title: 'New Booking',
    description: 'Start a new equipment rental request',
    icon: 'pi pi-plus-circle',
    color: '#667eea',
    route: '/booking'
  },
  {
    title: 'Browse Equipment',
    description: 'View all available equipment',
    icon: 'pi pi-box',
    color: '#764ba2',
    route: '/items'
  },
  {
    title: 'My Rentals',
    description: 'View your current and past rentals',
    icon: 'pi pi-calendar',
    color: '#f093fb',
    route: '/my-rentals'
  },
  {
    title: 'Profile Settings',
    description: 'Update your profile information',
    icon: 'pi pi-user',
    color: '#4facfe',
    route: '/profile'
  }
]

const navigateTo = (route: string) => {
  router.push(route)
}

onMounted(async () => {
  loading.value = true
  try {
    // Initialize data if not already loaded
    if (!itemsStore.allItems?.length) {
      await itemsStore.fetchItemGroups()
    }
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
  } finally {
    loading.value = false
  }
})
</script>

<template>
  <div class="dashboard-container">
    <!-- Welcome Header -->
    <div class="welcome-header">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1>Welcome back, {{ authStore.currentUserProfile?.displayName || 'User' }}!</h1>
          <p>Manage your equipment rentals and bookings from your dashboard</p>
        </div>
        <div class="welcome-stats">
          <div class="stat-item">
            <div class="stat-icon">
              <i class="pi pi-calendar"></i>
            </div>
            <div class="stat-info">
              <span class="stat-value">{{ myActiveRentals }}</span>
              <span class="stat-label">Active Rentals</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions Grid -->
    <div class="quick-actions-section">
      <h2 class="section-title">
        <i class="pi pi-bolt"></i>
        Quick Actions
      </h2>
      <div class="actions-grid">
        <div
          v-for="action in quickActions"
          :key="action.title"
          class="action-card"
          @click="navigateTo(action.route)"
        >
          <div class="action-icon" :style="{ background: action.color }">
            <i :class="action.icon"></i>
          </div>
          <div class="action-content">
            <h3>{{ action.title }}</h3>
            <p>{{ action.description }}</p>
          </div>
          <div class="action-arrow">
            <i class="pi pi-chevron-right"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Dashboard Stats -->
    <div class="stats-section">
      <h2 class="section-title">
        <i class="pi pi-chart-bar"></i>
        System Overview
      </h2>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-icon-large">
              <i class="pi pi-box"></i>
            </div>
            <div class="stat-trend positive">
              <i class="pi pi-arrow-up"></i>
              <span>Available</span>
            </div>
          </div>
          <div class="stat-body">
            <h3>{{ availableItems }}</h3>
            <p>Equipment Items</p>
            <div class="stat-progress">
              <div class="progress-bar" :style="{ width: `${(availableItems / totalItems) * 100}%` }"></div>
            </div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-icon-large">
              <i class="pi pi-calendar"></i>
            </div>
            <div class="stat-trend neutral">
              <i class="pi pi-minus"></i>
              <span>Current</span>
            </div>
          </div>
          <div class="stat-body">
            <h3>{{ myActiveRentals }}</h3>
            <p>My Active Rentals</p>
            <div class="stat-progress">
              <div class="progress-bar" style="width: 60%; background: #f093fb;"></div>
            </div>
          </div>
        </div>

        <div v-if="authStore.isAdmin" class="stat-card">
          <div class="stat-header">
            <div class="stat-icon-large">
              <i class="pi pi-clock"></i>
            </div>
            <div class="stat-trend warning">
              <i class="pi pi-exclamation-triangle"></i>
              <span>Pending</span>
            </div>
          </div>
          <div class="stat-body">
            <h3>{{ pendingApprovals }}</h3>
            <p>Pending Approvals</p>
            <div class="stat-progress">
              <div class="progress-bar" style="width: 30%; background: #ffa726;"></div>
            </div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-icon-large">
              <i class="pi pi-users"></i>
            </div>
            <div class="stat-trend positive">
              <i class="pi pi-check-circle"></i>
              <span>System</span>
            </div>
          </div>
          <div class="stat-body">
            <h3>Online</h3>
            <p>System Status</p>
            <div class="stat-progress">
              <div class="progress-bar" style="width: 100%; background: #4caf50;"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="activity-section">
      <h2 class="section-title">
        <i class="pi pi-history"></i>
        Recent Activity
      </h2>
      <div class="activity-card">
        <div class="activity-content">
          <div class="activity-placeholder">
            <i class="pi pi-info-circle"></i>
            <h4>Activity Feed Coming Soon</h4>
            <p>Recent booking activities and system updates will appear here.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0;
}

/* Welcome Header */
.welcome-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
  margin-bottom: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.welcome-content {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.25rem;
  font-weight: 700;
}

.welcome-text p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.welcome-stats {
  display: flex;
  gap: 2rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem 1.5rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.stat-icon {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
  line-height: 1;
}

/* Section Titles */
.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-title i {
  color: #667eea;
  font-size: 1.25rem;
}

/* Quick Actions */
.quick-actions-section {
  margin-bottom: 3rem;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
  border: 1px solid #f1f3f4;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border-color: #667eea;
}

.action-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.action-content {
  flex: 1;
}

.action-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.action-content p {
  margin: 0;
  font-size: 0.9rem;
  color: #6c757d;
  line-height: 1.4;
}

.action-arrow {
  color: #dee2e6;
  font-size: 1.25rem;
  transition: all 0.3s ease;
}

.action-card:hover .action-arrow {
  color: #667eea;
  transform: translateX(4px);
}

/* Stats Section */
.stats-section {
  margin-bottom: 3rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f3f4;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.stat-icon-large {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
}

.stat-trend.positive {
  background: #e8f5e8;
  color: #2e7d32;
}

.stat-trend.warning {
  background: #fff3e0;
  color: #f57c00;
}

.stat-trend.neutral {
  background: #f5f5f5;
  color: #616161;
}

.stat-body h3 {
  margin: 0 0 0.25rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
}

.stat-body p {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: #6c757d;
}

.stat-progress {
  height: 6px;
  background: #f1f3f4;
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* Activity Section */
.activity-section {
  margin-bottom: 2rem;
}

.activity-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f3f4;
  overflow: hidden;
}

.activity-content {
  padding: 2rem;
}

.activity-placeholder {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
}

.activity-placeholder i {
  font-size: 3rem;
  color: #dee2e6;
  margin-bottom: 1rem;
}

.activity-placeholder h4 {
  margin: 0 0 0.5rem 0;
  color: #495057;
  font-size: 1.25rem;
}

.activity-placeholder p {
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .welcome-content {
    padding: 0 1.5rem;
  }

  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
    padding: 0 1rem;
  }

  .welcome-text h1 {
    font-size: 1.75rem;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .action-card {
    padding: 1.25rem;
  }

  .stat-card {
    padding: 1.25rem;
  }

  .welcome-stats {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }

  .stat-item {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .welcome-header {
    margin: -2rem -1rem 2rem -1rem;
    border-radius: 0;
  }

  .section-title {
    font-size: 1.25rem;
  }

  .actions-grid,
  .stats-grid {
    gap: 1rem;
  }
}
</style>
