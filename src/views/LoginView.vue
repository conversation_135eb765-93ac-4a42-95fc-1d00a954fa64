<template>
  <div class="login-container">
    <div class="login-card">
      <h1>TKOH NSD Renting Platform</h1>
      <p class="mb-4">Enter your employee number to login</p>

      <div class="p-field mb-3">
        <label for="employeeNumber" class="block mb-2">Employee Number</label>
        <InputText
          id="employeeNumber"
          v-model="employeeNumber"
          placeholder="Enter employee number"
          class="w-full"
          @keyup.enter="handleLogin"
        />
      </div>

      <div class="p-field mb-3">
        <label for="password" class="block mb-2">Password</label>
        <Password
          id="password"
          v-model="password"
          placeholder="Enter password"
          class="w-full"
          :feedback="false"
          toggleMask
          @keyup.enter="handleLogin"
        />
      </div>

      <Button
        label="Login"
        @click="handleLogin"
        :loading="isLoading"
        class="w-full"
      />

      <div v-if="isDemoMode" class="mt-4 p-3 bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700">
        <p class="text-sm">
          <strong>Demo Mode:</strong> Use any employee number (try "admin" for admin access)
        </p>
      </div>

      <div v-if="errorMessage" class="mt-3">
        <Message severity="error" :closable="false">{{ errorMessage }}</Message>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { isDemoMode } from '@/firebase/config'
import InputText from 'primevue/inputtext'
import Password from 'primevue/password'
import Button from 'primevue/button'
import Message from 'primevue/message'

const router = useRouter()
const authStore = useAuthStore()

const employeeNumber = ref('')
const password = ref('')
const isLoading = ref(false)
const errorMessage = ref('')

const handleLogin = async () => {
  console.log('Login button clicked')

  if (!employeeNumber.value.trim()) {
    errorMessage.value = 'Please enter your employee number'
    return
  }

  if (!isDemoMode && !password.value.trim()) {
    errorMessage.value = 'Please enter your password'
    return
  }

  try {
    isLoading.value = true
    errorMessage.value = ''

    console.log('Attempting login with:', employeeNumber.value.trim())

    await authStore.loginWithExternalAPI({
      username: employeeNumber.value.trim(),
      password: password.value.trim()
    })

    console.log('Login successful, redirecting...')
    console.log('Auth state:', authStore.isAuthenticated)

    // Redirect to main application
    router.push('/')
  } catch (error) {
    console.error('Login error:', error)
    errorMessage.value = 'Login failed. Please check your credentials and try again.'
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: var(--surface-ground);
  padding: 1rem;
}

.login-card {
  padding: 2rem;
  background: var(--surface-card);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
  width: 100%;
  max-width: 400px;
}

.login-card h1 {
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.p-field {
  text-align: left;
}

.p-field label {
  font-weight: 600;
  color: var(--text-color);
}
</style>
