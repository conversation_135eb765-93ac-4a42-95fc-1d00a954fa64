import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  query, 
  where, 
  orderBy,
  onSnapshot,
  runTransaction,
  type Unsubscribe
} from 'firebase/firestore'
import { db, isDemoMode } from '@/firebase/config'
import { useItemsStore } from './items'

export interface Rental {
  id: string
  userId: string
  userEmail: string
  userName: string
  itemId: string
  itemName: string
  quantity: number
  startDate: Date
  endDate: Date
  status: 'pending' | 'approved' | 'active' | 'returned' | 'cancelled' | 'overdue'
  notes?: string
  adminNotes?: string
  approvedBy?: string
  approvedAt?: Date
  returnedAt?: Date
  createdAt: Date
  updatedAt: Date
}

export interface RentalRequest {
  itemId: string
  quantity: number
  startDate: Date
  endDate: Date
  notes?: string
}

export const useRentalsStore = defineStore('rentals', () => {
  // State
  const rentals = ref<Rental[]>([])
  const userRentals = ref<Rental[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // Real-time listeners
  let rentalsUnsubscribe: Unsubscribe | null = null
  let userRentalsUnsubscribe: Unsubscribe | null = null

  // Getters
  const pendingRentals = computed(() => 
    rentals.value.filter(rental => rental.status === 'pending')
  )

  const activeRentals = computed(() => 
    rentals.value.filter(rental => rental.status === 'active')
  )

  const overdueRentals = computed(() => 
    rentals.value.filter(rental => rental.status === 'overdue')
  )

  const userActiveRentals = computed(() => 
    userRentals.value.filter(rental => 
      rental.status === 'active' || rental.status === 'approved'
    )
  )

  const userPendingRentals = computed(() => 
    userRentals.value.filter(rental => rental.status === 'pending')
  )

  // Actions
  const initializeRealTimeListeners = (userId?: string) => {
    if (isDemoMode) {
      // Demo mode - load mock data
      allRentals.value = []
      userRentals.value = []
      return
    }

    // Listen to all rentals (for admin)
    const allRentalsQuery = query(
      collection(db!, 'rentals'),
      orderBy('created_at', 'desc')
    )
    
    rentalsUnsubscribe = onSnapshot(allRentalsQuery, (snapshot) => {
      rentals.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        startDate: doc.data().startDate?.toDate() || new Date(),
        endDate: doc.data().endDate?.toDate() || new Date(),
        approvedAt: doc.data().approvedAt?.toDate(),
        returnedAt: doc.data().returnedAt?.toDate(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      })) as Rental[]
    }, (err) => {
      console.error('Error listening to rentals:', err)
      error.value = 'Failed to load rentals'
    })

    // Listen to user-specific rentals
    if (userId) {
      const userRentalsQuery = query(
        collection(db!, 'rentals'),
        where('userId', '==', userId),
        orderBy('created_at', 'desc')
      )
      
      userRentalsUnsubscribe = onSnapshot(userRentalsQuery, (snapshot) => {
        userRentals.value = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          startDate: doc.data().startDate?.toDate() || new Date(),
          endDate: doc.data().endDate?.toDate() || new Date(),
          approvedAt: doc.data().approvedAt?.toDate(),
          returnedAt: doc.data().returnedAt?.toDate(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date()
        })) as Rental[]
      }, (err) => {
        console.error('Error listening to user rentals:', err)
      })
    }
  }

  const stopRealTimeListeners = () => {
    if (rentalsUnsubscribe) {
      rentalsUnsubscribe()
      rentalsUnsubscribe = null
    }
    if (userRentalsUnsubscribe) {
      userRentalsUnsubscribe()
      userRentalsUnsubscribe = null
    }
  }

  const createRental = async (
    rentalRequest: RentalRequest,
    userInfo: { userId: string; userEmail: string; userName: string }
  ) => {
    loading.value = true
    error.value = null
    
    try {
      const itemsStore = useItemsStore()
      
      // Use transaction to ensure data consistency
      const rentalId = await runTransaction(db, async (transaction) => {
        // Get the item to check availability
        const itemRef = doc(db, 'items', rentalRequest.itemId)
        const itemDoc = await transaction.get(itemRef)
        
        if (!itemDoc.exists()) {
          throw new Error('Item not found')
        }
        
        const itemData = itemDoc.data()
        const availableStock = itemData.availableStock || 0
        
        if (availableStock < rentalRequest.quantity) {
          throw new Error('Insufficient stock available')
        }
        
        // Create the rental
        const now = new Date()
        const rentalData = {
          userId: userInfo.userId,
          userEmail: userInfo.userEmail,
          userName: userInfo.userName,
          itemId: rentalRequest.itemId,
          itemName: itemData.name,
          quantity: rentalRequest.quantity,
          startDate: rentalRequest.startDate,
          endDate: rentalRequest.endDate,
          status: 'pending' as const,
          notes: rentalRequest.notes || '',
          createdAt: now,
          updatedAt: now
        }
        
        const rentalRef = doc(collection(db!, 'rentals'))
        transaction.set(rentalRef, rentalData)
        
        // Reserve the stock (reduce available stock)
        transaction.update(itemRef, {
          availableStock: availableStock - rentalRequest.quantity,
          updatedAt: now
        })
        
        return rentalRef.id
      })
      
      return rentalId
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create rental'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateRentalStatus = async (
    rentalId: string, 
    status: Rental['status'],
    adminInfo?: { adminId: string; adminNotes?: string }
  ) => {
    loading.value = true
    error.value = null
    
    try {
      await runTransaction(db, async (transaction) => {
        const rentalRef = doc(db, 'rentals', rentalId)
        const rentalDoc = await transaction.get(rentalRef)
        
        if (!rentalDoc.exists()) {
          throw new Error('Rental not found')
        }
        
        const rentalData = rentalDoc.data()
        const now = new Date()
        
        const updates: any = {
          status,
          updatedAt: now
        }
        
        if (adminInfo) {
          updates.approvedBy = adminInfo.adminId
          updates.approvedAt = now
          if (adminInfo.adminNotes) {
            updates.adminNotes = adminInfo.adminNotes
          }
        }
        
        if (status === 'returned') {
          updates.returnedAt = now
          
          // Return the stock to available inventory
          const itemRef = doc(db, 'items', rentalData.itemId)
          const itemDoc = await transaction.get(itemRef)
          
          if (itemDoc.exists()) {
            const itemData = itemDoc.data()
            transaction.update(itemRef, {
              availableStock: (itemData.availableStock || 0) + rentalData.quantity,
              updatedAt: now
            })
          }
        } else if (status === 'cancelled') {
          // Return the reserved stock if rental is cancelled
          const itemRef = doc(db, 'items', rentalData.itemId)
          const itemDoc = await transaction.get(itemRef)
          
          if (itemDoc.exists()) {
            const itemData = itemDoc.data()
            transaction.update(itemRef, {
              availableStock: (itemData.availableStock || 0) + rentalData.quantity,
              updatedAt: now
            })
          }
        }
        
        transaction.update(rentalRef, updates)
      })
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update rental status'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchUserRentals = async (userId: string) => {
    loading.value = true
    error.value = null

    try {
      if (isDemoMode) {
        // In demo mode, return empty array
        userRentals.value = []
        return
      }

      const q = query(
        collection(db!, 'rentals'),
        where('userId', '==', userId),
        orderBy('created_at', 'desc')
      )
      
      const snapshot = await getDocs(q)
      userRentals.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        startDate: doc.data().startDate?.toDate() || new Date(),
        endDate: doc.data().endDate?.toDate() || new Date(),
        approvedAt: doc.data().approvedAt?.toDate(),
        returnedAt: doc.data().returnedAt?.toDate(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      })) as Rental[]
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch user rentals'
      throw err
    } finally {
      loading.value = false
    }
  }

  const checkItemAvailability = async (
    itemId: string, 
    startDate: Date, 
    endDate: Date, 
    quantity: number
  ): Promise<boolean> => {
    try {
      // Get current item stock
      const itemDoc = await getDoc(doc(db, 'items', itemId))
      if (!itemDoc.exists()) return false
      
      const itemData = itemDoc.data()
      const totalStock = itemData.totalStock || 0
      
      // Get overlapping rentals
      const q = query(
        collection(db!, 'rentals'),
        where('itemId', '==', itemId),
        where('status', 'in', ['approved', 'active'])
      )
      
      const snapshot = await getDocs(q)
      let reservedQuantity = 0
      
      snapshot.docs.forEach(doc => {
        const rental = doc.data()
        const rentalStart = rental.startDate?.toDate()
        const rentalEnd = rental.endDate?.toDate()
        
        // Check if dates overlap
        if (rentalStart && rentalEnd && 
            startDate < rentalEnd && endDate > rentalStart) {
          reservedQuantity += rental.quantity || 0
        }
      })
      
      const availableQuantity = totalStock - reservedQuantity
      return availableQuantity >= quantity
    } catch (err) {
      console.error('Error checking availability:', err)
      return false
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    rentals: readonly(rentals),
    userRentals: readonly(userRentals),
    loading: readonly(loading),
    error: readonly(error),
    
    // Getters
    pendingRentals,
    activeRentals,
    overdueRentals,
    userActiveRentals,
    userPendingRentals,
    
    // Actions
    initializeRealTimeListeners,
    stopRealTimeListeners,
    createRental,
    updateRentalStatus,
    fetchUserRentals,
    checkItemAvailability,
    clearError
  }
})
