# 🔥 Firebase Setup Guide for TKOH NSD Renting Platform

## Step 1: Get Your Firebase Configuration

1. **Go to your Firebase Console**: https://console.firebase.google.com/
2. **Select your project**: `tkoh-nsd-renting-platform`
3. **Click the gear icon** → **Project settings**
4. **Scroll down to "Your apps"** section
5. **Click on your web app** or create one if it doesn't exist
6. **Copy the Firebase configuration object**

It should look like this:
```javascript
const firebaseConfig = {
  apiKey: "AIzaSyC...",
  authDomain: "tkoh-nsd-renting-platform.firebaseapp.com",
  projectId: "tkoh-nsd-renting-platform",
  storageBucket: "tkoh-nsd-renting-platform.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef123456"
};
```

## Step 2: Update Your .env File

Replace the values in your `.env` file with your actual Firebase configuration:

```env
VITE_FIREBASE_API_KEY=AIzaSyC...
VITE_FIREBASE_AUTH_DOMAIN=tkoh-nsd-renting-platform.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=tkoh-nsd-renting-platform
VITE_FIREBASE_STORAGE_BUCKET=tkoh-nsd-renting-platform.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef123456
```

## Step 3: Restart Your Development Server

After updating the `.env` file:
```bash
# Stop the current server (Ctrl+C)
# Then restart it
npm run dev
```

## Step 4: Initialize the Database

Once Firebase is properly configured:

### Option A: Use the Admin Panel
1. Go to http://localhost:5173/admin
2. Click the **"Initialize Database"** button
3. This will create all categories and items in Firebase

### Option B: Use the Demo Database Creator
1. Go to http://localhost:5173/create-demo-database.html
2. Click **"Create Database Structure"**
3. This works for both demo and real Firebase

## Step 5: Verify Database Creation

1. **Go back to Firebase Console**
2. **Click "Firestore Database"** in the left sidebar
3. **You should see two collections**:
   - `categories` (3 documents)
   - `item_groups` (6 documents)

## Database Structure That Will Be Created

### Categories Collection:
- `resuscitation-training` - Resuscitation Training Equipment
- `training-venue` - Training Venue  
- `audio-visual` - Audio Visual Equipment

### Item Groups Collection:
- `manikin` - Training manikins (2 items)
- `sim-train-equipment` - Simulation equipment (1 item)
- `rtrc` - Training venue (1 item)
- `smv-tv` - Smart TV (1 item)
- `mobile-phone-stabilizer` - Stabilizers (2 items)
- `digital-camera` - Camera (1 item)

**Total: 9 individual rental items across 6 equipment groups**

## Troubleshooting

### If you see "Demo mode" messages:
- Check that your `.env` file has the correct Firebase configuration
- Restart the development server after updating `.env`
- Make sure there are no typos in the environment variable names

### If the database initialization fails:
- Check the browser console for error messages
- Verify your Firebase project has Firestore enabled
- Make sure your Firebase rules allow read/write access

### Firebase Security Rules
Make sure your Firestore rules allow read/write access during development:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if true; // For development only
    }
  }
}
```

## Next Steps After Setup

1. **Test the booking flow**: Go to "New Booking" and try booking equipment
2. **Browse items**: Check that all equipment appears in "Browse Items"
3. **Admin management**: Use the admin panel to add/edit items
4. **Check Firebase**: Verify data appears in your Firebase console

---

**Need help?** Check the browser console for any error messages and make sure all Firebase configuration values are correct.
