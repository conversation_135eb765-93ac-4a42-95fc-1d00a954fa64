#!/usr/bin/env node

/**
 * Database Creation Script for TKOH NSD Renting Platform
 * 
 * This script creates the Firebase database structure with all required
 * categories and items for the rental platform.
 * 
 * Usage:
 * 1. Set up your Firebase project
 * 2. Create a .env file with your Firebase configuration
 * 3. Run: node scripts/create-database.js
 */

import { initializeApp } from 'firebase/app'
import { getFirestore, collection, doc, setDoc, getDocs } from 'firebase/firestore'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

// Database structure
const databaseStructure = {
  categories: [
    {
      id: 'resuscitation-training',
      name: 'Resuscitation Training Equipment',
      type: 'resus_trainings',
      description: 'Equipment for resuscitation and emergency response training',
      icon: 'pi pi-heart',
      order: 1
    },
    {
      id: 'training-venue',
      name: 'Training Venue',
      type: 'venues',
      description: 'Training venues and meeting rooms',
      icon: 'pi pi-building',
      order: 2
    },
    {
      id: 'audio-visual',
      name: 'Audio Visual Equipment',
      type: 'audio_visuals',
      description: 'Audio visual equipment for presentations and training',
      icon: 'pi pi-video',
      order: 3
    }
  ],
  itemGroups: [
    // Resuscitation Training Equipment
    {
      id: 'manikin',
      name: 'Manikin',
      category_id: 'resuscitation-training',
      type: 'resus_trainings',
      description: 'Training manikin for CPR and resuscitation practice',
      image_url: '',
      items: [
        {
          id: 'manikin-001',
          serial_number: 'MAN-001',
          condition: 'excellent',
          notes: 'Adult CPR training manikin',
          status: 'available'
        },
        {
          id: 'manikin-002',
          serial_number: 'MAN-002',
          condition: 'good',
          notes: 'Adult CPR training manikin',
          status: 'available'
        }
      ],
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 'sim-train-equipment',
      name: 'Sim Train Equipment',
      category_id: 'resuscitation-training',
      type: 'resus_trainings',
      description: 'Simulation training equipment for emergency scenarios',
      image_url: '',
      items: [
        {
          id: 'sim-001',
          serial_number: 'SIM-001',
          condition: 'excellent',
          notes: 'Complete simulation training kit',
          status: 'available'
        }
      ],
      created_at: new Date(),
      updated_at: new Date()
    },
    // Training Venue
    {
      id: 'rtrc',
      name: 'RTRC',
      category_id: 'training-venue',
      type: 'venues',
      description: 'Resuscitation Training Resource Centre - Main training venue',
      image_url: '',
      items: [
        {
          id: 'rtrc-001',
          serial_number: 'VENUE-001',
          condition: 'excellent',
          notes: 'Main training room with full equipment',
          status: 'available'
        }
      ],
      created_at: new Date(),
      updated_at: new Date()
    },
    // Audio Visual Equipment
    {
      id: 'smv-tv',
      name: 'SMV TV',
      category_id: 'audio-visual',
      type: 'audio_visuals',
      description: 'Smart TV for presentations and training videos',
      image_url: '',
      items: [
        {
          id: 'tv-001',
          serial_number: 'TV-001',
          condition: 'excellent',
          notes: '55-inch Smart TV with wireless connectivity',
          status: 'available'
        }
      ],
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 'mobile-phone-stabilizer',
      name: 'Mobile Phone Stabilizer',
      category_id: 'audio-visual',
      type: 'audio_visuals',
      description: 'Gimbal stabilizer for mobile phone video recording',
      image_url: '',
      items: [
        {
          id: 'stabilizer-001',
          serial_number: 'STAB-001',
          condition: 'excellent',
          notes: '3-axis gimbal stabilizer',
          status: 'available'
        },
        {
          id: 'stabilizer-002',
          serial_number: 'STAB-002',
          condition: 'good',
          notes: '3-axis gimbal stabilizer',
          status: 'available'
        }
      ],
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 'digital-camera',
      name: 'Digital Camera',
      category_id: 'audio-visual',
      type: 'audio_visuals',
      description: 'Professional digital camera for training documentation',
      image_url: '',
      items: [
        {
          id: 'camera-001',
          serial_number: 'CAM-001',
          condition: 'excellent',
          notes: 'DSLR camera with lens kit',
          status: 'available'
        }
      ],
      created_at: new Date(),
      updated_at: new Date()
    }
  ]
}

async function createDatabase() {
  console.log('🚀 Starting Firebase database creation...\n')

  // Check if Firebase is configured
  if (!process.env.VITE_FIREBASE_API_KEY || process.env.VITE_FIREBASE_API_KEY === 'your_api_key_here') {
    console.log('❌ Firebase not configured!')
    console.log('Please create a .env file with your Firebase configuration.')
    console.log('Use .env.example as a template.\n')
    return
  }

  try {
    // Initialize Firebase
    const firebaseConfig = {
      apiKey: process.env.VITE_FIREBASE_API_KEY,
      authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
      projectId: process.env.VITE_FIREBASE_PROJECT_ID,
      storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
      messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
      appId: process.env.VITE_FIREBASE_APP_ID
    }

    const app = initializeApp(firebaseConfig)
    const db = getFirestore(app)

    console.log('✅ Connected to Firebase project:', firebaseConfig.projectId)

    // Create categories
    console.log('\n📁 Creating categories...')
    for (const category of databaseStructure.categories) {
      await setDoc(doc(db, 'categories', category.id), category)
      console.log(`  ✅ Created category: ${category.name}`)
    }

    // Create item groups
    console.log('\n📦 Creating item groups...')
    for (const itemGroup of databaseStructure.itemGroups) {
      await setDoc(doc(db, 'item_groups', itemGroup.id), itemGroup)
      console.log(`  ✅ Created item group: ${itemGroup.name} (${itemGroup.items.length} items)`)
    }

    console.log('\n🎉 Database creation completed successfully!')
    console.log('\nCreated:')
    console.log(`  📁 ${databaseStructure.categories.length} categories`)
    console.log(`  📦 ${databaseStructure.itemGroups.length} item groups`)
    console.log(`  🔧 ${databaseStructure.itemGroups.reduce((total, group) => total + group.items.length, 0)} individual items`)

  } catch (error) {
    console.error('❌ Error creating database:', error)
    process.exit(1)
  }
}

// Run the script
createDatabase()
