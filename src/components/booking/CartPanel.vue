<template>
  <div class="cart-panel">
    <div class="cart-header">
      <h3>
        <i class="pi pi-shopping-cart"></i>
        Cart ({{ cartItems.length }})
      </h3>
      <Button
        v-if="cartItems.length > 0"
        label="Clear All"
        icon="pi pi-trash"
        class="p-button-text p-button-sm"
        @click="clearCart"
        :disabled="loading"
      />
    </div>

    <div class="cart-content">
      <!-- Empty Cart State -->
      <div v-if="cartItems.length === 0" class="empty-cart">
        <i class="pi pi-shopping-cart" style="font-size: 3rem; color: var(--text-color-secondary);"></i>
        <p>Your cart is empty</p>
        <small>Add items from the catalog to get started</small>
      </div>

      <!-- Cart Items -->
      <div v-else class="cart-items">
        <div
          v-for="item in cartItems"
          :key="item.item_group_id"
          class="cart-item"
        >
          <div class="item-info">
            <h4 class="item-name">{{ item.item_group_name }}</h4>
            <p class="item-type">{{ formatItemType(item.item_group_type) }}</p>
            
            <!-- Contraindications Warning -->
            <div v-if="item.contraindications.length > 0" class="contraindications-alert">
              <i class="pi pi-exclamation-triangle"></i>
              <span>{{ item.contraindications.length }} contraindication(s)</span>
            </div>
          </div>

          <div class="item-controls">
            <div class="quantity-control">
              <Button
                icon="pi pi-minus"
                class="p-button-sm p-button-outlined"
                @click="decreaseQuantity(item.item_group_id)"
                :disabled="loading || item.quantity <= 1"
              />
              <span class="quantity-display">{{ item.quantity }}</span>
              <Button
                icon="pi pi-plus"
                class="p-button-sm p-button-outlined"
                @click="increaseQuantity(item.item_group_id)"
                :disabled="loading || item.quantity >= item.max_quantity"
              />
            </div>
            
            <Button
              icon="pi pi-times"
              class="p-button-text p-button-sm remove-btn"
              @click="removeItem(item.item_group_id)"
              :disabled="loading"
              v-tooltip="'Remove from cart'"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Cart Summary -->
    <div v-if="cartItems.length > 0" class="cart-summary">
      <div class="summary-row">
        <span>Total Items:</span>
        <span class="summary-value">{{ totalItems }}</span>
      </div>
      
      <div class="summary-row">
        <span>Selected Dates:</span>
        <span class="summary-value">{{ selectedDates.length }} day(s)</span>
      </div>

      <!-- Date Range Display -->
      <div class="selected-dates-summary">
        <h4>Rental Dates:</h4>
        <div class="dates-display">
          <Tag
            v-for="date in formattedDates"
            :key="date"
            :value="date"
            severity="info"
            class="date-tag"
          />
        </div>
      </div>

      <!-- Contraindications Summary -->
      <div v-if="hasContraindications" class="contraindications-summary">
        <h4 class="warning-title">
          <i class="pi pi-exclamation-triangle"></i>
          Contraindications Detected
        </h4>
        <p class="warning-text">
          Some items in your cart have contraindications. Please review before submitting.
        </p>
        <Button
          label="Review Conflicts"
          class="p-button-text p-button-sm"
          @click="showContraindications = true"
        />
      </div>
    </div>

    <!-- Submit Button -->
    <div v-if="cartItems.length > 0" class="cart-actions">
      <Button
        label="Submit Booking"
        icon="pi pi-check"
        class="submit-btn"
        @click="submitCart"
        :loading="loading"
        :disabled="selectedDates.length === 0"
      />
      
      <small v-if="selectedDates.length === 0" class="submit-note">
        Please select dates to submit booking
      </small>
    </div>

    <!-- Contraindications Dialog -->
    <Dialog
      v-model:visible="showContraindications"
      header="Contraindications Review"
      modal
      style="width: 50vw; min-width: 400px;"
    >
      <div class="contraindications-review">
        <p>The following items in your cart have contraindications:</p>
        
        <div
          v-for="item in cartItemsWithContraindications"
          :key="item.item_group_id"
          class="contraindication-item"
        >
          <h4>{{ item.item_group_name }}</h4>
          <ul>
            <li v-for="contra in item.contraindications" :key="contra.item_group_name">
              <strong>{{ contra.item_group_name }}:</strong> {{ contra.reason }}
            </li>
          </ul>
        </div>
        
        <p class="warning-note">
          <i class="pi pi-info-circle"></i>
          You can still proceed with the booking, but please ensure these contraindications are acceptable for your use case.
        </p>
      </div>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { format, parseISO } from 'date-fns'
import type { CartItem } from '@/stores/episodes'

interface Props {
  cartItems: CartItem[]
  selectedDates: string[]
  loading?: boolean
}

interface Emits {
  (e: 'update-quantity', itemGroupId: string, quantity: number): void
  (e: 'remove-item', itemGroupId: string): void
  (e: 'submit-cart'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// Reactive state
const showContraindications = ref(false)

// Computed properties
const totalItems = computed(() => {
  return props.cartItems.reduce((total, item) => total + item.quantity, 0)
})

const formattedDates = computed(() => {
  return props.selectedDates
    .sort()
    .map(dateStr => format(parseISO(dateStr), 'MMM dd'))
})

const hasContraindications = computed(() => {
  return props.cartItems.some(item => item.contraindications.length > 0)
})

const cartItemsWithContraindications = computed(() => {
  return props.cartItems.filter(item => item.contraindications.length > 0)
})

// Methods
const formatItemType = (type: string) => {
  switch (type) {
    case 'resus_trainings':
      return 'Resuscitating Training'
    case 'venues':
      return 'Venue'
    case 'audio_visuals':
      return 'Audio Visual'
    default:
      return type
  }
}

const increaseQuantity = (itemGroupId: string) => {
  const item = props.cartItems.find(item => item.item_group_id === itemGroupId)
  if (item && item.quantity < item.max_quantity) {
    emit('update-quantity', itemGroupId, item.quantity + 1)
  }
}

const decreaseQuantity = (itemGroupId: string) => {
  const item = props.cartItems.find(item => item.item_group_id === itemGroupId)
  if (item && item.quantity > 1) {
    emit('update-quantity', itemGroupId, item.quantity - 1)
  }
}

const removeItem = (itemGroupId: string) => {
  emit('remove-item', itemGroupId)
}

const clearCart = () => {
  props.cartItems.forEach(item => {
    emit('remove-item', item.item_group_id)
  })
}

const submitCart = () => {
  emit('submit-cart')
}
</script>

<style scoped>
.cart-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.cart-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color);
  font-weight: 600;
}

.cart-content {
  flex: 1;
  overflow-y: auto;
}

.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
  color: var(--text-color-secondary);
}

.empty-cart p {
  margin: 1rem 0 0.5rem 0;
  font-size: 1.1rem;
}

.cart-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.cart-item {
  padding: 1rem;
  border: 1px solid var(--surface-border);
  border-radius: 8px;
  background: var(--surface-50);
}

.item-info {
  margin-bottom: 1rem;
}

.item-name {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
}

.item-type {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--text-color-secondary);
}

.contraindications-alert {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: var(--yellow-50);
  border: 1px solid var(--yellow-200);
  border-radius: 4px;
  font-size: 0.85rem;
  color: var(--yellow-800);
}

.item-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quantity-display {
  min-width: 2rem;
  text-align: center;
  font-weight: 600;
  color: var(--text-color);
}

.remove-btn {
  padding: 0.25rem;
  width: 1.75rem;
  height: 1.75rem;
  min-width: unset;
}

.cart-summary {
  margin: 1rem 0;
  padding: 1rem;
  background: var(--surface-100);
  border-radius: 8px;
  border: 1px solid var(--surface-border);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.summary-value {
  font-weight: 600;
  color: var(--text-color);
}

.selected-dates-summary {
  margin-top: 1rem;
}

.selected-dates-summary h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--text-color);
}

.dates-display {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  align-items: center;
}

.date-tag {
  font-size: 0.8rem;
}

.contraindications-summary {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--yellow-50);
  border: 1px solid var(--yellow-200);
  border-radius: 6px;
}

.warning-title {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--yellow-800);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.warning-text {
  margin: 0 0 0.75rem 0;
  font-size: 0.85rem;
  color: var(--yellow-700);
  line-height: 1.4;
}

.cart-actions {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--surface-border);
}

.submit-btn {
  width: 100%;
  margin-bottom: 0.5rem;
}

.submit-note {
  display: block;
  text-align: center;
  color: var(--text-color-secondary);
  font-style: italic;
}

.contraindications-review p {
  margin-bottom: 1rem;
  color: var(--text-color-secondary);
}

.contraindication-item {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--surface-50);
  border-radius: 6px;
  border: 1px solid var(--surface-border);
}

.contraindication-item h4 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 1rem;
}

.contraindication-item ul {
  margin: 0;
  padding-left: 1.5rem;
}

.contraindication-item li {
  margin-bottom: 0.25rem;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.warning-note {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--blue-50);
  border: 1px solid var(--blue-200);
  border-radius: 6px;
  color: var(--blue-800);
  font-size: 0.9rem;
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}
</style>
