rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null && request.auth.token.admin == true;
    }

    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Helper function to check if user owns the document (by employee number)
    function isOwner(employeeNumber) {
      return request.auth != null && request.auth.token.employee_number == employeeNumber;
    }

    // Users collection - users can read/write their own data (keyed by employee number)
    match /users/{employeeNumber} {
      allow read, write: if isAuthenticated() &&
        (isOwner(employeeNumber) || isAdmin());

      // User episodes subcollection
      match /episodes/{episodeId} {
        allow read, write: if isAuthenticated() &&
          (isOwner(employeeNumber) || isAdmin());
      }
    }

    // Items collection with new structure
    match /items/{itemType} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();

      // Item groups subcollection
      match /groups/{groupId} {
        allow read: if isAuthenticated();
        allow write: if isAdmin();
      }
    }

    // Categories collection - read for authenticated users, write for admin
    match /categories/{categoryId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // Settings collection - admin only
    match /settings/{settingId} {
      allow read, write: if isAdmin();
    }

    // Admin collection - admin only
    match /admin/{document} {
      allow read, write: if isAdmin();
    }
  }
}
