import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import {
  signInWithCustomToken,
  signOut,
  onAuthStateChanged,
  type User
} from 'firebase/auth'
import { doc, getDoc, setDoc, updateDoc, collection, getDocs, query, where } from 'firebase/firestore'
import { auth, db, isDemoMode } from '@/firebase/config'
import { useUsersStore, type UserRole } from '@/stores/users'

export interface UserProfile {
  uid: string
  employee_number: string
  email: string
  displayName: string
  department?: string
  unit?: string
  role: UserRole
  episodes: Record<string, {
    id: string
    dates: string[]
    items: Array<{
      item_group_id: string
      item_group_name: string
      item_group_type: 'resus_trainings' | 'venues' | 'audio_visuals'
      individual_item_id: string
      quantity: number
    }>
    status: 'cancelled' | 'scheduled' | 'rented' | 'returned'
  }>
  createdAt: Date
  lastLogin: Date
}

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const userProfile = ref<UserProfile | null>(null)
  const loading = ref(false)
  const initialized = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isAuthenticated = computed(() => !!user.value)
  const isAdmin = computed(() => userProfile.value?.role === 'Admin')
  const isPersonal = computed(() => userProfile.value?.role === 'Personal')
  const isUnit = computed(() => userProfile.value?.role === 'Unit')
  const currentUser = computed(() => user.value)
  const currentUserProfile = computed(() => userProfile.value)

  // Actions
  const initializeAuth = async () => {
    return new Promise<void>((resolve) => {
      if (isDemoMode) {
        // Demo mode - no Firebase auth state to listen to
        initialized.value = true
        resolve()
        return
      }

      const unsubscribe = onAuthStateChanged(auth!, async (firebaseUser) => {
        if (firebaseUser) {
          user.value = firebaseUser
          await loadUserProfile(firebaseUser.uid)
        } else {
          user.value = null
          userProfile.value = null
        }
        initialized.value = true
        unsubscribe()
        resolve()
      })
    })
  }

  const loginWithExternalAPI = async (credentials: { username: string; password: string }) => {
    loading.value = true
    error.value = null

    try {
      if (isDemoMode) {
        // Demo mode - simulate successful login
        console.log('Demo mode: Simulating login for employee:', credentials.username)

        // Create a mock user object
        const mockUser = {
          uid: 'demo-user-uid',
          email: `${credentials.username}@demo.com`,
          displayName: `Demo User ${credentials.username}`,
          emailVerified: true
        } as User

        // Create a mock user profile
        const mockProfile: UserProfile = {
          uid: 'demo-user-uid',
          employee_number: credentials.username,
          email: `${credentials.username}@demo.com`,
          displayName: `Demo User ${credentials.username}`,
          department: 'Demo Department',
          role: credentials.username === 'admin' ? 'Admin' : 'Personal',
          episodes: {},
          createdAt: new Date(),
          lastLogin: new Date()
        }

        // Set both user and userProfile for demo mode
        user.value = mockUser
        userProfile.value = mockProfile
        return
      }

      // Check for admin bypass (temporary solution)
      if (credentials.username === 'admin' && credentials.password === 'admin123') {
        console.log('Admin bypass login')

        // Create a mock user object for admin
        const adminUser = {
          uid: 'admin-user-uid',
          email: '<EMAIL>',
          displayName: 'Administrator',
          emailVerified: true
        } as User

        // Create admin user profile
        const adminProfile: UserProfile = {
          uid: 'admin-user-uid',
          employee_number: 'admin',
          email: '<EMAIL>',
          displayName: 'Administrator',
          department: 'IT Department',
          role: 'Admin',
          episodes: {},
          createdAt: new Date(),
          lastLogin: new Date()
        }

        // Create the admin user document in Firebase
        await createOrUpdateUserProfile(adminProfile)

        // Set both user and userProfile for admin bypass
        user.value = adminUser
        userProfile.value = adminProfile
        return
      }

      // Check user_list collection for authentication
      const usersStore = useUsersStore()
      await usersStore.fetchUsers()

      const userAccount = usersStore.getUserByEmployeeNumber(credentials.username)

      if (userAccount) {
        console.log('User found in user_list collection:', userAccount)

        // For now, we'll use a simple password check (in production, use proper authentication)
        // This is a temporary solution for testing
        const isValidPassword = credentials.password === 'password123' ||
                               (userAccount.role === 'Admin' && credentials.password === 'admin123')

        if (isValidPassword) {
          // Create a mock user object
          const mockUser = {
            uid: `user-${userAccount.id}`,
            email: userAccount.email,
            displayName: userAccount.displayName,
            emailVerified: true
          } as User

          // Create user profile from user_list data
          const userProfile: UserProfile = {
            uid: `user-${userAccount.id}`,
            employee_number: userAccount.employee_number,
            email: userAccount.email,
            displayName: userAccount.displayName,
            department: userAccount.department,
            role: userAccount.role,
            episodes: {},
            createdAt: userAccount.createdAt,
            lastLogin: new Date()
          }

          // Update last login in user_list
          await usersStore.updateLastLogin(userAccount.employee_number)

          // Create or update user profile in users collection
          await createOrUpdateUserProfile(userProfile)

          // Set authentication state
          user.value = mockUser
          userProfile.value = userProfile
          return
        } else {
          throw new Error('Invalid password')
        }
      }

      // Call external API for authentication
      const response = await fetch(import.meta.env.VITE_EXTERNAL_AUTH_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      })

      if (!response.ok) {
        throw new Error('Invalid credentials')
      }

      const data = await response.json()

      // Assuming the external API returns a custom token for Firebase
      if (data.customToken) {
        const userCredential = await signInWithCustomToken(auth!, data.customToken)
        user.value = userCredential.user

        // Create or update user profile
        await createOrUpdateUserProfile({
          uid: userCredential.user.uid,
          employee_number: data.employee_number || '',
          email: data.email || userCredential.user.email || '',
          displayName: data.displayName || userCredential.user.displayName || '',
          department: data.department,
          role: data.role || 'Personal',
          episodes: {},
          createdAt: new Date(),
          lastLogin: new Date()
        })

        await loadUserProfile(userCredential.user.uid)
      } else {
        throw new Error('Authentication failed')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Login failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    loading.value = true
    try {
      if (isDemoMode) {
        // Demo mode - just clear the user data
        user.value = null
        userProfile.value = null
        return
      }

      await signOut(auth!)
      user.value = null
      userProfile.value = null
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Logout failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  const loadUserProfile = async (uid: string) => {
    if (isDemoMode) {
      // Demo mode - profile already set in login
      return
    }

    try {
      // First, find the user by UID to get their employee number
      const usersQuery = query(collection(db!, 'users'), where('uid', '==', uid))
      const usersSnapshot = await getDocs(usersQuery)

      if (!usersSnapshot.empty) {
        const userDoc = usersSnapshot.docs[0]
        const data = userDoc.data()
        const employeeNumber = userDoc.id // Document ID is the employee number

        userProfile.value = {
          uid,
          employee_number: employeeNumber,
          email: data.email,
          displayName: data.displayName,
          department: data.department,
          role: data.role || 'user',
          episodes: data.episodes || {},
          createdAt: data.createdAt?.toDate() || new Date(),
          lastLogin: data.lastLogin?.toDate() || new Date()
        }

        // Update episode statuses based on current date
        await updateEpisodeStatuses(employeeNumber)
      }
    } catch (err) {
      console.error('Error loading user profile:', err)
    }
  }

  const createOrUpdateUserProfile = async (profile: UserProfile) => {
    if (isDemoMode) {
      // Demo mode - just store in memory
      userProfile.value = profile
      return
    }

    try {
      // Use employee number as document ID
      await setDoc(doc(db!, 'users', profile.employee_number), {
        uid: profile.uid,
        email: profile.email,
        displayName: profile.displayName,
        department: profile.department,
        role: profile.role,
        episodes: profile.episodes || {},
        createdAt: profile.createdAt,
        lastLogin: profile.lastLogin
      }, { merge: true })
    } catch (err) {
      console.error('Error creating/updating user profile:', err)
      throw err
    }
  }

  const updateEpisodeStatuses = async (employeeNumber: string) => {
    if (isDemoMode) {
      // Demo mode - no episode status updates needed
      return
    }

    try {
      const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD format
      const userDocRef = doc(db!, 'users', employeeNumber)
      const userDoc = await getDoc(userDocRef)

      if (!userDoc.exists()) return

      const userData = userDoc.data()
      const episodes = userData.episodes || {}
      let hasUpdates = false

      for (const episodeId in episodes) {
        const episode = episodes[episodeId]
        const episodeDates = episode.dates || []
        const latestDate = episodeDates.sort().pop()

        // Check if today's date is in the episode dates and status is scheduled
        if (episodeDates.includes(today) && episode.status === 'scheduled') {
          episode.status = 'rented'
          hasUpdates = true

          // Update individual items status
          for (const item of episode.items) {
            await updateIndividualItemStatus(item.item_group_type, item.item_group_id, item.individual_item_id, episodeId, 'rented')
          }
        }

        // Check if episode is finished (latest date is before today and status is rented)
        if (latestDate && latestDate < today && episode.status === 'rented') {
          episode.status = 'returned'
          hasUpdates = true

          // Update individual items status
          for (const item of episode.items) {
            await updateIndividualItemStatus(item.item_group_type, item.item_group_id, item.individual_item_id, episodeId, 'returned')
          }
        }
      }

      if (hasUpdates) {
        await updateDoc(userDocRef, { episodes })
      }
    } catch (err) {
      console.error('Error updating episode statuses:', err)
    }
  }

  const updateIndividualItemStatus = async (
    type: string,
    groupId: string,
    itemId: string,
    episodeId: string,
    status: 'scheduled' | 'rented' | 'returned' | 'cancelled'
  ) => {
    if (isDemoMode) {
      // Demo mode - no individual item status updates needed
      return
    }

    try {
      const itemGroupRef = doc(db!, `items/${type}/groups`, groupId)
      const itemGroupDoc = await getDoc(itemGroupRef)

      if (itemGroupDoc.exists()) {
        const groupData = itemGroupDoc.data()
        const items = groupData.items || []

        const itemIndex = items.findIndex((item: any) => item.id === itemId)
        if (itemIndex >= 0 && items[itemIndex].episodes[episodeId]) {
          items[itemIndex].episodes[episodeId].status = status
          await updateDoc(itemGroupRef, { items })
        }
      }
    } catch (err) {
      console.error('Error updating individual item status:', err)
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    user: readonly(user),
    userProfile: readonly(userProfile),
    loading: readonly(loading),
    initialized: readonly(initialized),
    error: readonly(error),
    
    // Getters
    isAuthenticated,
    isAdmin,
    isPersonal,
    isUnit,
    currentUser,
    currentUserProfile,
    
    // Actions
    initializeAuth,
    loginWithExternalAPI,
    logout,
    loadUserProfile,
    createOrUpdateUserProfile,
    updateEpisodeStatuses,
    updateIndividualItemStatus,
    clearError
  }
})
