/* SAFE PrimeVue Overlay Fixes - Only target overlay components that appear on top */

/* DIALOGS - Only target actual dialog overlays */
.p-dialog.p-component {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
  border: 1px solid #ddd !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.p-dialog .p-dialog-content {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

.p-dialog .p-dialog-header {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
  border-bottom: 1px solid #eee !important;
}

.p-dialog .p-dialog-footer {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
  border-top: 1px solid #eee !important;
}

/* DROPDOWN PANELS - Only target the floating panels */
.p-dropdown-panel.p-component {
  background: white !important;
  background-color: white !important;
  border: 1px solid #ddd !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.p-dropdown-items {
  background: white !important;
  background-color: white !important;
}

.p-dropdown-item {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

.p-dropdown-item:hover {
  background: #f5f5f5 !important;
  background-color: #f5f5f5 !important;
  color: #333 !important;
}

/* CALENDAR PANELS - Only target the floating calendar */
.p-calendar-panel.p-component {
  background: white !important;
  background-color: white !important;
  border: 1px solid #ddd !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.p-datepicker {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

.p-datepicker-header {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

.p-datepicker-calendar td {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

.p-datepicker-calendar td:hover {
  background: #f5f5f5 !important;
  background-color: #f5f5f5 !important;
}

/* MULTISELECT PANELS */
.p-multiselect-panel.p-component {
  background: white !important;
  background-color: white !important;
  border: 1px solid #ddd !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.p-multiselect-items {
  background: white !important;
  background-color: white !important;
}

.p-multiselect-item {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

/* TOAST MESSAGES */
.p-toast .p-toast-message {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
  border: 1px solid #ddd !important;
}

/* DIALOG BUTTONS - Improve visibility and styling */
.p-dialog .p-button {
  font-weight: 600 !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

.p-dialog .p-button.p-button-outlined {
  background: white !important;
  border: 2px solid #6366f1 !important;
  color: #6366f1 !important;
}

.p-dialog .p-button.p-button-outlined:hover {
  background: #6366f1 !important;
  color: white !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3) !important;
}

.p-dialog .p-button.p-button-outlined.p-button-secondary {
  border-color: #6b7280 !important;
  color: #6b7280 !important;
}

.p-dialog .p-button.p-button-outlined.p-button-secondary:hover {
  background: #6b7280 !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3) !important;
}

.p-dialog .p-button[severity="danger"] {
  background: #ef4444 !important;
  border-color: #ef4444 !important;
  color: white !important;
}

.p-dialog .p-button[severity="danger"]:hover {
  background: #dc2626 !important;
  border-color: #dc2626 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3) !important;
}

.p-dialog .p-button[severity="warning"] {
  background: #f59e0b !important;
  border-color: #f59e0b !important;
  color: white !important;
}

.p-dialog .p-button[severity="warning"]:hover {
  background: #d97706 !important;
  border-color: #d97706 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3) !important;
}

/* CONFIRM DIALOG SPECIFIC STYLING */
.p-confirm-dialog .p-dialog-footer {
  padding: 1.5rem !important;
  gap: 1rem !important;
  display: flex !important;
  justify-content: flex-end !important;
}

.p-confirm-dialog .p-button {
  min-width: 120px !important;
}

/* Z-INDEX MANAGEMENT */
.p-dialog.p-component {
  z-index: 10001 !important;
}

.p-dropdown-panel.p-component,
.p-multiselect-panel.p-component,
.p-calendar-panel.p-component {
  z-index: 10002 !important;
}
