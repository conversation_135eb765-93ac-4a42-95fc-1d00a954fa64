<template>
  <Card class="item-card" :class="{ 'item-unavailable': !isAvailable }">
    <template #header>
      <div class="item-image-container">
        <img 
          :src="itemGroup.imageUrl || '/images/placeholder-item.jpg'" 
          :alt="itemGroup.name"
          class="item-image"
          @error="onImageError"
        />
        <div class="availability-badge">
          <Badge 
            :value="availabilityText" 
            :severity="availabilityBadgeSeverity"
          />
        </div>
      </div>
    </template>

    <template #title>
      <div class="item-title">
        {{ itemGroup.name }}
        <Button
          icon="pi pi-info-circle"
          class="p-button-text p-button-sm info-btn"
          @click="showDetails = true"
          v-tooltip="'View details'"
        />
      </div>
    </template>

    <template #subtitle>
      <div class="item-category">
        {{ getCategoryName(itemGroup.category) }}
      </div>
    </template>

    <template #content>
      <div class="item-description">
        {{ itemGroup.description }}
      </div>

      <div class="item-stats">
        <div class="stat-item">
          <span class="stat-label">Available:</span>
          <span class="stat-value">{{ availableQuantity }} / {{ totalQuantity }}</span>
        </div>
        <div class="stat-item" v-if="itemGroup.items && itemGroup.items.length > 0">
          <span class="stat-label">Total Use:</span>
          <span class="stat-value">{{ totalUseCount }}</span>
        </div>
      </div>

      <!-- Contraindications Warning -->
      <div v-if="hasContraindications" class="contraindications-warning">
        <i class="pi pi-exclamation-triangle"></i>
        <span>Has contraindications</span>
        <Button
          label="View"
          class="p-button-text p-button-sm"
          @click="showContraindications = true"
        />
      </div>
    </template>

    <template #footer>
      <div class="item-actions">
        <div class="quantity-selector" v-if="isAvailable">
          <label for="quantity">Quantity:</label>
          <InputNumber
            id="quantity"
            v-model="selectedQuantity"
            :min="1"
            :max="availableQuantity"
            :disabled="!isAvailable"
            show-buttons
            button-layout="horizontal"
            size="small"
          />
        </div>
        
        <Button
          :label="isAvailable ? 'Add to Cart' : 'Unavailable'"
          :disabled="!isAvailable || selectedQuantity === 0"
          @click="addToCart"
          :loading="adding"
          class="add-to-cart-btn"
        />
      </div>
    </template>
  </Card>

  <!-- Item Details Dialog -->
  <Dialog
    v-model:visible="showDetails"
    :header="itemGroup.name"
    modal
    class="item-details-dialog"
    style="width: 50vw; min-width: 400px;"
  >
    <div class="item-details-content">
      <div class="detail-image">
        <img 
          :src="itemGroup.imageUrl || '/images/placeholder-item.jpg'" 
          :alt="itemGroup.name"
          @error="onImageError"
        />
      </div>
      
      <div class="detail-info">
        <h4>Description</h4>
        <p>{{ itemGroup.description }}</p>
        
        <h4>Category</h4>
        <p>{{ getCategoryName(itemGroup.category) }}</p>
        
        <h4>Individual Items</h4>
        <DataTable 
          :value="itemGroup.items || []" 
          size="small"
          class="items-table"
        >
          <Column field="id" header="Item ID" />
          <Column field="remarks" header="Condition" />
          <Column field="use_count" header="Use Count" />
          <Column header="Status">
            <template #body="{ data }">
              <Badge 
                :value="getItemStatus(data)" 
                :severity="getItemStatusSeverity(data)"
              />
            </template>
          </Column>
        </DataTable>
        
        <div v-if="hasContraindications">
          <h4>Contraindications</h4>
          <ul class="contraindications-list">
            <li v-for="contra in itemGroup.contraindications" :key="contra.item_group_name">
              <strong>{{ contra.item_group_name }}:</strong> {{ contra.reason }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </Dialog>

  <!-- Contraindications Dialog -->
  <Dialog
    v-model:visible="showContraindications"
    header="Contraindications"
    modal
    style="width: 40vw; min-width: 300px;"
  >
    <div class="contraindications-content">
      <p>This item has contraindications with the following items:</p>
      <ul class="contraindications-list">
        <li v-for="contra in itemGroup.contraindications" :key="contra.item_group_name">
          <strong>{{ contra.item_group_name }}:</strong> {{ contra.reason }}
        </li>
      </ul>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useItemsStore } from '@/stores/items'
import type { ItemGroup } from '@/stores/items'
import type { CartItem } from '@/stores/episodes'

interface Props {
  itemGroup: ItemGroup
  availableQuantity: number
  isAvailable: boolean
}

interface Emits {
  (e: 'add-to-cart', cartItem: CartItem): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const itemsStore = useItemsStore()

// Reactive state
const selectedQuantity = ref(1)
const adding = ref(false)
const showDetails = ref(false)
const showContraindications = ref(false)

// Computed properties
const totalQuantity = computed(() => {
  return props.itemGroup.items?.length || 0
})

const totalUseCount = computed(() => {
  return props.itemGroup.items?.reduce((total, item) => total + (item.use_count || 0), 0) || 0
})

const hasContraindications = computed(() => {
  return props.itemGroup.contraindications && props.itemGroup.contraindications.length > 0
})

const availabilityText = computed(() => {
  if (!props.isAvailable) return 'Unavailable'
  if (props.availableQuantity === 0) return 'Out of Stock'
  return `${props.availableQuantity} Available`
})

const availabilityBadgeSeverity = computed(() => {
  if (!props.isAvailable || props.availableQuantity === 0) return 'danger'
  if (props.availableQuantity <= 2) return 'warning'
  return 'success'
})

// Methods
const getCategoryName = (categoryId: string) => {
  const category = itemsStore.categories.find(cat => cat.id === categoryId)
  return category?.name || 'Unknown Category'
}

const getItemStatus = (item: any) => {
  if (item.suspended) return 'Suspended'
  
  // Check if item has any active episodes
  const hasActiveEpisodes = Object.values(item.episodes || {}).some((episode: any) => 
    episode.status === 'scheduled' || episode.status === 'rented'
  )
  
  if (hasActiveEpisodes) return 'In Use'
  return 'Available'
}

const getItemStatusSeverity = (item: any) => {
  const status = getItemStatus(item)
  switch (status) {
    case 'Suspended': return 'danger'
    case 'In Use': return 'warning'
    case 'Available': return 'success'
    default: return 'info'
  }
}

const onImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/placeholder-item.jpg'
}

const addToCart = async () => {
  if (!props.isAvailable || selectedQuantity.value === 0) return
  
  adding.value = true
  
  try {
    const cartItem: CartItem = {
      item_group_id: props.itemGroup.id,
      item_group_name: props.itemGroup.name,
      item_group_type: props.itemGroup.type,
      quantity: selectedQuantity.value,
      max_quantity: props.availableQuantity,
      contraindications: props.itemGroup.contraindications || []
    }
    
    emit('add-to-cart', cartItem)
    
    // Reset quantity after adding
    selectedQuantity.value = 1
  } finally {
    adding.value = false
  }
}
</script>

<style scoped>
.item-card {
  height: 100%;
  transition: transform 0.2s, box-shadow 0.2s;
}

.item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.item-unavailable {
  opacity: 0.7;
}

.item-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.availability-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

.item-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
}

.info-btn {
  padding: 0.25rem;
  width: 1.5rem;
  height: 1.5rem;
  min-width: unset;
}

.item-category {
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.item-description {
  color: var(--text-color-secondary);
  margin-bottom: 1rem;
  line-height: 1.4;
}

.item-stats {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
}

.stat-label {
  color: var(--text-color-secondary);
}

.stat-value {
  font-weight: 600;
  color: var(--text-color);
}

.contraindications-warning {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: var(--yellow-50);
  border: 1px solid var(--yellow-200);
  border-radius: 4px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: var(--yellow-800);
}

.item-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quantity-selector label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-color);
}

.add-to-cart-btn {
  width: 100%;
}

.item-details-content {
  display: flex;
  gap: 1.5rem;
}

.detail-image {
  flex: 0 0 200px;
}

.detail-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 4px;
}

.detail-info {
  flex: 1;
}

.detail-info h4 {
  margin: 1rem 0 0.5rem 0;
  color: var(--text-color);
  font-size: 1rem;
}

.detail-info h4:first-child {
  margin-top: 0;
}

.detail-info p {
  margin: 0 0 1rem 0;
  color: var(--text-color-secondary);
  line-height: 1.4;
}

.items-table {
  margin-bottom: 1rem;
}

.contraindications-list {
  margin: 0;
  padding-left: 1.5rem;
}

.contraindications-list li {
  margin-bottom: 0.5rem;
  color: var(--text-color-secondary);
}

.contraindications-content p {
  margin-bottom: 1rem;
  color: var(--text-color-secondary);
}

@media (max-width: 768px) {
  .item-details-dialog {
    width: 95vw !important;
    min-width: unset !important;
  }
  
  .item-details-content {
    flex-direction: column;
  }
  
  .detail-image {
    flex: none;
  }
}
</style>
