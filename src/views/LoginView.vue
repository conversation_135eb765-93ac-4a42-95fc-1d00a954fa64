<template>
  <div class="login-container">
    <!-- Header Bar -->
    <div class="header-bar">
      <div class="header-content">
        <div class="logo-section">
          <div class="logo">
            <i class="pi pi-building"></i>
          </div>
          <div class="brand-text">
            <h1>TKOH NSD Renting Platform</h1>
            <p>Equipment Management System</p>
          </div>
        </div>
        <div class="header-info">
          <span class="version">v1.0.0</span>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
      <!-- Background Elements -->
      <div class="background-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
      </div>

      <!-- Central Login Card -->
      <div class="login-wrapper">
        <div class="login-card">
          <!-- Card Header -->
          <div class="card-header">
            <div class="header-icon">
              <i class="pi pi-user-circle"></i>
            </div>
            <h2>Employee Login</h2>
            <p>Access your account to manage equipment rentals</p>
          </div>

          <!-- Login Form -->
          <div class="card-body">
            <form @submit.prevent="handleLogin" class="login-form">
              <div class="form-group">
                <label for="employeeNumber">Employee Number</label>
                <div class="input-container">
                  <i class="pi pi-id-card input-icon"></i>
                  <InputText
                    id="employeeNumber"
                    v-model="employeeNumber"
                    placeholder="Enter your employee number"
                    class="form-input"
                    @keyup.enter="handleLogin"
                  />
                </div>
              </div>

              <div class="form-group" v-if="!isDemoMode">
                <label for="password">Password</label>
                <div class="input-container">
                  <i class="pi pi-lock input-icon"></i>
                  <Password
                    id="password"
                    v-model="password"
                    placeholder="Enter your password"
                    class="form-input"
                    :feedback="false"
                    toggleMask
                    @keyup.enter="handleLogin"
                  />
                </div>
              </div>

              <Button
                type="submit"
                label="Login to System"
                :loading="isLoading"
                class="login-btn"
                icon="pi pi-sign-in"
                iconPos="right"
              />
            </form>

            <!-- Demo Mode Notice -->
            <div v-if="isDemoMode" class="demo-panel">
              <div class="demo-header">
                <i class="pi pi-info-circle"></i>
                <span>Demo Mode Active</span>
              </div>
              <div class="demo-content">
                <p>This is a demonstration version of the system.</p>
                <div class="demo-instructions">
                  <div class="instruction-item">
                    <i class="pi pi-arrow-right"></i>
                    <span>Use any employee number to login</span>
                  </div>
                  <div class="instruction-item">
                    <i class="pi pi-arrow-right"></i>
                    <span>Try "admin" for administrator privileges</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Error Message -->
            <div v-if="errorMessage" class="error-panel">
              <i class="pi pi-exclamation-triangle"></i>
              <div class="error-content">
                <strong>Login Failed</strong>
                <p>{{ errorMessage }}</p>
              </div>
            </div>
          </div>

          <!-- Card Footer -->
          <div class="card-footer">
            <div class="footer-links">
              <a href="#" class="footer-link">Help & Support</a>
              <span class="separator">|</span>
              <a href="#" class="footer-link">System Status</a>
            </div>
            <div class="copyright">
              <p>&copy; 2024 TKOH NSD. All rights reserved.</p>
            </div>
          </div>
        </div>

        <!-- Side Information Panel -->
        <div class="info-panel">
          <h3>System Features</h3>
          <div class="feature-list">
            <div class="feature">
              <div class="feature-icon">
                <i class="pi pi-calendar"></i>
              </div>
              <div class="feature-text">
                <h4>Booking Management</h4>
                <p>Schedule and manage equipment rentals with real-time availability</p>
              </div>
            </div>
            <div class="feature">
              <div class="feature-icon">
                <i class="pi pi-cog"></i>
              </div>
              <div class="feature-text">
                <h4>Equipment Tracking</h4>
                <p>Monitor equipment status, maintenance, and usage history</p>
              </div>
            </div>
            <div class="feature">
              <div class="feature-icon">
                <i class="pi pi-chart-bar"></i>
              </div>
              <div class="feature-text">
                <h4>Analytics Dashboard</h4>
                <p>View comprehensive reports and system analytics</p>
              </div>
            </div>
            <div class="feature">
              <div class="feature-icon">
                <i class="pi pi-users"></i>
              </div>
              <div class="feature-text">
                <h4>User Management</h4>
                <p>Manage user accounts, roles, and permissions</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer Bar -->
    <div class="footer-bar">
      <div class="footer-content">
        <div class="footer-left">
          <span>TKOH NSD Equipment Management System</span>
        </div>
        <div class="footer-right">
          <span>Secure Login Portal</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { isDemoMode } from '@/firebase/config'
import InputText from 'primevue/inputtext'
import Password from 'primevue/password'
import Button from 'primevue/button'

const router = useRouter()
const authStore = useAuthStore()

const employeeNumber = ref('')
const password = ref('')
const isLoading = ref(false)
const errorMessage = ref('')

const handleLogin = async () => {
  console.log('Login button clicked')

  if (!employeeNumber.value.trim()) {
    errorMessage.value = 'Please enter your employee number'
    return
  }

  if (!isDemoMode && !password.value.trim()) {
    errorMessage.value = 'Please enter your password'
    return
  }

  try {
    isLoading.value = true
    errorMessage.value = ''

    console.log('Attempting login with:', employeeNumber.value.trim())

    await authStore.loginWithExternalAPI({
      username: employeeNumber.value.trim(),
      password: password.value.trim()
    })

    console.log('Login successful, redirecting...')
    console.log('Auth state:', authStore.isAuthenticated)

    // Redirect to main application
    router.push('/')
  } catch (error) {
    console.error('Login error:', error)
    errorMessage.value = 'Login failed. Please check your credentials and try again.'
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
/* Main Container */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
}

/* Header Bar */
.header-bar {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.brand-text h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.brand-text p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

.header-info .version {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
}

/* Background Shapes */
.background-shapes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 0;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 20s ease-in-out infinite;
}

.shape-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 200px;
  height: 200px;
  top: 60%;
  right: 15%;
  animation-delay: 7s;
}

.shape-3 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 60%;
  animation-delay: 14s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-30px) scale(1.1); }
}

/* Login Wrapper */
.login-wrapper {
  display: grid;
  grid-template-columns: 500px 350px;
  gap: 3rem;
  max-width: 1000px;
  width: 100%;
  position: relative;
  z-index: 1;
}

/* Login Card */
.login-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Card Header */
.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2.5rem 2rem;
  text-align: center;
}

.header-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2.5rem;
}

.card-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.card-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
}

/* Card Body */
.card-body {
  padding: 2.5rem 2rem;
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.95rem;
}

.input-container {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
  z-index: 2;
  font-size: 1.1rem;
}

.form-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem !important;
  border: 2px solid #e1e8ed !important;
  border-radius: 8px !important;
  font-size: 1rem !important;
  transition: all 0.3s ease !important;
  background: #fff !important;
}

.form-input:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  outline: none !important;
}

.login-btn {
  width: 100%;
  padding: 1rem !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  color: white !important;
  transition: all 0.3s ease !important;
  margin-top: 1rem;
}

.login-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3) !important;
}

.login-btn:active {
  transform: translateY(0) !important;
}

/* Demo Panel */
.demo-panel {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  border-radius: 8px;
  margin-top: 1.5rem;
  overflow: hidden;
}

.demo-header {
  background: rgba(0, 0, 0, 0.1);
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-weight: 600;
}

.demo-content {
  padding: 1.5rem;
  color: white;
}

.demo-content p {
  margin: 0 0 1rem 0;
  font-size: 0.95rem;
}

.demo-instructions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.instruction-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

/* Error Panel */
.error-panel {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1.5rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.error-content strong {
  display: block;
  margin-bottom: 0.25rem;
}

.error-content p {
  margin: 0;
  font-size: 0.9rem;
}

/* Card Footer */
.card-footer {
  background: #f8f9fa;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e9ecef;
}

.footer-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.footer-link {
  color: #667eea;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #764ba2;
}

.separator {
  color: #dee2e6;
}

.copyright {
  text-align: center;
}

.copyright p {
  margin: 0;
  color: #6c757d;
  font-size: 0.85rem;
}

/* Info Panel */
.info-panel {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-panel h3 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
  text-align: center;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.feature {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.feature-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.feature-text h4 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
}

.feature-text p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Footer Bar */
.footer-bar {
  background: #2c3e50;
  color: white;
  padding: 1rem 0;
}

.footer-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
}

.footer-right {
  opacity: 0.8;
}

/* Loading State */
.login-btn[aria-busy="true"] {
  position: relative;
  color: transparent !important;
}

.login-btn[aria-busy="true"]::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Responsive adjustments for smaller desktops */
@media (max-width: 1200px) {
  .login-wrapper {
    grid-template-columns: 450px 300px;
    gap: 2rem;
  }

  .header-content,
  .footer-content {
    padding: 0 1rem;
  }
}

@media (max-width: 900px) {
  .login-wrapper {
    grid-template-columns: 1fr;
    max-width: 500px;
  }

  .info-panel {
    order: -1;
  }
}
</style>
