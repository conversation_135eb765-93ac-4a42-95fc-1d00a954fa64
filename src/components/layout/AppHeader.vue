<template>
  <header class="app-header">
    <div class="header-content">
      <!-- Left Section - Brand -->
      <div class="header-brand">
        <div class="brand-logo">
          <i class="pi pi-building"></i>
        </div>
        <div class="brand-text">
          <h1>TKOH NSD Renting Platform</h1>
          <p>Equipment Management System</p>
        </div>
      </div>

      <!-- Center Section - Search -->
      <div class="header-center" v-if="showSearch">
        <div class="search-container">
          <i class="pi pi-search search-icon"></i>
          <InputText
            v-model="searchQuery"
            placeholder="Search equipment, categories, or items..."
            class="search-input"
            @input="onSearch"
          />
          <Button
            icon="pi pi-filter"
            severity="secondary"
            text
            rounded
            class="filter-btn"
            @click="toggleFilters"
            v-tooltip="'Advanced Filters'"
          />
        </div>
      </div>

      <!-- Right Section - Actions -->
      <div class="header-actions">
        <!-- System Status -->
        <div class="system-status">
          <div class="status-indicator" :class="systemStatus"></div>
          <span class="status-text">{{ systemStatusText }}</span>
        </div>

        <!-- Notifications -->
        <div class="notification-container">
          <Button
            icon="pi pi-bell"
            severity="secondary"
            text
            rounded
            @click="toggleNotifications"
            class="notification-btn"
            v-tooltip="'Notifications'"
          />
          <Badge
            v-if="notificationCount > 0"
            :value="notificationCount"
            severity="danger"
            class="notification-badge"
          />
        </div>

        <!-- User Profile -->
        <div class="user-profile">
          <div class="user-avatar">
            <i class="pi pi-user"></i>
          </div>
          <div class="user-info">
            <span class="user-name">{{ userStore.currentUserProfile?.displayName || 'User' }}</span>
            <span class="user-role">{{ userStore.currentUserProfile?.role || 'Employee' }}</span>
          </div>
          <Button
            icon="pi pi-chevron-down"
            severity="secondary"
            text
            rounded
            size="small"
            @click="toggleUserMenu"
            aria-haspopup="true"
            aria-controls="user-menu"
            class="user-menu-btn"
          />
        </div>

        <!-- User menu overlay -->
        <Menu
          ref="userMenu"
          id="user-menu"
          :model="userMenuItems"
          :popup="true"
          class="user-menu"
        />
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useItemsStore } from '@/stores/items'
import { useRentalsStore } from '@/stores/rentals'
import Menu from 'primevue/menu'

const router = useRouter()
const userStore = useAuthStore()
const itemsStore = useItemsStore()
const rentalsStore = useRentalsStore()

const userMenu = ref()
const searchQuery = ref('')
const showNotifications = ref(false)
const showFilters = ref(false)

// Show search only on certain pages
const showSearch = computed(() => {
  const searchPages = ['home', 'items', 'booking']
  return searchPages.includes(router.currentRoute.value.name as string)
})

// System status
const systemStatus = computed(() => {
  // In demo mode or if all services are working
  return 'online'
})

const systemStatusText = computed(() => {
  switch (systemStatus.value) {
    case 'online': return 'System Online'
    case 'warning': return 'Limited Service'
    case 'offline': return 'System Offline'
    default: return 'Unknown Status'
  }
})

// Notification count (pending rentals for admin, user's rental updates for users)
const notificationCount = computed(() => {
  if (userStore.isAdmin) {
    return rentalsStore.pendingRentals.length
  } else {
    // Count user's rentals that have status updates
    return rentalsStore.userRentals.filter((rental: any) =>
      rental.status === 'approved' || rental.status === 'overdue'
    ).length
  }
})

const userMenuItems = computed(() => [
  {
    label: 'Profile',
    icon: 'pi pi-user',
    command: () => router.push('/profile')
  },
  {
    label: 'My Rentals',
    icon: 'pi pi-calendar',
    command: () => router.push('/my-rentals')
  },
  ...(userStore.isAdmin ? [{
    label: 'Admin Panel',
    icon: 'pi pi-cog',
    command: () => router.push('/admin')
  }] : []),
  {
    separator: true
  },
  {
    label: 'Logout',
    icon: 'pi pi-sign-out',
    command: logout
  }
])

const toggleUserMenu = (event: Event) => {
  userMenu.value.toggle(event)
}

const toggleNotifications = () => {
  showNotifications.value = !showNotifications.value
  // Navigate to appropriate notifications page
  if (userStore.isAdmin) {
    router.push('/admin')
  } else {
    router.push('/my-rentals')
  }
}

const toggleFilters = () => {
  showFilters.value = !showFilters.value
  // Could emit event to parent component or show filter panel
}

const onSearch = () => {
  itemsStore.setSearchQuery(searchQuery.value)
}

const logout = async () => {
  try {
    await userStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('Logout failed:', error)
  }
}

onMounted(() => {
  // Sync search query with store
  searchQuery.value = itemsStore.searchQuery
})
</script>

<style scoped>
.app-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 1rem 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 300px 1fr 400px;
  gap: 2rem;
  align-items: center;
}

/* Brand Section */
.header-brand {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.brand-logo {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  backdrop-filter: blur(10px);
}

.brand-text h1 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.2;
}

.brand-text p {
  margin: 0;
  font-size: 0.8rem;
  opacity: 0.8;
  line-height: 1;
}

/* Search Section */
.header-center {
  display: flex;
  justify-content: center;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 0.5rem 1rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 500px;
}

.search-icon {
  color: rgba(255, 255, 255, 0.7);
  margin-right: 0.75rem;
  font-size: 1rem;
}

.search-input {
  flex: 1;
  background: transparent !important;
  border: none !important;
  color: white !important;
  font-size: 0.95rem !important;
  padding: 0.5rem 0 !important;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

.search-input:focus {
  outline: none !important;
  box-shadow: none !important;
}

.filter-btn {
  margin-left: 0.5rem;
  color: rgba(255, 255, 255, 0.8) !important;
}

.filter-btn:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
}

/* Actions Section */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  justify-content: flex-end;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator.online {
  background: #2ecc71;
}

.status-indicator.warning {
  background: #f39c12;
}

.status-indicator.offline {
  background: #e74c3c;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  opacity: 0.9;
  font-weight: 500;
}

.notification-container {
  position: relative;
}

.notification-btn {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 1.1rem !important;
}

.notification-btn:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  min-width: 18px;
  height: 18px;
  font-size: 0.7rem;
  z-index: 1;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-profile:hover {
  background: rgba(255, 255, 255, 0.15);
}

.user-avatar {
  width: 35px;
  height: 35px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 0.9rem;
  font-weight: 600;
  line-height: 1.2;
}

.user-role {
  font-size: 0.75rem;
  opacity: 0.8;
  line-height: 1;
  text-transform: capitalize;
}

.user-menu-btn {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 0.8rem !important;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .header-content {
    grid-template-columns: 250px 1fr 350px;
    gap: 1.5rem;
    padding: 0 1.5rem;
  }

  .brand-text h1 {
    font-size: 1.1rem;
  }
}

@media (max-width: 1024px) {
  .header-content {
    grid-template-columns: 200px 1fr 300px;
    gap: 1rem;
    padding: 0 1rem;
  }

  .system-status {
    display: none;
  }
}

@media (max-width: 768px) {
  .header-content {
    grid-template-columns: auto 1fr auto;
    gap: 1rem;
  }

  .brand-text p {
    display: none;
  }

  .brand-text h1 {
    font-size: 1rem;
  }

  .search-container {
    max-width: 300px;
  }

  .user-info {
    display: none;
  }
}

@media (max-width: 480px) {
  .header-center {
    display: none;
  }

  .header-content {
    grid-template-columns: 1fr auto;
  }
}
</style>
