<template>
  <div class="booking-container">
    <!-- Header with Calendar -->
    <div class="calendar-section">
      <h2 class="section-title">Select Rental Dates</h2>
      <DateSelector 
        v-model="selectedDates" 
        @update:modelValue="onDatesChanged"
        :disabled="loading"
      />
    </div>

    <!-- Main Content Area -->
    <div class="main-content" v-if="selectedDates.length > 0">
      <!-- Items Region (2/3 width) -->
      <div class="items-region">
        <div class="items-header">
          <h3>Available Items</h3>
          <div class="item-type-tabs">
            <Button
              v-for="tab in itemTabs"
              :key="tab.value"
              :label="tab.label"
              :class="{ 'p-button-outlined': selectedType !== tab.value }"
              @click="setSelectedType(tab.value)"
              :disabled="loading"
            />
          </div>
        </div>

        <div class="items-content">
          <!-- Category Sidebar -->
          <div class="category-sidebar">
            <h4>Categories</h4>
            <div class="category-list">
              <Button
                label="All Categories"
                :class="{ 'p-button-outlined': selectedCategory !== '' }"
                @click="setSelectedCategory('')"
                class="category-button"
                size="small"
              />
              <Button
                v-for="category in categoriesByType"
                :key="category.id"
                :label="category.name"
                :class="{ 'p-button-outlined': selectedCategory !== category.id }"
                @click="setSelectedCategory(category.id)"
                class="category-button"
                size="small"
              />
            </div>
          </div>

          <!-- Items Display -->
          <div class="items-display">
            <div v-if="loading" class="loading-state">
              <ProgressSpinner />
              <p>Loading items...</p>
            </div>
            
            <div v-else-if="filteredItemGroups.length === 0" class="empty-state">
              <i class="pi pi-inbox" style="font-size: 3rem; color: var(--text-color-secondary);"></i>
              <p>No items available for the selected dates and category.</p>
            </div>

            <div v-else class="items-grid">
              <ItemCard
                v-for="itemGroup in filteredItemGroups"
                :key="itemGroup.id"
                :item-group="itemGroup"
                :available-quantity="getAvailableQuantity(itemGroup)"
                :is-available="isItemGroupAvailable(itemGroup)"
                @add-to-cart="handleAddToCart"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Cart Region (1/3 width) -->
      <div class="cart-region">
        <CartPanel 
          :cart-items="cart.map(item => ({
            ...item,
            contraindications: [...item.contraindications]
          }))"
          :selected-dates="selectedDates"
          @update-quantity="handleUpdateQuantity"
          @remove-item="handleRemoveFromCart"
          @submit-cart="handleSubmitCart"
          :loading="submitting"
        />
      </div>
    </div>

    <!-- Empty State when no dates selected -->
    <div v-else class="empty-dates-state">
      <i class="pi pi-calendar" style="font-size: 4rem; color: var(--text-color-secondary);"></i>
      <h3>Select dates to view available items</h3>
      <p>Choose one or more dates from the calendar above to see what items are available for rental.</p>
    </div>

    <!-- Contraindication Dialog -->
    <ContraIndicationDialog
      v-model:visible="showContraDialog"
      :conflicts="contraindictionConflicts"
      :item-name="pendingCartItem?.item_group_name || ''"
      @confirm="handleContraindictionConfirm"
      @cancel="handleContraindictionCancel"
    />

    <!-- Toast for notifications -->
    <Toast />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import { useItemsStore } from '@/stores/items'
import { useEpisodesStore } from '@/stores/episodes'
import { useAuthStore } from '@/stores/auth'
import DateSelector from '@/components/booking/DateSelector.vue'
import ItemCard from '@/components/booking/ItemCard.vue'
import CartPanel from '@/components/booking/CartPanel.vue'
import ContraIndicationDialog from '@/components/booking/ContraIndicationDialog.vue'
import type { CartItem } from '@/stores/episodes'

// Stores
const itemsStore = useItemsStore()
const episodesStore = useEpisodesStore()
const authStore = useAuthStore()
const toast = useToast()

// Reactive state
const selectedDates = ref<string[]>([])
const loading = ref(false)
const submitting = ref(false)
const showContraDialog = ref(false)
const contraindictionConflicts = ref<Array<{item_group_name: string, reason: string}>>([])
const pendingCartItem = ref<CartItem | null>(null)

// Item type tabs
const itemTabs = [
  { label: 'Resuscitating Training', value: 'resus_trainings' as const },
  { label: 'Venue', value: 'venues' as const },
  { label: 'Audio Visuals', value: 'audio_visuals' as const }
]

// Computed properties
const selectedType = computed(() => itemsStore.selectedType)
const selectedCategory = computed(() => itemsStore.selectedCategory)
const categoriesByType = computed(() => itemsStore.categoriesByType)
const filteredItemGroups = computed(() => itemsStore.filteredItemGroups)
const cart = computed(() => episodesStore.cart)

// Methods
const setSelectedType = (type: 'resus_trainings' | 'venues' | 'audio_visuals') => {
  itemsStore.setSelectedType(type)
}

const setSelectedCategory = (categoryId: string) => {
  itemsStore.setSelectedCategory(categoryId)
}

const getAvailableQuantity = (itemGroup: any) => {
  return itemsStore.getAvailableQuantity(itemGroup)
}

const isItemGroupAvailable = (itemGroup: any) => {
  return itemsStore.isItemGroupAvailable(itemGroup)
}

const onDatesChanged = (dates: string[]) => {
  selectedDates.value = dates
  itemsStore.setSelectedDates(dates)
  episodesStore.setSelectedDates(dates)
}

const handleAddToCart = (cartItem: CartItem) => {
  const success = episodesStore.addToCart(cartItem)
  
  if (!success) {
    // Check contraindications
    const conflicts = episodesStore.checkContraindications(cartItem)
    if (conflicts.length > 0) {
      contraindictionConflicts.value = conflicts
      pendingCartItem.value = cartItem
      showContraDialog.value = true
      return
    }
  }
  
  toast.add({
    severity: 'success',
    summary: 'Added to Cart',
    detail: `${cartItem.item_group_name} added to cart`,
    life: 3000
  })
}

const handleContraindictionConfirm = () => {
  if (pendingCartItem.value) {
    // Force add to cart despite contraindications
    const existingIndex = cart.value.findIndex(
      item => item.item_group_id === pendingCartItem.value!.item_group_id
    )
    
    if (existingIndex >= 0) {
      episodesStore.updateCartItemQuantity(
        pendingCartItem.value.item_group_id,
        cart.value[existingIndex].quantity + pendingCartItem.value.quantity
      )
    } else {
      episodesStore.addToCart(pendingCartItem.value)
    }
    
    toast.add({
      severity: 'warn',
      summary: 'Added with Warning',
      detail: `${pendingCartItem.value.item_group_name} added despite contraindications`,
      life: 5000
    })
  }
  
  showContraDialog.value = false
  pendingCartItem.value = null
  contraindictionConflicts.value = []
}

const handleContraindictionCancel = () => {
  showContraDialog.value = false
  pendingCartItem.value = null
  contraindictionConflicts.value = []
}

const handleUpdateQuantity = (itemGroupId: string, quantity: number) => {
  episodesStore.updateCartItemQuantity(itemGroupId, quantity)
}

const handleRemoveFromCart = (itemGroupId: string) => {
  episodesStore.removeFromCart(itemGroupId)
  toast.add({
    severity: 'info',
    summary: 'Removed from Cart',
    detail: 'Item removed from cart',
    life: 3000
  })
}

const handleSubmitCart = async () => {
  if (!authStore.currentUserProfile?.employee_number) {
    toast.add({
      severity: 'error',
      summary: 'Authentication Error',
      detail: 'Please log in to submit booking',
      life: 5000
    })
    return
  }

  submitting.value = true
  
  try {
    const episodeId = await episodesStore.submitCart(authStore.currentUserProfile.employee_number)
    
    toast.add({
      severity: 'success',
      summary: 'Booking Submitted',
      detail: `Your booking has been submitted successfully. Episode ID: ${episodeId}`,
      life: 5000
    })
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Booking Failed',
      detail: error instanceof Error ? error.message : 'Failed to submit booking',
      life: 5000
    })
  } finally {
    submitting.value = false
  }
}

// Lifecycle
onMounted(async () => {
  loading.value = true
  
  try {
    // Initialize real-time listeners
    itemsStore.initializeRealTimeListeners()
    
    if (authStore.currentUserProfile?.employee_number) {
      episodesStore.initializeUserEpisodesListener(authStore.currentUserProfile.employee_number)
    }
    
    // Fetch initial data
    await Promise.all([
      itemsStore.fetchItemGroups(),
      itemsStore.fetchCategories()
    ])
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Loading Error',
      detail: 'Failed to load booking data',
      life: 5000
    })
  } finally {
    loading.value = false
  }
})

onUnmounted(() => {
  itemsStore.stopRealTimeListeners()
  episodesStore.stopUserEpisodesListener()
})
</script>

<style scoped>
.booking-container {
  padding: 1rem;
  max-width: 1400px;
  margin: 0 auto;
}

.calendar-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--surface-card);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-weight: 600;
}

.main-content {
  display: flex;
  gap: 1.5rem;
  min-height: 600px;
}

.items-region {
  flex: 2;
  background: var(--surface-card);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.items-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--surface-border);
  background: var(--surface-ground);
}

.items-header h3 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
}

.item-type-tabs {
  display: flex;
  gap: 0.5rem;
}

.items-content {
  display: flex;
  height: calc(100% - 120px);
}

.category-sidebar {
  width: 200px;
  padding: 1rem;
  border-right: 1px solid var(--surface-border);
  background: var(--surface-50);
}

.category-sidebar h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-size: 0.9rem;
  font-weight: 600;
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.category-button {
  justify-content: flex-start;
  text-align: left;
}

.items-display {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: var(--text-color-secondary);
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.cart-region {
  flex: 1;
  background: var(--surface-card);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.empty-dates-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: var(--text-color-secondary);
  text-align: center;
}

.empty-dates-state h3 {
  margin: 1rem 0 0.5rem 0;
  color: var(--text-color);
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }
  
  .items-content {
    flex-direction: column;
    height: auto;
  }
  
  .category-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--surface-border);
  }
  
  .category-list {
    flex-direction: row;
    flex-wrap: wrap;
  }
}
</style>
