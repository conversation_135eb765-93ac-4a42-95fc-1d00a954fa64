@import './base.css';

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  font-weight: normal;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }

  #app {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0 2rem;
  }
}

/* Global PrimeVue Dropdown and Overlay Fixes */
.p-dropdown-panel,
.p-multiselect-panel,
.p-calendar-panel,
.p-overlay-panel,
.p-menu,
.p-contextmenu,
.p-tooltip,
.p-autocomplete-panel {
  background: white !important;
  background-color: white !important;
  border: 1px solid #e0e0e0 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border-radius: 6px !important;
}

.p-dropdown-items,
.p-multiselect-items,
.p-calendar-panel .p-datepicker,
.p-autocomplete-items {
  background: white !important;
  background-color: white !important;
}

.p-dropdown-item,
.p-multiselect-item,
.p-calendar-panel .p-datepicker-calendar td,
.p-autocomplete-item {
  background: white !important;
  background-color: white !important;
  color: #333 !important;
}

.p-dropdown-item:hover,
.p-multiselect-item:hover,
.p-calendar-panel .p-datepicker-calendar td:hover,
.p-autocomplete-item:hover {
  background: #f8f9fa !important;
  background-color: #f8f9fa !important;
  color: #333 !important;
}

.p-dropdown-item.p-highlight,
.p-multiselect-item.p-highlight,
.p-calendar-panel .p-datepicker-calendar td.p-highlight,
.p-autocomplete-item.p-highlight {
  background: #667eea !important;
  background-color: #667eea !important;
  color: white !important;
}
