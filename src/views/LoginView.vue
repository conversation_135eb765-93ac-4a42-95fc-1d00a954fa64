<template>
  <div class="login-container">
    <!-- Background Pattern -->
    <div class="background-pattern"></div>

    <!-- Main Content -->
    <div class="login-content">
      <!-- Left Side - Branding -->
      <div class="branding-section">
        <div class="logo-container">
          <div class="logo-icon">
            <i class="pi pi-building text-6xl"></i>
          </div>
          <h1 class="brand-title">TKOH NSD</h1>
          <h2 class="brand-subtitle">Renting Platform</h2>
        </div>

        <div class="features-list">
          <div class="feature-item">
            <i class="pi pi-check-circle"></i>
            <span>Equipment Management</span>
          </div>
          <div class="feature-item">
            <i class="pi pi-check-circle"></i>
            <span>Real-time Availability</span>
          </div>
          <div class="feature-item">
            <i class="pi pi-check-circle"></i>
            <span>Booking System</span>
          </div>
          <div class="feature-item">
            <i class="pi pi-check-circle"></i>
            <span>Admin Dashboard</span>
          </div>
        </div>
      </div>

      <!-- Right Side - Login Form -->
      <div class="login-section">
        <div class="login-card">
          <div class="login-header">
            <h3>Welcome Back</h3>
            <p>Sign in to access your account</p>
          </div>

          <form @submit.prevent="handleLogin" class="login-form">
            <div class="input-group">
              <label for="employeeNumber">Employee Number</label>
              <div class="input-wrapper">
                <i class="pi pi-user input-icon"></i>
                <InputText
                  id="employeeNumber"
                  v-model="employeeNumber"
                  placeholder="Enter your employee number"
                  class="styled-input"
                  @keyup.enter="handleLogin"
                />
              </div>
            </div>

            <div class="input-group" v-if="!isDemoMode">
              <label for="password">Password</label>
              <div class="input-wrapper">
                <i class="pi pi-lock input-icon"></i>
                <Password
                  id="password"
                  v-model="password"
                  placeholder="Enter your password"
                  class="styled-input"
                  :feedback="false"
                  toggleMask
                  @keyup.enter="handleLogin"
                />
              </div>
            </div>

            <Button
              type="submit"
              label="Sign In"
              :loading="isLoading"
              class="login-button"
              icon="pi pi-sign-in"
            />
          </form>

          <!-- Demo Mode Notice -->
          <div v-if="isDemoMode" class="demo-notice">
            <div class="demo-badge">
              <i class="pi pi-info-circle"></i>
              <span>Demo Mode</span>
            </div>
            <p>Use any employee number to login</p>
            <p class="demo-hint">Try "admin" for administrator access</p>
          </div>

          <!-- Error Message -->
          <div v-if="errorMessage" class="error-message">
            <i class="pi pi-exclamation-triangle"></i>
            <span>{{ errorMessage }}</span>
          </div>

          <!-- Footer -->
          <div class="login-footer">
            <p>&copy; 2024 TKOH NSD. All rights reserved.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { isDemoMode } from '@/firebase/config'
import InputText from 'primevue/inputtext'
import Password from 'primevue/password'
import Button from 'primevue/button'

const router = useRouter()
const authStore = useAuthStore()

const employeeNumber = ref('')
const password = ref('')
const isLoading = ref(false)
const errorMessage = ref('')

const handleLogin = async () => {
  console.log('Login button clicked')

  if (!employeeNumber.value.trim()) {
    errorMessage.value = 'Please enter your employee number'
    return
  }

  if (!isDemoMode && !password.value.trim()) {
    errorMessage.value = 'Please enter your password'
    return
  }

  try {
    isLoading.value = true
    errorMessage.value = ''

    console.log('Attempting login with:', employeeNumber.value.trim())

    await authStore.loginWithExternalAPI({
      username: employeeNumber.value.trim(),
      password: password.value.trim()
    })

    console.log('Login successful, redirecting...')
    console.log('Auth state:', authStore.isAuthenticated)

    // Redirect to main application
    router.push('/')
  } catch (error) {
    console.error('Login error:', error)
    errorMessage.value = 'Login failed. Please check your credentials and try again.'
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.login-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  background-size: 100px 100px;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.login-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  min-height: 600px;
}

/* Branding Section */
.branding-section {
  background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
  color: white;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: relative;
}

.branding-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.logo-container {
  position: relative;
  z-index: 1;
  margin-bottom: 2rem;
}

.logo-icon {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.brand-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.02em;
}

.brand-subtitle {
  font-size: 1.25rem;
  font-weight: 300;
  margin: 0.5rem 0 0 0;
  opacity: 0.9;
}

.features-list {
  position: relative;
  z-index: 1;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.feature-item i {
  margin-right: 0.75rem;
  color: #2ecc71;
  font-size: 1.2rem;
}

/* Login Section */
.login-section {
  padding: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-card {
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h3 {
  font-size: 2rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.login-header p {
  color: #7f8c8d;
  font-size: 1rem;
  margin: 0;
}

.login-form {
  margin-bottom: 2rem;
}

.input-group {
  margin-bottom: 1.5rem;
}

.input-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.input-wrapper {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
  z-index: 1;
}

.styled-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem !important;
  border: 2px solid #ecf0f1 !important;
  border-radius: 12px !important;
  font-size: 1rem !important;
  transition: all 0.3s ease !important;
  background: #fff !important;
}

.styled-input:focus {
  border-color: #3498db !important;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1) !important;
  outline: none !important;
}

.login-button {
  width: 100%;
  padding: 1rem !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  border-radius: 12px !important;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
  border: none !important;
  color: white !important;
  transition: all 0.3s ease !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.login-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3) !important;
}

.login-button:active {
  transform: translateY(0) !important;
}

/* Demo Notice */
.demo-notice {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  text-align: center;
}

.demo-badge {
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  margin-bottom: 1rem;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.demo-badge i {
  margin-right: 0.5rem;
}

.demo-notice p {
  margin: 0.5rem 0;
  font-size: 0.95rem;
}

.demo-hint {
  font-size: 0.85rem !important;
  opacity: 0.9;
  font-style: italic;
}

/* Error Message */
.error-message {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  padding: 1rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  font-weight: 500;
}

.error-message i {
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

/* Footer */
.login-footer {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid #ecf0f1;
}

.login-footer p {
  color: #95a5a6;
  font-size: 0.85rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-content {
    grid-template-columns: 1fr;
    margin: 1rem;
    min-height: auto;
  }

  .branding-section {
    padding: 2rem;
    min-height: 300px;
  }

  .logo-icon {
    width: 80px;
    height: 80px;
  }

  .brand-title {
    font-size: 2rem;
  }

  .brand-subtitle {
    font-size: 1rem;
  }

  .login-section {
    padding: 2rem;
  }

  .login-header h3 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .login-content {
    margin: 0.5rem;
  }

  .branding-section,
  .login-section {
    padding: 1.5rem;
  }

  .logo-icon {
    width: 60px;
    height: 60px;
  }

  .brand-title {
    font-size: 1.5rem;
  }

  .feature-item {
    font-size: 1rem;
  }
}

/* Loading State */
.login-button[aria-busy="true"] {
  position: relative;
  color: transparent !important;
}

.login-button[aria-busy="true"]::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Focus States */
.styled-input:focus-visible {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

.login-button:focus-visible {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}
</style>
