import { initializeDatabase } from '@/firebase/initializeDatabase'

export class AdminUtils {
  static async initializeDatabase() {
    try {
      await initializeDatabase()
      return { success: true, message: 'Database initialized successfully!' }
    } catch (error) {
      console.error('Failed to initialize database:', error)
      return { 
        success: false, 
        message: `Failed to initialize database: ${error instanceof Error ? error.message : 'Unknown error'}` 
      }
    }
  }
}
