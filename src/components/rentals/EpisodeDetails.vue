<template>
  <div class="episode-details">
    <!-- Episode Header -->
    <div class="details-header">
      <div class="episode-info">
        <h2>{{ episode.id }}</h2>
        <Badge 
          :value="statusLabel" 
          :severity="statusSeverity"
          class="status-badge"
        />
      </div>
      <div class="episode-meta">
        <div class="meta-item">
          <span class="meta-label">Created:</span>
          <span class="meta-value">{{ formatDateTime(episode.created_at) }}</span>
        </div>
        <div class="meta-item">
          <span class="meta-label">Last Updated:</span>
          <span class="meta-value">{{ formatDateTime(episode.updated_at) }}</span>
        </div>
      </div>
    </div>

    <!-- Rental Dates -->
    <div class="section">
      <h3>
        <i class="pi pi-calendar"></i>
        Rental Dates
      </h3>
      <div class="dates-grid">
        <Tag
          v-for="date in sortedDates"
          :key="date"
          :value="formatDate(date)"
          severity="info"
          class="date-tag"
        />
      </div>
      <div class="date-summary">
        <strong>Duration:</strong> {{ episode.dates.length }} day{{ episode.dates.length === 1 ? '' : 's' }}
        <span class="date-range">({{ formatDateRange(episode.dates) }})</span>
      </div>
    </div>

    <!-- Rented Items -->
    <div class="section">
      <h3>
        <i class="pi pi-box"></i>
        Rented Items ({{ episode.items.length }})
      </h3>
      <DataTable 
        :value="episode.items" 
        class="items-table"
        :paginator="episode.items.length > 10"
        :rows="10"
        responsive-layout="scroll"
      >
        <Column field="item_group_name" header="Item Group" sortable>
          <template #body="{ data }">
            <div class="item-group-cell">
              <strong>{{ data.item_group_name }}</strong>
              <small class="item-type">{{ formatItemType(data.item_group_type) }}</small>
            </div>
          </template>
        </Column>
        <Column field="individual_item_id" header="Item ID" sortable>
          <template #body="{ data }">
            <code class="item-id">{{ data.individual_item_id }}</code>
          </template>
        </Column>
        <Column field="quantity" header="Quantity" sortable>
          <template #body="{ data }">
            <Badge :value="data.quantity" severity="info" />
          </template>
        </Column>
        <Column header="Status">
          <template #body="{ data }">
            <Badge 
              :value="getItemStatus(data)" 
              :severity="getItemStatusSeverity(data)"
            />
          </template>
        </Column>
      </DataTable>
    </div>

    <!-- Status Timeline -->
    <div class="section">
      <h3>
        <i class="pi pi-history"></i>
        Status Timeline
      </h3>
      <div class="timeline">
        <div class="timeline-item completed">
          <div class="timeline-marker">
            <i class="pi pi-plus"></i>
          </div>
          <div class="timeline-content">
            <h4>Episode Created</h4>
            <p>{{ formatDateTime(episode.created_at) }}</p>
          </div>
        </div>

        <div 
          class="timeline-item"
          :class="{ 
            'completed': episode.status !== 'cancelled',
            'active': episode.status === 'scheduled' && !isCancelled
          }"
        >
          <div class="timeline-marker">
            <i class="pi pi-clock"></i>
          </div>
          <div class="timeline-content">
            <h4>Scheduled</h4>
            <p v-if="episode.status === 'scheduled'">
              Rental starts {{ getStartDateText(episode.dates) }}
            </p>
            <p v-else>Episode was scheduled</p>
          </div>
        </div>

        <div 
          class="timeline-item"
          :class="{ 
            'completed': episode.status === 'rented' || episode.status === 'returned',
            'active': episode.status === 'rented'
          }"
        >
          <div class="timeline-marker">
            <i class="pi pi-check"></i>
          </div>
          <div class="timeline-content">
            <h4>Rented</h4>
            <p v-if="episode.status === 'rented'">
              Currently rented • Returns {{ getEndDateText(episode.dates) }}
            </p>
            <p v-else-if="episode.status === 'returned'">
              Items were rented
            </p>
            <p v-else>Items will be marked as rented automatically</p>
          </div>
        </div>

        <div 
          class="timeline-item"
          :class="{ 
            'completed': episode.status === 'returned',
            'active': false
          }"
        >
          <div class="timeline-marker">
            <i class="pi pi-verified"></i>
          </div>
          <div class="timeline-content">
            <h4>Returned</h4>
            <p v-if="episode.status === 'returned'">
              Items returned on {{ getEndDateText(episode.dates) }}
            </p>
            <p v-else>Items will be marked as returned automatically</p>
          </div>
        </div>

        <!-- Cancelled Timeline Item -->
        <div 
          v-if="episode.status === 'cancelled'"
          class="timeline-item cancelled"
        >
          <div class="timeline-marker">
            <i class="pi pi-times"></i>
          </div>
          <div class="timeline-content">
            <h4>Cancelled</h4>
            <p>Episode cancelled on {{ formatDateTime(episode.updated_at) }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="details-actions">
      <Button
        label="Close"
        icon="pi pi-times"
        class="p-button-outlined"
        @click="$emit('close')"
      />
      
      <Button
        v-if="episode.status === 'scheduled'"
        label="Cancel Episode"
        icon="pi pi-times"
        severity="danger"
        class="p-button-outlined"
        @click="$emit('cancel-episode', episode)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { format, parseISO, isToday, isTomorrow, isYesterday, differenceInDays } from 'date-fns'
import type { UserEpisode } from '@/stores/episodes'

interface Props {
  episode: UserEpisode
}

interface Emits {
  (e: 'cancel-episode', episode: UserEpisode): void
  (e: 'close'): void
}

const props = defineProps<Props>()
defineEmits<Emits>()

// Computed properties
const statusLabel = computed(() => {
  switch (props.episode.status) {
    case 'scheduled':
      return 'Scheduled'
    case 'rented':
      return 'Rented'
    case 'returned':
      return 'Returned'
    case 'cancelled':
      return 'Cancelled'
    default:
      return props.episode.status
  }
})

const statusSeverity = computed(() => {
  switch (props.episode.status) {
    case 'scheduled':
      return 'info'
    case 'rented':
      return 'warning'
    case 'returned':
      return 'success'
    case 'cancelled':
      return 'danger'
    default:
      return 'info'
  }
})

const sortedDates = computed(() => {
  return [...props.episode.dates].sort()
})

const isCancelled = computed(() => {
  return props.episode.status === 'cancelled'
})

// Methods
const formatDateTime = (date: Date) => {
  return format(date, 'MMM dd, yyyy \'at\' h:mm a')
}

const formatDate = (dateStr: string) => {
  return format(parseISO(dateStr), 'EEE, MMM dd, yyyy')
}

const formatDateRange = (dates: string[]) => {
  if (dates.length === 0) return 'No dates'
  if (dates.length === 1) return format(parseISO(dates[0]), 'MMM dd, yyyy')
  
  const sortedDates = [...dates].sort()
  const startDate = parseISO(sortedDates[0])
  const endDate = parseISO(sortedDates[sortedDates.length - 1])
  
  return `${format(startDate, 'MMM dd')} - ${format(endDate, 'MMM dd, yyyy')}`
}

const formatItemType = (type: string) => {
  switch (type) {
    case 'resus_trainings':
      return 'Resuscitating Training'
    case 'venues':
      return 'Venue'
    case 'audio_visuals':
      return 'Audio Visual'
    default:
      return type
  }
}

const getItemStatus = (item: any) => {
  // Based on episode status
  switch (props.episode.status) {
    case 'scheduled':
      return 'Scheduled'
    case 'rented':
      return 'Rented'
    case 'returned':
      return 'Returned'
    case 'cancelled':
      return 'Cancelled'
    default:
      return 'Unknown'
  }
}

const getItemStatusSeverity = (item: any) => {
  switch (props.episode.status) {
    case 'scheduled':
      return 'info'
    case 'rented':
      return 'warning'
    case 'returned':
      return 'success'
    case 'cancelled':
      return 'danger'
    default:
      return 'info'
  }
}

const getStartDateText = (dates: string[]) => {
  if (dates.length === 0) return ''
  
  const startDate = parseISO([...dates].sort()[0])
  
  if (isToday(startDate)) return 'today'
  if (isTomorrow(startDate)) return 'tomorrow'
  
  const daysFromNow = differenceInDays(startDate, new Date())
  if (daysFromNow > 0) {
    return `in ${daysFromNow} day${daysFromNow === 1 ? '' : 's'}`
  }
  
  return format(startDate, 'MMM dd')
}

const getEndDateText = (dates: string[]) => {
  if (dates.length === 0) return ''
  
  const endDate = parseISO([...dates].sort().pop()!)
  
  if (isToday(endDate)) return 'today'
  if (isYesterday(endDate)) return 'yesterday'
  if (isTomorrow(endDate)) return 'tomorrow'
  
  return format(endDate, 'MMM dd')
}
</script>

<style scoped>
.episode-details {
  padding: 1rem 0;
}

.details-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.episode-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.episode-info h2 {
  margin: 0;
  font-family: 'Courier New', monospace;
  color: var(--text-color);
}

.status-badge {
  font-size: 0.9rem;
}

.episode-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.meta-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.meta-label {
  font-size: 0.9rem;
  color: var(--text-color-secondary);
  font-weight: 500;
}

.meta-value {
  color: var(--text-color);
}

.section {
  margin-bottom: 2rem;
}

.section h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-size: 1.2rem;
  font-weight: 600;
}

.dates-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.date-tag {
  font-size: 0.9rem;
}

.date-summary {
  color: var(--text-color-secondary);
  font-size: 0.95rem;
}

.date-range {
  margin-left: 0.5rem;
  font-style: italic;
}

.item-group-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.item-type {
  color: var(--text-color-secondary);
  font-style: italic;
}

.item-id {
  font-family: 'Courier New', monospace;
  background: var(--surface-100);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
}

.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--surface-border);
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
  padding-left: 2rem;
}

.timeline-marker {
  position: absolute;
  left: -2rem;
  top: 0;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: var(--surface-200);
  border: 2px solid var(--surface-border);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color-secondary);
}

.timeline-item.completed .timeline-marker {
  background: var(--green-500);
  border-color: var(--green-500);
  color: white;
}

.timeline-item.active .timeline-marker {
  background: var(--blue-500);
  border-color: var(--blue-500);
  color: white;
  animation: pulse 2s infinite;
}

.timeline-item.cancelled .timeline-marker {
  background: var(--red-500);
  border-color: var(--red-500);
  color: white;
}

.timeline-content h4 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 1rem;
}

.timeline-content p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.details-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid var(--surface-border);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

@media (max-width: 768px) {
  .episode-meta {
    grid-template-columns: 1fr;
  }
  
  .details-actions {
    flex-direction: column;
  }
  
  .details-actions .p-button {
    width: 100%;
  }
  
  .dates-grid {
    gap: 0.25rem;
  }
  
  .date-tag {
    font-size: 0.8rem;
  }
}
</style>
