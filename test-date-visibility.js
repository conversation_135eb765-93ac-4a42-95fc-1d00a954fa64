// Test script to verify date button text visibility
console.log('🧪 Testing Date Button Text Visibility...\n');

// Test 1: Check for date buttons
console.log('1. Checking for date buttons...');
setTimeout(() => {
  const dateButtons = document.querySelectorAll('.date-button');
  console.log(`Found ${dateButtons.length} date buttons`);
  
  if (dateButtons.length > 0) {
    console.log('✅ Date buttons found');
    
    // Test text visibility
    let visibleTextCount = 0;
    dateButtons.forEach((button, index) => {
      const styles = window.getComputedStyle(button);
      const color = styles.color;
      const backgroundColor = styles.backgroundColor;
      
      // Check if text is visible (not white on white)
      if (color !== 'rgb(255, 255, 255)' || backgroundColor !== 'rgb(255, 255, 255)') {
        visibleTextCount++;
      }
      
      if (index < 3) { // Log first 3 buttons for debugging
        console.log(`Button ${index + 1}: color=${color}, bg=${backgroundColor}`);
      }
    });
    
    console.log(`${visibleTextCount}/${dateButtons.length} buttons have visible text`);
    
    if (visibleTextCount === dateButtons.length) {
      console.log('✅ All date button text is visible');
    } else {
      console.log('❌ Some date button text may not be visible');
    }
  } else {
    console.log('❌ No date buttons found');
  }
}, 2000);

// Test 2: Test date selection
console.log('\n2. Testing date selection...');
setTimeout(() => {
  const availableButtons = document.querySelectorAll('.date-button:not(:disabled)');
  
  if (availableButtons.length > 0) {
    console.log(`Found ${availableButtons.length} available date buttons`);
    
    // Simulate clicking the first available button
    const firstButton = availableButtons[0];
    firstButton.click();
    
    setTimeout(() => {
      const selectedButtons = document.querySelectorAll('.date-button.selected');
      console.log(`${selectedButtons.length} buttons are now selected`);
      
      if (selectedButtons.length > 0) {
        const selectedButton = selectedButtons[0];
        const styles = window.getComputedStyle(selectedButton);
        console.log(`Selected button: color=${styles.color}, bg=${styles.backgroundColor}`);
        
        // Check if selected text is visible
        if (styles.color === 'rgb(255, 255, 255)' && styles.backgroundColor !== 'rgb(255, 255, 255)') {
          console.log('✅ Selected date text is visible (white on colored background)');
        } else if (styles.color !== 'rgb(255, 255, 255)') {
          console.log('✅ Selected date text is visible (colored text)');
        } else {
          console.log('❌ Selected date text may not be visible');
        }
      }
    }, 500);
  } else {
    console.log('❌ No available date buttons found');
  }
}, 3000);

// Test 3: Check CSS specificity
console.log('\n3. Testing CSS specificity...');
setTimeout(() => {
  const dateSelector = document.querySelector('.date-selector');
  if (dateSelector) {
    const buttons = dateSelector.querySelectorAll('.p-button.date-button');
    console.log(`Found ${buttons.length} PrimeVue date buttons in DateSelector`);
    
    if (buttons.length > 0) {
      console.log('✅ DateSelector specific styling should be applied');
    }
  }
}, 4000);

console.log('\n🔍 Date visibility test completed. Check the results above.');
