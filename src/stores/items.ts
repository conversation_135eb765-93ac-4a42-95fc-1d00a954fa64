import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  setDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  type Unsubscribe
} from 'firebase/firestore'
import { db, isDemoMode } from '@/firebase/config'

// Individual item within an item group
export interface IndividualItem {
  id: string
  serial_number: string
  condition: 'excellent' | 'good' | 'fair' | 'poor'
  notes?: string
  status: 'available' | 'rented' | 'maintenance' | 'retired'
  remarks?: string
  episodes?: Record<string, {
    dates: string[] // Array of dates in YYYY-MM-DD format
    status: 'scheduled' | 'rented' | 'returned' | 'cancelled'
  }>
  use_count?: number
  suspended?: boolean
}

// Item group (what users see and select)
export interface ItemGroup {
  id: string
  name: string
  description: string
  category_id: string
  type: 'resus_trainings' | 'venues' | 'audio_visuals'
  image_url?: string
  items?: IndividualItem[] // Array of individual items of the same model
  contraindications?: Array<{
    item_group_name: string
    reason: string
  }>
  created_at: Date
  updated_at: Date
  isActive?: boolean
  // Legacy fields for backward compatibility
  imageUrl?: string
  category?: string
  createdAt?: Date
  updatedAt?: Date
}

// Category within each type
export interface ItemCategory {
  id: string
  name: string
  description: string
  type: 'resus_trainings' | 'venues' | 'audio_visuals'
  icon?: string
  color?: string
  order?: number
}

export const useItemsStore = defineStore('items', () => {
  // State
  const itemGroups = ref<ItemGroup[]>([])
  const categories = ref<ItemCategory[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const selectedType = ref<'resus_trainings' | 'venues' | 'audio_visuals'>('resus_trainings')
  const selectedCategory = ref<string>('')
  const selectedDates = ref<string[]>([]) // Array of selected dates in YYYY-MM-DD format
  const searchQuery = ref('')

  // Real-time listeners
  let itemGroupsUnsubscribe: Unsubscribe | null = null
  let categoriesUnsubscribe: Unsubscribe | null = null

  // Getters
  const itemGroupsByType = computed(() => {
    return itemGroups.value.filter(group =>
      (group.isActive !== false) && group.type === selectedType.value
    )
  })

  const categoriesByType = computed(() => {
    return categories.value.filter(category => category.type === selectedType.value)
  })

  const filteredItemGroups = computed(() => {
    let filtered = itemGroupsByType.value

    if (selectedCategory.value) {
      filtered = filtered.filter(group =>
        (group.category_id === selectedCategory.value) ||
        (group.category === selectedCategory.value) // backward compatibility
      )
    }

    return filtered
  })

  const itemGroupsByCategory = computed(() => {
    const grouped: Record<string, ItemGroup[]> = {}
    filteredItemGroups.value.forEach(group => {
      const categoryKey = group.category_id || group.category || 'uncategorized'
      if (!grouped[categoryKey]) {
        grouped[categoryKey] = []
      }
      grouped[group.category].push(group)
    })
    return grouped
  })

  // Get available quantity for an item group on selected dates
  const getAvailableQuantity = (itemGroup: ItemGroup): number => {
    if (!itemGroup.items || itemGroup.items.length === 0) {
      return 0
    }

    if (selectedDates.value.length === 0) {
      // Return total non-suspended items if no dates selected
      return itemGroup.items.filter(item => !item.suspended && item.status === 'available').length
    }

    let availableCount = 0

    itemGroup.items.forEach(item => {
      if (item.suspended || item.status !== 'available') return // Skip suspended or unavailable items

      let isAvailable = true

      // Check if any selected date conflicts with item's episodes
      if (item.episodes) {
        for (const episodeId in item.episodes) {
          const episode = item.episodes[episodeId]
          // Only consider active episodes (not cancelled)
          if (episode.status === 'scheduled' || episode.status === 'rented') {
            // Check if any selected date overlaps with episode dates
            const hasConflict = selectedDates.value.some(selectedDate =>
              episode.dates.includes(selectedDate)
            )
            if (hasConflict) {
              isAvailable = false
              break
            }
          }
        }
      }

      if (isAvailable) {
        availableCount++
      }
    })

    // Debug logging for Manikin availability issues
    if (itemGroup.id === 'manikin' && selectedDates.value.length > 0) {
      console.log('🔍 MANIKIN AVAILABILITY ANALYSIS:', {
        selectedDates: selectedDates.value,
        totalItems: itemGroup.items.length,
        availableCount,
        itemAnalysis: itemGroup.items.map(item => {
          const allEpisodes = item.episodes ? Object.entries(item.episodes) : []
          const activeEpisodes = allEpisodes.filter(([_, ep]: [string, any]) =>
            ep.status === 'scheduled' || ep.status === 'rented'
          )
          const conflictingEpisodes = activeEpisodes.filter(([_, ep]: [string, any]) =>
            selectedDates.value.some(date => ep.dates.includes(date))
          )

          return {
            id: item.id,
            status: item.status,
            suspended: item.suspended,
            isAvailable: !item.suspended && item.status === 'available' && conflictingEpisodes.length === 0,
            totalEpisodes: allEpisodes.length,
            activeEpisodes: activeEpisodes.length,
            conflictingEpisodes: conflictingEpisodes.length,
            allEpisodeDetails: allEpisodes.map(([id, ep]) => ({
              episodeId: id,
              dates: ep.dates,
              status: ep.status,
              isActive: ep.status === 'scheduled' || ep.status === 'rented',
              hasConflict: selectedDates.value.some(date => ep.dates.includes(date))
            })),
            conflictDetails: conflictingEpisodes.map(([id, ep]) => ({ episodeId: id, dates: ep.dates, status: ep.status }))
          }
        })
      })
    }

    return availableCount
  }

  // Get available quantity considering current cart items
  const getAvailableQuantityWithCart = (itemGroup: ItemGroup, cartItems: any[] = []): number => {
    const baseAvailability = getAvailableQuantity(itemGroup)

    // Find if this item group is already in cart
    const cartItem = cartItems.find(item => item.item_group_id === itemGroup.id)
    if (cartItem) {
      // Subtract already selected quantity from available quantity
      const result = Math.max(0, baseAvailability - cartItem.quantity)

      // Debug logging for Manikin cart-aware calculation
      if (itemGroup.id === 'manikin') {
        console.log('🛒 Manikin Cart-Aware Calculation:', {
          itemGroupId: itemGroup.id,
          baseAvailability,
          cartQuantity: cartItem.quantity,
          finalResult: result,
          selectedDates: selectedDates.value
        })
      }

      return result
    }

    // Debug logging for Manikin when no cart items
    if (itemGroup.id === 'manikin' && selectedDates.value.length > 0) {
      console.log('🛒 Manikin No Cart Items:', {
        itemGroupId: itemGroup.id,
        baseAvailability,
        selectedDates: selectedDates.value,
        cartItemsCount: cartItems.length
      })
    }

    return baseAvailability
  }

  // Check if a specific quantity is available for an item group
  const isQuantityAvailable = (itemGroup: ItemGroup, requestedQuantity: number, cartItems: any[] = []): boolean => {
    const availableQuantity = getAvailableQuantityWithCart(itemGroup, cartItems)
    return availableQuantity >= requestedQuantity
  }

  // Get maximum bookable quantity for an item group considering cart
  const getMaxBookableQuantity = (itemGroup: ItemGroup, cartItems: any[] = []): number => {
    return getAvailableQuantityWithCart(itemGroup, cartItems)
  }

  // Check if item group is available (has at least one available item)
  const isItemGroupAvailable = (itemGroup: ItemGroup): boolean => {
    return getAvailableQuantity(itemGroup) > 0
  }

  // Demo mode mock data
  const loadMockData = () => {
    const mockItemGroups: ItemGroup[] = [
      {
        id: 'demo-resus-1',
        name: 'Adult Manikin',
        description: 'High-fidelity adult training manikin for resuscitation training',
        contraindications: [],
        items: [
          { id: 'item-1', episodes: {}, use_count: 0, suspended: false },
          { id: 'item-2', episodes: {}, use_count: 0, suspended: false }
        ],
        category: 'manikins',
        type: 'resus_trainings',
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true
      },
      {
        id: 'demo-venue-1',
        name: 'Training Room A',
        description: 'Large training room with projector and whiteboard',
        contraindications: [],
        items: [
          { id: 'room-a', episodes: {}, use_count: 0, suspended: false }
        ],
        category: 'training_rooms',
        type: 'venues',
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true
      },
      {
        id: 'demo-av-1',
        name: 'Projector',
        description: 'HD projector for presentations',
        contraindications: [],
        items: [
          { id: 'proj-1', episodes: {}, use_count: 0, suspended: false },
          { id: 'proj-2', episodes: {}, use_count: 0, suspended: false }
        ],
        category: 'projectors',
        type: 'audio_visuals',
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true
      }
    ]

    itemGroups.value = mockItemGroups

    // Mock categories
    categories.value = [
      { id: 'manikins', name: 'Manikins', description: 'Training manikins', type: 'resus_trainings' },
      { id: 'training_rooms', name: 'Training Rooms', description: 'Training venues', type: 'venues' },
      { id: 'projectors', name: 'Projectors', description: 'Audio visual equipment', type: 'audio_visuals' }
    ]
  }

  // Actions
  const initializeRealTimeListeners = () => {
    if (isDemoMode) {
      // Demo mode - load mock data
      loadMockData()
      return
    }

    // Listen to the unified item_groups collection
    const itemGroupsQuery = query(
      collection(db!, 'item_groups'),
      orderBy('name')
    )

    itemGroupsUnsubscribe = onSnapshot(itemGroupsQuery, (snapshot) => {
      const newGroups = snapshot.docs.map(doc => {
        const data = doc.data()
        return {
          id: doc.id,
          ...data,
          // Handle both new and legacy date fields
          created_at: data.created_at?.toDate?.() || data.createdAt?.toDate?.() || new Date(),
          updated_at: data.updated_at?.toDate?.() || data.updatedAt?.toDate?.() || new Date(),
          createdAt: data.created_at?.toDate?.() || data.createdAt?.toDate?.() || new Date(),
          updatedAt: data.updated_at?.toDate?.() || data.updatedAt?.toDate?.() || new Date()
        }
      }) as ItemGroup[]

      itemGroups.value = newGroups
    }, (err) => {
      console.error('Error listening to item_groups:', err)
      error.value = 'Failed to load item groups'
    })

    // Listen to categories collection
    const categoriesQuery = query(
      collection(db!, 'categories'),
      orderBy('name')
    )

    categoriesUnsubscribe = onSnapshot(categoriesQuery, (snapshot) => {
      categories.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ItemCategory[]
    }, (err) => {
      console.error('Error listening to categories:', err)
    })
  }

  const stopRealTimeListeners = () => {
    if (itemGroupsUnsubscribe) {
      itemGroupsUnsubscribe()
      itemGroupsUnsubscribe = null
    }
    if (categoriesUnsubscribe) {
      categoriesUnsubscribe()
      categoriesUnsubscribe = null
    }
  }

  const fetchItemGroups = async () => {
    loading.value = true
    error.value = null

    try {
      if (isDemoMode) {
        // In demo mode, just load the mock data
        loadMockData()
        return
      }

      // Fetch from the new unified item_groups collection
      const q = query(
        collection(db!, 'item_groups'),
        orderBy('name')
      )

      const snapshot = await getDocs(q)
      const groups = snapshot.docs.map(doc => {
        const data = doc.data()
        return {
          id: doc.id,
          ...data,
          created_at: data.created_at?.toDate() || new Date(),
          updated_at: data.updated_at?.toDate() || new Date(),
          // Backward compatibility
          createdAt: data.created_at?.toDate() || data.createdAt?.toDate() || new Date(),
          updatedAt: data.updated_at?.toDate() || data.updatedAt?.toDate() || new Date(),
          category: data.category_id || data.category,
          imageUrl: data.image_url || data.imageUrl
        }
      }) as ItemGroup[]

      itemGroups.value = groups
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch item groups'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchItemGroupById = async (type: string, id: string): Promise<ItemGroup | null> => {
    try {
      if (isDemoMode) {
        // In demo mode, find from loaded mock data
        return itemGroups.value.find(group => group.id === id && group.type === type) || null
      }

      const docRef = doc(db!, 'item_groups', id)
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        const data = docSnap.data()
        return {
          id: docSnap.id,
          ...data,
          type: type as 'resus_trainings' | 'venues' | 'audio_visuals',
          created_at: data.created_at?.toDate() || new Date(),
          updated_at: data.updated_at?.toDate() || new Date(),
          // Backward compatibility
          createdAt: data.created_at?.toDate() || data.createdAt?.toDate() || new Date(),
          updatedAt: data.updated_at?.toDate() || data.updatedAt?.toDate() || new Date()
        } as ItemGroup
      }
      return null
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch item group'
      throw err
    }
  }

  const fetchCategories = async () => {
    try {
      if (isDemoMode) {
        // In demo mode, categories are already loaded with mock data
        return
      }

      const snapshot = await getDocs(collection(db!, 'categories'))
      categories.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ItemCategory[]
    } catch (err) {
      console.error('Error fetching categories:', err)
    }
  }

  const setSelectedType = (type: 'resus_trainings' | 'venues' | 'audio_visuals') => {
    selectedType.value = type
    selectedCategory.value = '' // Reset category when type changes
  }

  const setSelectedCategory = (categoryId: string) => {
    selectedCategory.value = categoryId
  }

  const setSelectedDates = (dates: string[]) => {
    selectedDates.value = dates
  }

  const clearFilters = () => {
    selectedCategory.value = ''
    selectedDates.value = []
  }



  const createItemGroup = async (itemGroupData: Omit<ItemGroup, 'id'> & { id: string }) => {
    if (isDemoMode) {
      // Demo mode - add to memory
      itemGroups.value.push(itemGroupData as ItemGroup)
      return
    }

    try {
      // Add to the unified collection
      const docRef = doc(db!, 'item_groups', itemGroupData.id)
      await setDoc(docRef, {
        ...itemGroupData,
        created_at: new Date(),
        updated_at: new Date(),
        createdAt: new Date(), // backward compatibility
        updatedAt: new Date() // backward compatibility
      })
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create item group'
      throw err
    }
  }

  const updateItemGroup = async (itemGroupId: string, updatedData: Partial<ItemGroup>) => {
    if (isDemoMode) {
      // Demo mode - update in memory
      const index = itemGroups.value.findIndex(group => group.id === itemGroupId)
      if (index >= 0) {
        itemGroups.value[index] = { ...itemGroups.value[index], ...updatedData }
      }
      return
    }

    try {
      const itemGroup = itemGroups.value.find(group => group.id === itemGroupId)
      if (!itemGroup) {
        throw new Error('Item group not found')
      }

      // Update in the new unified collection
      const docRef = doc(db!, 'item_groups', itemGroupId)
      await updateDoc(docRef, {
        ...updatedData,
        updated_at: new Date(),
        updatedAt: new Date() // backward compatibility
      })
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update item group'
      throw err
    }
  }

  const updateIndividualItem = async (itemGroupId: string, itemId: string, updatedData: any) => {
    if (isDemoMode) {
      // Demo mode - update in memory
      const groupIndex = itemGroups.value.findIndex(group => group.id === itemGroupId)
      if (groupIndex >= 0) {
        const itemIndex = itemGroups.value[groupIndex].items?.findIndex(item => item.id === itemId)
        if (itemIndex !== undefined && itemIndex >= 0) {
          itemGroups.value[groupIndex].items![itemIndex] = {
            ...itemGroups.value[groupIndex].items![itemIndex],
            ...updatedData
          }
        }
      }
      return
    }

    try {
      const itemGroup = itemGroups.value.find(group => group.id === itemGroupId)
      if (!itemGroup) {
        throw new Error('Item group not found')
      }

      // Update the individual item within the items array of the item group document
      const updatedItems = itemGroup.items?.map(item =>
        item.id === itemId ? { ...item, ...updatedData } : item
      ) || []

      // Update the entire item group document with the modified items array
      const itemGroupDocRef = doc(db!, 'item_groups', itemGroupId)
      await updateDoc(itemGroupDocRef, {
        items: updatedItems,
        updated_at: new Date(),
        updatedAt: new Date() // backward compatibility
      })

      // Update local state
      const groupIndex = itemGroups.value.findIndex(group => group.id === itemGroupId)
      if (groupIndex >= 0) {
        const itemIndex = itemGroups.value[groupIndex].items?.findIndex(item => item.id === itemId)
        if (itemIndex !== undefined && itemIndex >= 0) {
          itemGroups.value[groupIndex].items![itemIndex] = {
            ...itemGroups.value[groupIndex].items![itemIndex],
            ...updatedData
          }
        }
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update individual item'
      throw err
    }
  }

  const clearError = () => {
    error.value = null
  }

  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }

  // Computed properties
  const allItems = computed(() => {
    return itemGroups.value.filter(group => group.isActive)
  })

  const availableItems = computed(() => {
    let filtered = itemGroups.value

    // Filter by search query
    if (searchQuery.value.trim()) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(query) ||
        item.description.toLowerCase().includes(query)
      )
    }

    // Filter by selected type
    if (selectedType.value) {
      filtered = filtered.filter(item => item.type === selectedType.value)
    }

    // Filter by selected category
    if (selectedCategory.value) {
      filtered = filtered.filter(item => item.category === selectedCategory.value)
    }

    return filtered
  })

  // Helper method to get item count by category
  const getItemCountByCategory = (categoryId: string) => {
    if (!categoryId) {
      return filteredItemGroups.value.length
    }
    return itemGroups.value.filter(group =>
      group.isActive &&
      group.category === categoryId &&
      group.type === selectedType.value
    ).length
  }

  return {
    // State
    itemGroups: readonly(itemGroups),
    categories: readonly(categories),
    loading: readonly(loading),
    error: readonly(error),
    selectedType: readonly(selectedType),
    selectedCategory: readonly(selectedCategory),
    selectedDates: readonly(selectedDates),
    searchQuery: readonly(searchQuery),

    // Getters
    itemGroupsByType,
    categoriesByType,
    filteredItemGroups,
    itemGroupsByCategory,
    allItems,
    availableItems,
    getAvailableQuantity,
    getAvailableQuantityWithCart,
    isQuantityAvailable,
    getMaxBookableQuantity,
    isItemGroupAvailable,
    getItemCountByCategory,

    // Actions
    initializeRealTimeListeners,
    stopRealTimeListeners,
    fetchItemGroups,
    fetchItemGroupById,
    fetchCategories,
    createItemGroup,
    updateItemGroup,
    updateIndividualItem,
    setSelectedType,
    setSelectedCategory,
    setSelectedDates,
    setSearchQuery,
    clearFilters,
    clearError
  }
})
